

#####################jiangyh bg##################################
ALTER TABLE `t_t_product_box`
    ADD COLUMN `isOpenBoxBook` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否图鉴兑换' AFTER `limitTimeType`,
    ADD COLUMN `boxBookGifJson` json NULL COMMENT '兑换奖品json' AFTER `isOpenBoxBook`;

CREATE TABLE `t_t_member_box_book` (
                                       `id` bigint(20) NOT NULL,
                                       `chainId` bigint(20) NOT NULL,
                                       `tenantId` bigint(20) DEFAULT NULL,
                                       `memberId` bigint(20) NOT NULL COMMENT '会员id',
                                       `spuId` bigint(20) NOT NULL COMMENT '盲盒spuId',
                                       `spuItemId` bigint(20) NOT NULL COMMENT '盲盒奖品spuItemId',
                                       `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                       `created` datetime DEFAULT NULL COMMENT '创建时间',
                                       `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                       `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                       `dataStatus` bit(1) DEFAULT b'1' COMMENT '删除标识',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_memberId_spuId_spuItemId` (`memberId`,`spuId`,`spuItemId`) USING BTREE,
                                       KEY `idx_chainId` (`chainId`) USING BTREE,
                                       KEY `idx_memberId` (`memberId`) USING BTREE,
                                       KEY `idx_spuId` (`spuId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员盲盒图鉴';

CREATE TABLE `t_t_member_box_book_record` (
                                              `id` bigint(20) NOT NULL,
                                              `chainId` bigint(20) NOT NULL,
                                              `tenantId` bigint(20) DEFAULT NULL,
                                              `memberId` bigint(20) NOT NULL COMMENT '会员id',
                                              `spuId` bigint(20) NOT NULL COMMENT '盲盒spuId',
                                              `boxBookGifJson` json NOT NULL COMMENT '兑换奖品json',
                                              `boxBookNum` int(10) NOT NULL DEFAULT '0' COMMENT '图鉴数(冗余数据)',
                                              `collectNum` int(10) NOT NULL DEFAULT '0' COMMENT '收集数(冗余数据)',
                                              `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                              `created` datetime DEFAULT NULL COMMENT '创建时间',
                                              `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                              `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                              `dataStatus` bit(1) DEFAULT b'1' COMMENT '删除标识',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `uk_memberId_spuId` (`memberId`,`spuId`) USING BTREE,
                                              KEY `idx_chainId` (`chainId`) USING BTREE,
                                              KEY `idx_memberId` (`memberId`) USING BTREE,
                                              KEY `idx_spuId` (`spuId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员盲盒图鉴兑换记录';

INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MRRW', 'S_MRRW_BOX_BOOK', '图鉴兑换', NULL, 0, NOW(), b'1', '商品获得途径');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_MRRW', 'S_MRRW_BOX_BOOK', '图鉴兑换', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_MRRW', 'S_MRRW_BOX_BOOK', 'Bestiary exchange', 'en_US', 0, NOW(), b'1');


INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_AT', 'S_AT_BOX_BOOK', '图鉴兑换', NULL, 0, NOW(), b'1', '活动类型');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_AT', 'S_AT_BOX_BOOK', '图鉴兑换', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_AT', 'S_AT_BOX_BOOK', 'Bestiary exchange', 'en_US', 0, NOW(), b'1');

#####################jiangyh end#################################

#####################zhangao b##############################
CREATE TABLE `t_pl_turntable` (
                                  `id` bigint(20) NOT NULL COMMENT '主键id',
                                  `chainId` bigint(20) NOT NULL COMMENT '总店ID',
                                  `tenantId` bigint(20) DEFAULT NULL COMMENT '店铺ID',
                                  `name` varchar(50) DEFAULT NULL COMMENT '名称',
                                  `background` varchar(255) DEFAULT NULL COMMENT '背景图',
                                  `price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
                                  `isLimit` bit(1) NOT NULL COMMENT '是否限制',
                                  `limitNum` int(10) DEFAULT NULL COMMENT '限制次数',
                                  `status` varchar(50) NOT NULL COMMENT '状态：上架 下架',
                                  `created` datetime DEFAULT NULL COMMENT '创建时间',
                                  `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                  `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                  `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                  `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标示',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_chainId` (`chainId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水转盘表';

CREATE TABLE `t_t_turntable_cell_ref` (
                                          `id` bigint(20) NOT NULL COMMENT '主键id',
                                          `chainId` bigint(20) NOT NULL COMMENT '总店ID',
                                          `tenantId` bigint(20) DEFAULT NULL COMMENT '店铺ID',
                                          `turntableId` bigint(20) NOT NULL COMMENT '转盘id',
                                          `serialNum` int(4) NOT NULL COMMENT '序号',
                                          `name` varchar(50) DEFAULT NULL COMMENT '名称',
                                          `color` varchar(50) DEFAULT NULL COMMENT '颜色',
                                          `winPercent` double(16,6) NOT NULL COMMENT '中奖概率',
                                          `giftType` varchar(50) NOT NULL COMMENT '礼品类型',
                                          `giftJson` json NOT NULL COMMENT '礼品内容json',
                                          `created` datetime DEFAULT NULL COMMENT '创建时间',
                                          `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                          `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                          `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                          `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_turntableId` (`turntableId`),
                                          KEY `idx_chainId` (`chainId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转盘格子关联表';

CREATE TABLE `t_t_turntable_obtain_record` (
                                               `id` bigint(20) NOT NULL COMMENT '主键id',
                                               `chainId` bigint(20) NOT NULL COMMENT '总店ID',
                                               `tenantId` bigint(20) DEFAULT NULL COMMENT '店铺ID',
                                               `memberId` bigint(20) NOT NULL COMMENT '用户id',
                                               `turntableId` bigint(20) DEFAULT NULL COMMENT '转盘id',
                                               `name` varchar(50) DEFAULT NULL COMMENT '转盘名称',
                                               `giftType` varchar(50) DEFAULT NULL COMMENT '礼品类型',
                                               `giftExtJson` varchar(1000) DEFAULT NULL COMMENT '礼品内容json',
                                               `created` datetime DEFAULT NULL COMMENT '创建时间',
                                               `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                               `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                               `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                               `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                               PRIMARY KEY (`id`),
                                               KEY `idx_chainId` (`chainId`),
                                               KEY `idx_turntableId` (`turntableId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转盘获奖记录表';

CREATE TABLE `t_t_turntable_flow` (
                                      `id` bigint(20) NOT NULL COMMENT '主键id',
                                      `chainId` bigint(20) NOT NULL COMMENT '总店ID',
                                      `tenantId` bigint(20) DEFAULT NULL COMMENT '店铺ID',
                                      `memberId` bigint(20) NOT NULL COMMENT '会员ID',
                                      `tradeMoney` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '交易金额',
                                      `created` datetime DEFAULT NULL COMMENT '创建时间',
                                      `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                      `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                      `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                      `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `unique_chainId_memberId` (`chainId`,`memberId`) USING BTREE,
                                      KEY `idx_chainId` (`chainId`),
                                      KEY `idx_memberId` (`memberId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转盘流水表';

CREATE TABLE `t_t_turntable_flow_record` (
                                             `id` bigint(20) NOT NULL COMMENT '主键id',
                                             `chainId` bigint(20) NOT NULL COMMENT '总店ID',
                                             `tenantId` bigint(20) DEFAULT NULL COMMENT '店铺ID',
                                             `memberId` bigint(20) NOT NULL COMMENT '会员ID',
                                             `type` varchar(50) NOT NULL COMMENT '类型：income、consume',
                                             `tradeMoney` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '流水金额',
                                             `created` datetime DEFAULT NULL COMMENT '创建时间',
                                             `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                             `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转盘流水记录表';

INSERT INTO `t_b_sys_code` ( `typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MRRW', 'S_MRRW_TURNTABLE', '流水转盘', NULL, 0, '2024-10-31 09:40:05', b'1', '商品获得途径');
INSERT INTO `t_b_sys_code` ( `typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MCRW', 'S_MCRW_TURNTABLE', '流水转盘', NULL, 0, '2024-09-26 11:24:39', b'1', '会员优惠券来源');

INSERT INTO `t_b_sys_code_lang` ( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_MRRW', 'S_MRRW_TURNTABLE', '流水转盘', 'zh_CN', 0, '2024-10-31 09:40:05', b'1');
INSERT INTO `t_b_sys_code_lang` ( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_MRRW', 'S_MRRW_TURNTABLE', 'Flowing Wheel', 'en_US', 0, '2024-10-31 09:40:05', b'1');
INSERT INTO `t_b_sys_code_lang` ( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_MCRW', 'S_MCRW_TURNTABLE', '流水转盘', 'zh_CN', 0, '2024-09-26 11:24:39', b'1');
INSERT INTO `t_b_sys_code_lang` ( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_MCRW', 'S_MCRW_TURNTABLE', 'Flowing Wheel', 'en_US', 0, '2024-09-26 11:24:39', b'1');

INSERT INTO `sys_function` (`id`, `funcName`, `funcKey`, `isAtom`, `isOpen`, `moduleId`, `pid`, `type`, `priority`, `remark`, `dataStatus`) VALUES (276, '流水转盘', 'marketing.flow.turntable', b'1', b'1', 0, 173, NULL, 6920, NULL, b'1');
INSERT INTO `sys_version_function`( `versionId`, `funcId`) VALUES ( 1, (SELECT id from sys_function where funcKey = 'marketing.flow.turntable'));
INSERT INTO `t_b_role_function`( `roleId`, `funcId`) VALUES ( 1, (SELECT id from sys_function where funcKey = 'marketing.flow.turntable'));

INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_AT', 'S_AT_TURNTABLE', '流水转盘', NULL, 0, NOW(), b'1', '活动类型');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_AT', 'S_AT_TURNTABLE', '流水转盘', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_AT', 'S_AT_TURNTABLE', 'Flowing Wheel', 'en_US', 0, NOW(), b'1');
#####################zhangao e##############################

#####################zhangdi bg##################################
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_OPM', 'S_OPM_BANK', '银行卡', NULL, 0, NOW(), b'1', '支付方式');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_OPM', 'S_OPM_BANK', '银行卡', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_OPM', 'S_OPM_BANK', '银行卡', 'en_US', 0, NOW(), b'1');

CREATE TABLE `t_t_member_bank` (
                                   `id` bigint(20) NOT NULL COMMENT 'ID',
                                   `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                   `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                   `memberId` bigint(20) NOT NULL COMMENT '会员id',
                                   `bankType` varchar(20) NOT NULL COMMENT '银行卡类型',
                                   `account` varchar(50) NOT NULL COMMENT '站好',
                                   `mobile` varchar(20) NOT NULL COMMENT '预留手机号',
                                   `realName` varchar(50) NOT NULL COMMENT '真实姓名',
                                   `idType` varchar(50) NOT NULL COMMENT '证件类型',
                                   `idNo` varchar(100) NOT NULL COMMENT '证件号',
                                   `isDefault` bit(1) DEFAULT b'0' COMMENT '是否默认',
                                   `created` datetime DEFAULT NULL COMMENT '创建时间',
                                   `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                   `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                   `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                   `dataStatus` bit(1) DEFAULT b'1',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `idx_memberId` (`memberId`) USING BTREE,
                                   KEY `idx_chainId` (`chainId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员银行卡信息';
#####################zhangdi end#################################


#####paas######
#####paas######
