

#####################jiangyh bg##################################
CREATE TABLE `t_t_different_space_member` (
                                              `id` bigint(20) NOT NULL COMMENT '主键id',
                                              `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                              `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                              `differentSpaceId` bigint(20) NOT NULL COMMENT '异空间id',
                                              `memberId` bigint(20) NOT NULL COMMENT '会员Id',
                                              `quantity` int(10) NOT NULL DEFAULT '0' COMMENT '剩余数量',
                                              `giftNum` int(10) NOT NULL DEFAULT '0' COMMENT '获奖数量',
                                              `created` datetime DEFAULT NULL COMMENT '创建时间',
                                              `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                              `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `uk_differentSpaceId_memberId` (`differentSpaceId`,`memberId`) USING BTREE,
                                              KEY `idx_chainId` (`chainId`),
                                              KEY `idx_differentSpaceId` (`differentSpaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异空间会员奖池表';


INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_AT', 'S_AT_DIFFERENT_SPACE', '异空间', NULL, 0, NOW(), b'1', '活动类型');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_AT', 'S_AT_DIFFERENT_SPACE', '异空间', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_AT', 'S_AT_DIFFERENT_SPACE', 'Different space', 'en_US', 0, NOW(), b'1');


INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MRRW', 'S_MRRW_DIFFERENT_SPACE', '异空间', NULL, 0, NOW(), b'1', '商品获得途径');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_MRRW', 'S_MRRW_DIFFERENT_SPACE', '异空间', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_MRRW', 'S_MRRW_DIFFERENT_SPACE', 'Different space', 'en_US', 0, NOW(), b'1');

INSERT INTO `sys_function` ( `funcName`, `funcKey`, `isAtom`, `isOpen`, `moduleId`, `pid`, `type`, `priority`, `remark`, `dataStatus`) VALUES ('异空间', 'marketing.flow.differentSpace', b'1', b'1', 0, 173, NULL, 6943, NULL, b'1');
INSERT INTO `sys_version_function`( `versionId`, `funcId`) VALUES ( 1, (SELECT id from sys_function where funcKey = 'marketing.flow.differentSpace'));
INSERT INTO `t_b_role_function`( `roleId`, `funcId`) VALUES ( 1, (SELECT id from sys_function where funcKey = 'marketing.flow.differentSpace'));
INSERT INTO `sys_version_function`( `versionId`, `funcId`) VALUES ( 5, (SELECT id from sys_function where funcKey = 'marketing.flow.differentSpace'));
INSERT INTO `t_b_role_function`( `roleId`, `funcId`) VALUES ( 5, (SELECT id from sys_function where funcKey = 'marketing.flow.differentSpace'));

#####################jiangyh e##############################

#####################zhangao bg##################################
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_DSS', 'S_DSS_UP', '启用中', NULL, 0, NOW(), b'1', '异空间状态');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_DSS', 'S_DSS_UP', '启用中', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_DSS', 'S_DSS_UP', 'Enabling in progress', 'en_US', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_DSS', 'S_DSS_DOWN', '未启用', NULL, 0, NOW(), b'1', '异空间状态');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_DSS', 'S_DSS_DOWN', '下架', 'zh_CN', 0, NOW(), b'1');
INSERT INTO `t_b_sys_code_lang`(`typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ('S_DSS', 'S_DSS_DOWN', 'not enabled', 'en_US', 0, NOW(), b'1');

CREATE TABLE `t_t_different_space` (
                                       `id` bigint(20) NOT NULL COMMENT '主键id',
                                       `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                       `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                       `title` varchar(100) NOT NULL COMMENT '标题',
                                       `boxSpuId` bigint(20) NOT NULL COMMENT '盲盒的spuId',
                                       `progressNum` int(10) NOT NULL COMMENT '进度数量',
                                       `clearCategoryId` bigint(20) NOT NULL COMMENT '清空类型id',
                                       `status` varchar(50) NOT NULL COMMENT '状态',
                                       `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                       `created` datetime DEFAULT NULL COMMENT '创建时间',
                                       `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                       `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                       `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_chainId` (`chainId`),
                                       KEY `idx_spuId` (`boxSpuId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异空间';

CREATE TABLE `t_t_different_space_reward` (
                                              `id` bigint(20) NOT NULL COMMENT '主键id',
                                              `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                              `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                              `differentSpaceId` bigint(20) NOT NULL COMMENT '异空间id',
                                              `spuId` bigint(20) NOT NULL COMMENT 'spuId',
                                              `skuId` bigint(20) NOT NULL COMMENT 'skuId',
                                              `categoryId` bigint(20) NOT NULL COMMENT '分类id',
                                              `odds` decimal(10,5) NOT NULL COMMENT '中奖概率',
                                              `priority` int(10) DEFAULT NULL COMMENT '排序',
                                              `created` datetime DEFAULT NULL COMMENT '创建时间',
                                              `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                              `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                              `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                              `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标志',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_chainId` (`chainId`),
                                              KEY `idx_differentSpaceId` (`differentSpaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异空间奖品表';

CREATE TABLE `t_t_different_space_reward_record` (
                                                     `id` bigint(20) NOT NULL COMMENT '主键id',
                                                     `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                                     `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                                     `differentSpaceId` bigint(20) NOT NULL COMMENT '异空间id',
                                                     `memberId` bigint(20) NOT NULL COMMENT '会员id',
                                                     `spuId` bigint(20) DEFAULT NULL COMMENT 'spuId',
                                                     `mainImage` varchar(255) DEFAULT NULL COMMENT '奖品图片',
                                                     `rewardName` varchar(255) DEFAULT NULL COMMENT '奖品名称',
                                                     `categoryId` bigint(20) DEFAULT NULL COMMENT 'categoryId',
                                                     `categoryName` varchar(255) DEFAULT NULL COMMENT '分类名称',
                                                     `categoryPic` varchar(255) DEFAULT NULL COMMENT '分类图片',
                                                     `created` datetime DEFAULT NULL COMMENT '创建时间',
                                                     `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                                     `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除状态',
                                                     PRIMARY KEY (`id`),
                                                     KEY `idx_chainId` (`chainId`),
                                                     KEY `idx_differentSpaceId` (`differentSpaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异空间奖品记录表';
#####################zhangao end#################################

#####################zhangdi bg##################################
#####################zhangdi end#################################


#####paas######
#####paas######
