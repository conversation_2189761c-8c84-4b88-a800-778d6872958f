#####################Mengdanni bg################################
ALTER TABLE `t_t_product_spu`
    ADD COLUMN `discountedPrice` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '折扣价' AFTER `priceFee`,
    ADD COLUMN `boxTagId` bigint(20) NULL COMMENT '自定义标签' AFTER `shareDesc`;

ALTER TABLE `t_t_product_sku`
    ADD COLUMN `discountedPrice` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '折扣价' AFTER `priceFee`;

INSERT INTO `sys_function`(`funcName`,`funcKey`,`isAtom`,`isOpen`,`moduleId`,`pid`,`type`,`priority`,`remark`,`dataStatus`)
VALUES ('清空客户赏袋','member.mng.clean',b'1',b'1',1,1,null,12,null,b'1');

INSERT INTO `sys_version_function`(`versionId`, `funcId`) VALUES (1, (SELECT id FROM sys_function WHERE funcKey = 'member.mng.clean'));
INSERT INTO `t_b_role_function`(`roleId`, `funcId`) VALUES (1, (SELECT id FROM sys_function WHERE funcKey = 'member.mng.clean'));
INSERT INTO `sys_version_function`(`versionId`, `funcId`) VALUES (5, (SELECT id FROM sys_function WHERE funcKey = 'member.mng.clean'));
INSERT INTO `t_b_role_function`(`roleId`, `funcId`) VALUES (5, (SELECT id FROM sys_function WHERE funcKey = 'member.mng.clean'));

CREATE TABLE `t_pl_box_tag_config` (
                                       `id` bigint(20) NOT NULL COMMENT 'ID',
                                       `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                       `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                       `name` varchar(20) NOT NULL COMMENT '标签名称',
                                       `image` varchar(50) NOT NULL COMMENT '标签图片',
                                       `position` varchar(50) NOT NULL COMMENT '展示位置',
                                       `created` datetime DEFAULT NULL COMMENT '创建时间',
                                       `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                       `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                       `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                       `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标识',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `idx_chainId` (`chainId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自定义标签设置表';

INSERT INTO t_b_sys_code (typeCode, sysCode, sysName, tenantId, priority, created, dataStatus, remark) VALUES ('S_BTP', 'S_BTP_LEFT', '左侧', null, 0, NOW(), true, '自定义标签位置');
INSERT INTO t_b_sys_code (typeCode, sysCode, sysName, tenantId, priority, created, dataStatus, remark) VALUES ('S_BTP', 'S_BTP_RIGHT', '右侧', null, 0, NOW(), true, '自定义标签位置');
INSERT INTO `t_b_sys_code_lang`( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_BTP', 'S_BTP_LEFT', '左侧', 'zh_CN', 0, NULL, b'1');
INSERT INTO `t_b_sys_code_lang`( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_BTP', 'S_BTP_RIGHT', '右侧', 'en_US', 0, NULL, b'1');
INSERT INTO `t_b_sys_code_lang`( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_BTP', 'S_BTP_LEFT', '左侧', 'en_US', 0, NULL, b'1');
INSERT INTO `t_b_sys_code_lang`( `typeCode`, `sysCode`, `sysName`, `lang`, `priority`, `created`, `dataStatus`) VALUES ( 'S_BTP', 'S_BTP_RIGHT', '右侧', 'zh_CN', 0, NULL, b'1');

ALTER TABLE `t_b_box_reward_config`
    ADD COLUMN `spuIds` text DEFAULT NULL COMMENT '存储部分可选spuId' AFTER `lotteryJson`,
ADD COLUMN `applyType` varchar(20) NOT NULL COMMENT '盲盒适用范围' AFTER `spuIds`;

INSERT INTO `sys_function`(`funcName`,`funcKey`,`isAtom`,`isOpen`,`moduleId`,`pid`,`type`,`priority`,`remark`,`dataStatus`) values
    ('自定义标签设置','settings.box.self.tag',b'1',b'1',0,123,null,6700,null,b'1');

INSERT INTO `sys_version_function`( `versionId`, `funcId`) VALUES ( 1, (SELECT id from sys_function where funcKey = 'settings.box.self.tag'));
INSERT INTO `t_b_role_function`( `roleId`, `funcId`) VALUES ( 1, (SELECT id from sys_function where funcKey = 'settings.box.self.tag'));
INSERT INTO `sys_version_function`( `versionId`, `funcId`) VALUES ( 5, (SELECT id from sys_function where funcKey = 'settings.box.self.tag'));
INSERT INTO `t_b_role_function`( `roleId`, `funcId`) VALUES ( 5, (SELECT id from sys_function where funcKey = 'settings.box.self.tag'));
#####################Mengdanni end###############################

#####################zhangdi bg##################################

#####################zhangdi end#################################

#####################jiangyh bg##################################

#####################jiangyh end#################################

#####################wanghaocheng b##############################
ALTER TABLE  `t_pl_cashback_config`
    ADD COLUMN `refundType` varchar(20) NOT NULL  DEFAULT 'proportion' COMMENT '返现类型;比例proportion;固定金额regular' AFTER `chooseMemberType`;

ALTER TABLE `t_pl_sweet_config`
    MODIFY COLUMN `profitMin` decimal(12, 4) NOT NULL DEFAULT 0 COMMENT '抽赏利润min' AFTER `applyType`;
ALTER TABLE `t_pl_sweet_config`
    MODIFY COLUMN `profitMax` decimal(12, 4) NOT NULL DEFAULT 0 COMMENT '抽赏利润max' AFTER `profitMin`;
ALTER TABLE `t_pl_sweet_config`
    MODIFY COLUMN `recoveryMin` decimal(12, 4) NOT NULL DEFAULT 0 COMMENT '回收价min' AFTER `profitMax`;
ALTER TABLE `t_pl_sweet_config`
    MODIFY COLUMN `recoveryMax` decimal(12, 4) NOT NULL DEFAULT 0 COMMENT '回收价max' AFTER `recoveryMin`;

ALTER TABLE  `t_pl_sign_activity`
    ADD COLUMN `participationMoney` decimal(12, 2) NOT NULL DEFAULT 0 COMMENT '参与签到规则,玩家日流水' AFTER `isSendReward`;

#####################wanghaocheng e##############################


#####################lcj b#######################################

#####################lcj e#######################################
