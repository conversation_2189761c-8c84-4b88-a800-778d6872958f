#####################Mengdanni bg######################################
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ( 'S_MLTE', 'S_MLTE_SIGN_IN', '签到', NULL, 0, NULL, b'1', '会员等级任务类型');

CREATE TABLE `t_t_wechat_group_config` (
  `id` bigint(20) NOT NULL,
  `chainId` bigint(20) NOT NULL,
  `tenantId` bigint(20) DEFAULT NULL,
  `isOpen` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否展示',
  `position` varchar(50) NOT NULL COMMENT '展示位置',
  `icon` varchar(50) NOT NULL COMMENT '图标',
  `wechatGroupImg` varchar(50) DEFAULT NULL COMMENT '群二维码图片',
  `isOpenExpired` bit(1) NOT NULL COMMENT '是否开启过期提醒',
  `expired` datetime NOT NULL COMMENT '过期时间',
  `uploadTime` datetime NOT NULL COMMENT '上传时间',
  `isSend` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否发送过短信',
  `created` datetime DEFAULT NULL COMMENT '创建时间',
  `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated` datetime DEFAULT NULL COMMENT '更新时间',
  `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
  `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标示',
  PRIMARY KEY (`id`),
  KEY `idx_chainId` (`chainId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群进群设置表';

INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_HOME_PAGE', '首页', NULL, 0, NULL, b'1', '进群交流板块类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_DRAW_BOX', '抽赏页详情', NULL, 0, NULL, b'1', '进群交流板块类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_DRAW_CARD', '抽卡页详情', NULL, 0, NULL, b'1', '进群交流板块类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_DRAW_EGG', '扭蛋详情页', NULL, 0, NULL, b'1', '进群交流板块类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_REWARD', '赏袋', NULL, 0, NULL, b'1', '进群交流板块类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_MINE', '我的', NULL, 0, NULL, b'1', '进群交流板块类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_WG', 'S_WG_TITLE', '头衔', NULL, 0, NULL, b'1', '进群交流板块类型');
#####################Mengdanni end######################################

#####################guoweichen bg######################################
ALTER TABLE `t_t_member_ex` ADD COLUMN `guideIds` json NULL COMMENT '新手引导已读ID半角逗号分隔' AFTER `latestTradeTime`;

CREATE TABLE `t_t_member_guide` (
                                    `id` bigint(20) NOT NULL,
                                    `chainId` bigint(20) NOT NULL,
                                    `tenantId` bigint(20) DEFAULT NULL,
                                    `isOpen` bit(1) NOT NULL COMMENT '是否开启',
                                    `position` varchar(50) NOT NULL COMMENT '展示位置',
                                    `isAll` bit(1) NOT NULL COMMENT '是否全部',
                                    `showJson` json NOT NULL COMMENT '展示json(图片/视频)',
                                    `created` datetime DEFAULT NULL,
                                    `createBy` bigint(20) DEFAULT NULL,
                                    `updated` datetime DEFAULT NULL,
                                    `updateBy` bigint(20) DEFAULT NULL,
                                    `dataStatus` bit(1) NOT NULL DEFAULT b'1' COMMENT '删除标示',
                                    PRIMARY KEY (`id`),
                                    KEY `idx_chainId` (`chainId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新手引导表';

INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MG', 'S_MG_HOME_PAGE', '首页', NULL, 0, NULL, b'1', '新手引导展示类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MG', 'S_MG_MAGIC_BOX_NEW', '常规盲盒', NULL, 0, NULL, b'1', '新手引导展示类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MG', 'S_MG_MAGIC_BOX', '无限盲盒', NULL, 0, NULL, b'1', '新手引导展示类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MG', 'S_MG_CARD', '抽卡', NULL, 0, NULL, b'1', '新手引导展示类型');
INSERT INTO `t_b_sys_code`(`typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ('S_MG', 'S_MG_MINE', '我的', NULL, 0, NULL, b'1', '新手引导展示类型');

INSERT INTO t_t_member_guide (id, chainId, tenantId, isOpen, position, isAll, showJson ) SELECT id, chainId, tenantId, isMaShow, 'S_MG_MINE', FALSE, extJson FROM t_pl_application_config WHERE application = 'guide';
####################guoweichen end######################################


#####################zhangdi bg######################################
ALTER TABLE  `t_pl_sign_activity`
    DROP COLUMN `isOpen`;

ALTER TABLE `t_pl_sign_activity`
    ADD COLUMN `continuousOpen` bit(1) NOT NULL DEFAULT b'0' COMMENT '连续是否开启' AFTER `isRepeatRewards`;

ALTER TABLE  `t_pl_sign_rule`
    DROP COLUMN `giftId`,
    DROP COLUMN `customContent`,
    MODIFY COLUMN `ruleType` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'access' COMMENT '签到规则 base/continuous' AFTER `activityId`;
ALTER TABLE `t_pl_sign_record`
    ADD COLUMN `activityId` bigint(20) NOT NULL COMMENT '活动id' AFTER `tenantId`,
    ADD COLUMN `createDay` date NOT NULL COMMENT '签到日期' AFTER `created`,
    ADD INDEX `idx_memberId`(`memberId`) USING BTREE,
    ADD INDEX `idx_activityId`(`activityId`) USING BTREE,
    ADD INDEX `idx_memberId_createDay`(`memberId`, `createDay`) USING BTREE;


ALTER TABLE  `t_pl_sign_record`
    ADD COLUMN `isNewMember` bit(1) NOT NULL COMMENT '是否新用户' AFTER `isFirstSign`;


ALTER TABLE  `t_pl_sign_record`
    DROP INDEX `idx_memberId_createDay`,
    ADD UNIQUE INDEX `idx_memberId_createDay`(`memberId`, `createDay`) USING BTREE;


INSERT INTO `t_b_sys_code`( `typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ( 'S_MRRW', 'S_MRRW_SIGN', '签到', NULL, 0, NULL, b'1', '商品获得途径');
INSERT INTO `t_b_sys_code`( `typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES ( 'S_AT', 'S_AT_GIFT', '拉新裂变', NULL, 0, NULL, b'1', '活动类型');


ALTER TABLE `sso_user`
    ADD COLUMN `lastLoginCity` varchar(50) NULL DEFAULT '' COMMENT '最后登入城市' AFTER `lastLoginIp`;

###############paas 执行###
#INSERT INTO `sys_message_tmpl`( `tmplName`, `tmplEvent`, `tmplEventDesc`, `tmplType`, `tmplId`, `appId`, `dataString`, `smsTmpl`, `smsTmplId`, `created`) VALUES ( '商品价格异常改动通知', 'product_price_change', '商品价格异常改动通知', NULL, NULL, NULL, NULL, '系统内{0}商品价格发生了异常变更，请及时查看', '5319460', '2022-08-05 15:27:00');
#INSERT INTO `sys_message_tmpl`( `tmplName`, `tmplEvent`, `tmplEventDesc`, `tmplType`, `tmplId`, `appId`, `dataString`, `smsTmpl`, `smsTmplId`, `created`) VALUES ( '二维码过期通知', 'qr_code_expired', '二维码过期通知', NULL, NULL, NULL, NULL, '{0}中的引导用户进群的二维码有效期仅剩余1天，请及时生成新的二维码，并到后台更新！', '5320550', '2022-08-05 15:27:03');
#UPDATE  `sys_message_tmpl_ref` SET `tmplEvents` = 'deliver_notice,fixed_success_send,bidding_success_send,win_lottery_remind,return_reward_bag_notice,activity_finish_notice,check_code,universal_check_code,product_price_change,qr_code_expired' WHERE `client` = 'mb';
###############paas 执行###

#####################zhangdi end######################################