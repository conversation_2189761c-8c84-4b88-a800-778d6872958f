CREATE TABLE `dist_profit_product_range_config` (
                                                    `id` bigint(20) NOT NULL COMMENT 'ID',
                                                    `chainId` bigint(20) NOT NULL COMMENT '连锁店id',
                                                    `tenantId` bigint(20) DEFAULT NULL COMMENT '租户ID',
                                                    `productRangeType` varchar(30) NOT NULL COMMENT '商品范围类型 all / part',
                                                    `spuType` varchar(30) NOT NULL COMMENT '商品类型',
                                                    `profitRatio` decimal(10,2) NOT NULL COMMENT '分佣比例',
                                                    `created` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `createBy` bigint(20) DEFAULT NULL COMMENT '创建人',
                                                    `updated` datetime DEFAULT NULL COMMENT '更新时间',
                                                    `updateBy` bigint(20) DEFAULT NULL COMMENT '更新人',
                                                    `dataStatus` bit(1) DEFAULT b'1',
                                                    PRIMARY KEY (`id`),
                                                    UNIQUE KEY `uk_c_s` (`chainId`,`spuType`) USING BTREE,
                                                    KEY `idx_chainId` (`chainId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品范围配置';

INSERT INTO  `t_b_sys_code`(`id`, `typeCode`, `sysCode`, `sysName`, `tenantId`, `priority`, `created`, `dataStatus`, `remark`) VALUES (24, 'S_MLCT', 'S_MLCT_LOGIN_IN', '登入成为代理', NULL, 0, NULL, b'1', '入会条件');
