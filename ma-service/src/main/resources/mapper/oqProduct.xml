<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.ma.oq.product">

        <select id="queryOqPointProductPage" resultType="com.lewei.eshop.ma.message.vo.MaOqProductPageVo">
            SELECT sp.id,
                   spu.id AS spuId,
                   spu.name,
                   spu.mainImage,
                   MAX(sku.primeCostFee) primeCostFee,
                   sp.point,
                   sp.limitNum,
                   sp.isLimit
            FROM t_t_oq_point_product sp
            LEFT JOIN t_t_product_spu spu ON sp.spuId = spu.id
            LEFT JOIN t_b_sys_code sc ON sc.sysCode = spu.spuType
            LEFT JOIN t_t_product_sku sku ON sku.spuId = spu.id and  sku.dataStatus = 1
            WHERE sp.chainId = #{chainId}
            AND spu.dataStatus = 1
            AND sp.dataStatus = 1
            AND sp.isOpen = 1
            <if test="queryKey != null and queryKey != ''">
                AND spu.name LIKE CONCAT('%', #{queryKey}, '%')
            </if>
            <if test="spuType != null and spuType != ''">
                AND spu.spuType = #{spuType}
            </if>
            GROUP BY sp.id,spu.id
            ORDER BY sp.id DESC
            <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
                LIMIT #{start},#{pageSize}
            </if>
            <if test="start != null and start > 0 and pageSize == null">
                LIMIT #{pageSize}
            </if>
        </select>


    <select id="queryOqProductDetail" resultType="com.lewei.eshop.ma.message.vo.MaOqProductDetailVo">
        SELECT sp.id,
        spu.id AS spuId,
        spu.name,
        spu.mainImage,
        MAX(sku.primeCostFee) primeCostFee,
        MAX(sku.priceFee) priceFee,
        intro.productIntro productIntro,
        sp.point,
        sp.limitNum,
        sp.isLimit
        FROM t_t_oq_point_product sp
        LEFT JOIN t_t_product_spu spu ON sp.spuId = spu.id
        LEFT JOIN t_t_product_intro intro ON intro.spuId = spu.id
        LEFT JOIN t_b_sys_code sc ON sc.sysCode = spu.spuType
        LEFT JOIN t_t_product_sku sku ON sku.spuId = spu.id and  sku.dataStatus = 1
        WHERE sp.chainId = #{chainId}
        AND sp.spuId = #{spuId}

    </select>

    <select id="queryExchangedNum" resultType="java.lang.Integer">
         SELECT COUNT(op.id) FROM t_t_oq_point_product_record op
         WHERE  op.chainId = #{chainId}
         AND op.spuId = #{spuId}
         AND op.memberId = #{memberId}
           AND DATE_FORMAT(op.created,'%Y-%m-%d') = DATE_FORMAT(NOW(),'%Y-%m-%d')
    </select>


</mapper>