<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.ma.media.browse">

    <resultMap id="queryPageMediaMap" type="com.lewei.eshop.common.vo.ma.MaMediaBrowseListVo">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="mainImage" column="mainImage"/>
        <result property="image" column="image"/>
        <result property="video" column="video"/>
        <result property="likedNum" column="likedNum"/>
        <result property="uploadType" column="uploadType"/>
        <result property="memberName" column="memberName"/>
        <result property="headImage" column="headImage"/>
        <result property="browseDate" column="browseDate"/>
        <result property="memberId" column="memberId"/>
    </resultMap>

    <insert id="saveMediaBrowse">
        INSERT INTO
            t_pl_media_browse (memberId, mediaId, chainId, created)
        VALUES (#{memberId}, #{mediaId}, #{chainId}, now())
        ON DUPLICATE KEY UPDATE created = NOW();
    </insert>

    <select id="queryPageMediaBrowse" resultMap="queryPageMediaMap">
        SELECT
            m.id,
            m.title,
            m.mainImage,
            m.video,
            m.image,
            m.likedNum,
            m.uploadType,
            tm.memberName,
            tm.headImage,
            mb.created AS browseDate,
            m.createBy AS memberId
        FROM t_pl_media m
        LEFT JOIN t_pl_media_browse mb ON m.id = mb.mediaId
        LEFT JOIN t_t_member tm ON tm.id = m.createBy
        WHERE mb.chainId = #{chainId}
        AND mb.memberId = #{memberId}
        AND m.dataStatus = 1
        AND m.publicFlag = 1
        <if test="st != null">
            AND mb.created &gt;= #{st}
        </if>
        <if test="et != null">
            AND mb.created &lt;= #{et}
        </if>
        ORDER BY mb.created DESC
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>
</mapper>