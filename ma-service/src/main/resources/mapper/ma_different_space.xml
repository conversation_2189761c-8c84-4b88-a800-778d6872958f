<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.ma.different.space">
    <select id="queryDifferentSpaceReward" resultType="com.lewei.eshop.common.vo.different.MaDifferentSpaceRewardVo">
        SELECT dsr.id,
               dsr.spuId,
               dsr.skuId,
               dsr.differentSpaceId,
               dsr.odds,
               spu.mainImage spuMainImage,
               spu.name spuName,
               pbrc.id  AS categoryId,
               pbrc.categoryName,
               pbrc.pic categoryPic,
               dsr.priority,
               spu.primeCostFee,
               spu.priceFee
        FROM t_t_different_space_reward dsr
        LEFT JOIN t_t_product_spu spu ON dsr.spuId = spu.id
        LEFT JOIN t_t_product_box_reward_category pbrc ON pbrc.id = dsr.categoryId
        WHERE dsr.chainId = #{chainId}
            AND dsr.dataStatus = 1
            AND dsr.differentSpaceId = #{differentSpaceId}
        order by dsr.priority
    </select>

    <select id="queryDifferentSpace" resultType="com.lewei.eshop.common.vo.different.MaDifferentSpaceVo">
        SELECT ds.id,
               ds.title,
               ds.boxSpuId,
               ds.clearCategoryId,
               ds.progressNum,
               IFNULL(dsm.quantity,0) quantity
        FROM t_t_different_space ds
        LEFT JOIN t_t_different_space_member dsm ON ds.id = dsm.differentSpaceId AND dsm.memberId = #{memberId}
        WHERE ds.chainId = #{chainId}
          AND ds.dataStatus = 1
          AND ds.boxSpuId = #{spuId}
        AND ds.status = 'S_DSS_UP'
    </select>

    <update id="reduceDifferentSpaceMember">
        UPDATE t_t_different_space_member
        SET quantity = quantity - 1
        WHERE differentSpaceId = #{differentSpaceId}
        AND memberId = #{memberId}
        AND quantity > 0
    </update>

    <select id="queryDifferentSpaceRewardRecord" resultType="com.lewei.eshop.common.vo.different.MaDifferentSpaceRewardRecordVo">
        SELECT dsrr.id,
               dsrr.differentSpaceId,
               dsrr.memberId,
                m.headImage,
                m.mobile,
                m.memberName,
               dsrr.categoryPic categoryPic,
               dsrr.mainImage,
               dsrr.rewardName,
               dsrr.categoryName,
               dsrr.created
        FROM t_t_different_space_reward_record dsrr
        LEFT JOIN t_t_member m ON m.id = dsrr.memberId
        WHERE dsrr.chainId = #{chainId}
        AND dsrr.dataStatus = 1
          AND dsrr.differentSpaceId = #{differentSpaceId}
          <if test="memberId != null">
              AND dsrr.memberId = #{memberId}
          </if>
        ORDER BY dsrr.id DESC
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>

    <update id="addDifferentSpaceMemberGift">
        UPDATE t_t_different_space_member
        SET giftNum = giftNum + 1
        WHERE differentSpaceId = #{differentSpaceId}
        AND memberId = #{memberId}
    </update>
</mapper>