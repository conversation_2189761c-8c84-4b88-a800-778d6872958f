<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.ma">

    <resultMap id="queryMemberCacheByOpenId" type="com.lewei.eshop.ma.sso.MemberCache">
        <result property="id" column="id"/>
        <result property="chainId" column="chainId"/>
        <result property="tenantId" column="tenantId"/>
        <result property="wechatAccount" column="wechatAccount"/>
        <result property="mobile" column="mobile"/>
        <result property="levelId" column="levelId"/>
        <result property="userCode" column="userCode"/>
        <result property="openId" column="openId"/>
        <result property="priority" column="priority"/>
        <result property="payConfigId" column="payConfigId"/>
    </resultMap>

    <resultMap id="queryProducts" type="com.lewei.eshop.ma.message.vo.MaProductVO">
        <result property="id" column="id"/>
        <result property="parentSpuId" column="parentSpuId"/>
        <result property="bannerImage" column="bannerImage"/>
        <result property="mainImage" column="mainImage"/>
        <result property="name" column="name"/>
        <result property="originalPrice" column="originalPrice"/>
        <result property="priceFee" column="priceFee"/>
        <result property="discountedPrice" column="discountedPrice"/>
        <result property="costPriceFee" column="costPriceFee"/>
        <result property="lookerNum" column="lookerNum"/>
        <result property="boxType" column="boxType"/>
        <result property="boxTypeName" column="boxTypeName"/>
        <result property="boxDisplayMode" column="boxDisplayMode"/>
        <result property="boxPrizeMode" column="boxPrizeMode"/>
        <result property="boxPrizeModeName" column="boxPrizeModeName"/>
        <result property="boxDisplayModeName" column="boxDisplayModeName"/>
        <result property="lotteryType" column="lotteryType"/>
        <result property="quantity" column="quantity"/>
        <result property="salesCount" column="salesCount"/>
        <result property="quantityLimit" column="quantityLimit"/>
        <result property="serviceTime" column="serviceTime"/>
        <result property="unit" column="unit"/>
        <result property="unitDesc" column="unitDesc"/>
        <result property="categoryId" column="categoryId"/>
        <result property="productCategoryName" column="productCategoryName"/>
        <result property="sellingPoint" column="sellingPoint"/>
        <result property="cardType" column="cardType"/>
        <result property="spuType" column="spuType"/>
        <result property="isTop" column="isTop"/>
        <result property="isMine" column="isMine"/>
        <result property="cornucopia" column="cornucopia"/>
        <result property="skuId" column="skuId"/>
        <result property="quantity" column="quantity"/>
        <result property="boxTagId" column="boxTagId"/>
        <result property="boxTagImg" column="boxTagImg"/>
        <result property="boxTagPosition" column="boxTagPosition"/>
        <result property="doubleJson" column="doubleJson"/>
        <result property="paymentInfo" column="paymentInfo"/>
    </resultMap>

    <resultMap id="queryProductDetailById" type="com.lewei.eshop.ma.message.vo.MaProductDetailVO">
        <result property="id" column="id"/>
        <result property="parentSpuId" column="parentSpuId"/>
        <result property="name" column="name"/>
        <result property="categoryId" column="categoryId"/>
        <result property="categoryId" column="categoryId"/>
        <result property="spuBannerImage" column="spuBannerImage"/>
        <result property="spuMainImage" column="spuMainImage"/>
        <result property="lookerNum" column="lookerNum"/>
        <result property="backgroundImage" column="backgroundImage"/>
        <result property="boxType" column="boxType"/>
        <result property="boxTypeName" column="boxTypeName"/>
        <result property="boxDisplayMode" column="boxDisplayMode"/>
        <result property="spuPriceFee" column="spuPriceFee"/>
        <result property="spuOriginalPrice" column="spuOriginalPrice"/>
        <result property="spuCostPriceFee" column="spuCostPriceFee"/>
        <result property="shareDesc" column="shareDesc"/>
        <result property="sellingPoint" column="sellingPoint"/>
        <result property="productIntro" column="productIntro"/>
        <result property="salesCount" column="salesCount"/>
        <result property="status" column="status"/>
        <result property="onlineShopShow" column="onlineShopShow"/>
        <result property="appOnlineShopShow" column="appOnlineShopShow"/>
        <result property="logisticsMode" column="logisticsMode"/>
        <result property="cardType" column="cardType"/>
        <result property="duration" column="duration"/>
        <result property="times" column="times"/>
        <result property="selectNum" column="selectNum"/>
        <result property="isCollected" column="isCollected"/>
        <result property="recoveryFee" column="recoveryFee"/>
        <result property="spuType" column="spuType"/>
        <result property="extJson" column="extJson"/>
        <result property="isCard" column="isCard"/>
        <collection property="attrs" ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$MaAttr">
            <id property="attrId" column="attrId"/>
            <result property="attrName" column="attrName"/>
            <result property="attrDesc" column="attrDesc"/>
            <collection property="attrValues"
                        ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$MaAttr$MaAttrValue">
                <id property="attrValueId" column="attrValueId"/>
                <result property="attrValueName" column="attrValueName"/>
                <result property="attrValueDesc" column="attrValueDesc"/>
            </collection>
        </collection>
        <collection property="skus" ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$MaSku">
            <id property="skuId" column="skuId"/>
            <id property="parentSkuId" column="parentSkuId"/>
            <result property="attrs" column="attrs"/>
            <result property="bannerImage" column="bannerImage"/>
            <result property="mainImage" column="mainImage"/>
            <result property="priceFee" column="priceFee"/>
            <result property="discountedPrice" column="discountedPrice"/>
            <result property="originalPrice" column="originalPrice"/>
            <result property="costPriceFee" column="costPriceFee"/>
            <result property="quantity" column="quantity"/>
            <result property="productCode" column="productCode"/>
            <result property="quantityLimit" column="quantityLimit"/>
            <result property="serviceTime" column="serviceTime"/>
            <result property="unit" column="unit"/>
            <result property="unitDesc" column="unitDesc"/>
            <result property="weight" column="weight"/>
        </collection>
        <collection property="categories" ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$Category">
            <id property="categoryId" column="spuCategoryId"/>
            <result property="categoryName" column="spuCategoryName"/>
            <result property="parentId" column="spuCategoryParentId"/>
            <result property="extJson" column="categoryExtJson"/>
        </collection>
        <collection property="skuAttrAndValueMaps"
                    ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$MaSkuAttrAndValueMap">
            <id property="mapSkuId" column="mapSkuId"/>
            <collection property="mapAttrAndValues"
                        ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$MaSkuAttrAndValueMap$MapAttrAndValue">
                <id property="attrId" column="attrId"/>
                <result property="attrValueId" column="attrValueId"/>
            </collection>
        </collection>
        <collection property="cardItems" ofType="com.lewei.eshop.ma.message.vo.MaProductDetailVO$CardItem">
            <result property="spuItemId" column="spuItemId"/>
            <result property="skuItemId" column="skuItemId"/>
            <result property="mainImage" column="itemMainImage"/>
            <result property="name" column="itemName"/>
            <result property="attrValues" column="attrValues"/>
            <result property="times" column="itemTimes"/>
        </collection>
    </resultMap>

    <resultMap id="queryShoppingCarts" type="com.lewei.eshop.ma.message.vo.ShoppingCartVO">
        <result property="cartId" column="cartId"/>
        <result property="num" column="num"/>
        <result property="spuId" column="spuId"/>
        <result property="skuId" column="skuId"/>
        <result property="parentSpuId" column="parentSpuId"/>
        <result property="name" column="name"/>
        <result property="dataStatus" column="dataStatus"/>
        <result property="mainImage" column="mainImage"/>
        <result property="priceFee" column="priceFee"/>
        <result property="originalPrice" column="originalPrice"/>
        <result property="costPriceFee" column="costPriceFee"/>
        <result property="status" column="status"/>
        <result property="quantity" column="quantity"/>
        <result property="spuType" column="spuType"/>
        <collection property="attrs" ofType="com.lewei.eshop.ma.message.vo.ShoppingCartVO$CartSkuAttrAndValue">
            <id property="attrId" column="attrId"/>
            <result property="attrName" column="attrName"/>
            <result property="attrValueId" column="attrValueId"/>
            <result property="attrValueName" column="attrValueName"/>
        </collection>
    </resultMap>

    <resultMap id="queryShoppingCartSkus" type="com.lewei.eshop.ma.message.vo.ShoppingCartSkuVO">
        <id property="spuId" column="spuId"/>
        <collection property="attrs" ofType="com.lewei.eshop.ma.message.vo.ShoppingCartSkuVO$CartAttr">
            <id property="attrId" column="attrId"/>
            <result property="attrName" column="attrName"/>
            <result property="attrDesc" column="attrDesc"/>
            <collection property="attrValues"
                        ofType="com.lewei.eshop.ma.message.vo.ShoppingCartSkuVO$CartAttr$CartAttrValue">
                <id property="attrValueId" column="attrValueId"/>
                <result property="attrValueName" column="attrValueName"/>
                <result property="attrValueDesc" column="attrValueDesc"/>
            </collection>
        </collection>
        <collection property="skus" ofType="com.lewei.eshop.ma.message.vo.ShoppingCartSkuVO$CartSku">
            <id property="skuId" column="skuId"/>
            <result property="mainImage" column="mainImage"/>
            <result property="priceFee" column="priceFee"/>
            <result property="costPriceFee" column="costPriceFee"/>
            <result property="quantity" column="quantity"/>
        </collection>
        <collection property="skuAttrAndValueMaps"
                    ofType="com.lewei.eshop.ma.message.vo.ShoppingCartSkuVO$CartSkuAttrAndValueMap">
            <id property="mapSkuId" column="mapSkuId"/>
            <collection property="mapAttrAndValues"
                        ofType="com.lewei.eshop.ma.message.vo.ShoppingCartSkuVO$CartSkuAttrAndValueMap$MapAttrAndValue">
                <id property="attrId" column="attrId"/>
                <result property="attrValueId" column="attrValueId"/>
            </collection>
        </collection>
    </resultMap>


    <resultMap id="queryOrderDetail" type="com.lewei.eshop.ma.message.vo.OrderDetailVO">
        <id property="id" column="id"/>
        <result property="shippingUser" column="shippingUser"/>
        <result property="telNumber" column="telNumber"/>
        <result property="address" column="address"/>
        <result property="provinceName" column="provinceName"/>
        <result property="cityName" column="cityName"/>
        <result property="countyName" column="countyName"/>
        <result property="email" column="email"/>
        <result property="orderSn" column="orderSn"/>
        <result property="created" column="created"/>
        <result property="paymentMethod" column="paymentMethod"/>
        <result property="paymentMethodDesc" column="paymentMethodDesc"/>
        <result property="payTime" column="payTime"/>
        <result property="orderMoney" column="orderMoney"/>
        <result property="status" column="status"/>
        <result property="plStatus" column="plStatus"/>
        <result property="plStatusDesc" column="plStatusDesc"/>
        <result property="plIdEndTime" column="plIdEndTime"/>
        <result property="estimatedDeliveryTime" column="estimatedDeliveryTime"/>
        <result property="statusDesc" column="statusDesc"/>
        <result property="shippingMoney" column="shippingMoney"/>
        <result property="orderType" column="orderType"/>
        <result property="remark" column="remark"/>
        <result property="reason" column="reason"/>
        <result property="couponPreferentialFee" column="couponPreferentialFee"/>
        <result property="orderExJson" column="orderExJson"/>
        <collection property="orderProducts" ofType="com.lewei.eshop.ma.message.vo.OrderDetailVO$OrderProductVO">
            <id property="orderProductId" column="orderProductId"/>
            <result property="productName" column="productName"/>
            <result property="mainImage" column="mainImage"/>
            <result property="currentUnitPrice" column="currentUnitPrice"/>
            <result property="quantity" column="quantity"/>
            <result property="refundSucCount" column="refundSucCount"/>
            <result property="spuId" column="spuId"/>
            <result property="skuId" column="skuId"/>
            <result property="extJson" column="extJson"/>
            <collection property="ships" ofType="com.lewei.eshop.ma.message.vo.OrderDetailVO$Ships">
                <result property="company" column="company"/>
                <result property="shippingTime" column="shippingTime"/>
                <result property="trackingNum" column="trackingNum"/>
            </collection>
        </collection>
    </resultMap>

    <resultMap id="prePay" type="com.lewei.eshop.ma.message.vo.PrePayVO">
        <id property="id" column="id"/>
        <result property="skuId" column="skuId"/>
        <result property="spuId" column="spuId"/>
        <result property="parentSpuId" column="parentSpuId"/>
        <result property="productName" column="productName"/>
        <result property="mainImage" column="mainImage"/>
        <result property="priceFee" column="priceFee"/>
        <result property="originalPrice" column="originalPrice"/>
        <result property="costPriceFee" column="costPriceFee"/>
        <result property="num" column="num"/>
        <result property="quantity" column="quantity"/>
        <result property="spuType" column="spuType"/>
        <result property="subTotalPriceFee" column="subTotalPriceFee"/>
        <result property="subTotalCostPriceFee" column="subTotalCostPriceFee"/>
        <result property="freightFee" column="freightFee"/>
        <result property="freightTemplateId" column="freightTemplateId"/>
        <result property="weight" column="weight"/>
        <result property="totalWeight" column="totalWeight"/>
        <result property="logisticsMode" column="logisticsMode"/>
        <collection property="attrs" ofType="com.lewei.eshop.ma.message.vo.PrePayVO$AttrAndValue">
            <id property="attrId" column="attrId"/>
            <result property="attrName" column="attrName"/>
            <result property="attrValueId" column="attrValueId"/>
            <result property="attrValueName" column="attrValueName"/>
        </collection>
    </resultMap>

    <resultMap id="prePayBuyNow" type="com.lewei.eshop.ma.message.vo.PrePayBuyNowVO">
        <id property="skuId" column="skuId"/>
        <id property="parentSkuId" column="parentSkuId"/>
        <result property="spuId" column="spuId"/>
        <result property="productName" column="productName"/>
        <result property="mainImage" column="mainImage"/>
        <result property="priceFee" column="priceFee"/>
        <result property="discountedPrice" column="discountedPrice"/>
        <result property="costPriceFee" column="costPriceFee"/>
        <result property="spuType" column="spuType"/>
        <result property="quantity" column="quantity"/>
        <result property="freightFee" column="freightFee"/>
        <result property="freightTemplateId" column="freightTemplateId"/>
        <result property="weight" column="weight"/>
        <result property="logisticsMode" column="logisticsMode"/>
        <result property="parentSpuId" column="parentSpuId"/>
        <result property="tenantId" column="tenantId"/>
        <result property="originalPrice" column="originalPrice"/>
        <result property="boxType" column="boxType"/>
        <collection property="attrs" ofType="com.lewei.eshop.ma.message.vo.PrePayBuyNowVO$AttrAndValue">
            <id property="attrId" column="attrId"/>
            <result property="attrName" column="attrName"/>
            <result property="attrValueId" column="attrValueId"/>
            <result property="attrValueName" column="attrValueName"/>
        </collection>
    </resultMap>

    <resultMap id="querySaveOrderData" type="com.lewei.eshop.ma.message.vo.SaveOrderDataVO">
        <id property="cartId" column="cartId"/>
        <result property="skuId" column="skuId"/>
        <result property="spuId" column="spuId"/>
        <result property="spuType" column="spuType"/>
        <result property="spuName" column="spuName"/>
        <result property="mainImage" column="mainImage"/>
        <result property="priceFee" column="priceFee"/>
        <result property="parentSpuId" column="parentSpuId"/>
        <result property="status" column="status"/>
        <result property="quantity" column="quantity"/>
        <result property="num" column="num"/>
        <result property="plType" column="plType"/>
        <result property="plId" column="plId"/>
        <result property="verifyType" column="verifyType"/>
        <result property="logisticsMode" column="logisticsMode"/>
        <result property="attrValues" column="attrValues"/>
        <result property="parentSkuId" column="parentSkuId"/>
        <result property="supplySkuId" column="supplySkuId"/>
        <result property="primeCostFee" column="primeCostFee"/>
    </resultMap>

    <select id="queryMemberCacheByOpenId" resultType="com.lewei.eshop.ma.sso.MemberCache">
        SELECT m.id,
               m.chainId,
               m.tenantId,
               m.wechatAccount,
               m.mobile,
               m.levelId,
               m.userCode,
               mar.openId,
               payConfigId,
               IFNULL(ml.priority, 0) priority
        FROM t_t_member m
                 LEFT JOIN t_t_member_ma_ref mar ON mar.memberId = m.id
                 LEFT JOIN t_t_member_merchant_verify mmv ON m.id = mmv.memberId
                 LEFT JOIN t_t_member_level ml ON ml.id = m.levelId
        WHERE m.chainId = #{chainId}
          AND mar.openId = #{openId}
          AND m.dataStatus = 1
    </select>

    <select id="queryMemberCacheById" resultType="com.lewei.eshop.ma.sso.MemberCache">
        SELECT m.id,
               m.chainId,
               m.tenantId,
               m.wechatAccount,
               m.mobile,
               m.levelId,
               m.userCode,
               m.openId,
               payConfigId,
               IFNULL(ml.priority, 0) priority
        FROM t_t_member m
                 LEFT JOIN t_t_member_merchant_verify mmv ON m.id = mmv.memberId
                 LEFT JOIN t_t_member_level ml ON ml.id = m.levelId
        WHERE m.chainId = #{chainId}
          AND m.id = #{memberId}
          AND m.dataStatus = 1
    </select>

    <select id="queryOrderByOrderSn" resultType="com.lewei.eshop.entity.order.Order">
        SELECT *
        FROM t_t_order
        WHERE orderSn = #{orderSn} FOR
        UPDATE
    </select>

    <select id="queryProducts" resultMap="queryProducts">
        SELECT spu.id,spu.parentId AS parentSpuId, spu.bannerImage, spu.mainImage,
        spu.name,spu.spuType,box.lookerNum,box.boxType,box.boxDisplayMode,box.boxPrizeMode,box.lotteryType,box.doubleJson,code.sysName
        boxDisplayModeName,s1.sysName boxTypeName,code2.sysName as boxPrizeModeName,
        MIN(sku.priceFee) originalPrice,
        MIN(sku.priceFee) priceFee,
        MIN(sku.discountedPrice) discountedPrice,
        MIN(sku.costPriceFee) costPriceFee,
        MIN(sku.id) skuId,
        MIN(skus.quantityLimit) quantityLimit,
        MIN(sku.quantity) quantity,
        spu.sellingPoint,
        spu.salesCount,
        JSON_UNQUOTE(JSON_EXTRACT(spu.extJson, '$.payment')) AS paymentInfo,
        spu.isTop,
        spu.boxTagId,
        btc.image boxTagImg,
        btc.position boxTagPosition,
        exists(SELECT 1 FROM t_t_cornucopia a left JOIN t_pl_application_config b on a.chainId = b.chainId WHERE
        a.boxSpuId = spu.id AND a.chainId = #{chainId} and a.open = 1 and b.isMaShow =1 and b.application =
        'funds-setting') cornucopia,
        exists(select 1 from t_t_product_box_mine where spuId = spu.id and mineStatus = 'S_PBMS_JXZ' and chainId =
        #{chainId}) isMine
        FROM t_t_product_spu spu
        LEFT JOIN t_t_product_box box ON box.spuId = spu.id
        LEFT JOIN t_b_sys_code s1 ON s1.sysCode = box.boxType
        LEFT JOIN t_b_sys_code code ON box.boxDisplayMode = code.sysCode
        LEFT JOIN t_b_sys_code code2 ON box.boxPrizeMode = code2.sysCode
        LEFT JOIN t_t_product_category_ref pcr ON pcr.spuId = spu.id
        LEFT JOIN t_t_product_sku sku ON sku.spuId = spu.id AND sku.dataStatus = 1
        LEFT JOIN t_t_product_sku_service skus ON sku.id = skus.skuId AND spu.id = skus.spuId
        LEFT JOIN t_pl_box_tag_config btc ON btc.id = spu.boxTagId AND btc.dataStatus = 1
        <if test="myBrowse == true">
            LEFT JOIN t_t_product_browse pb ON pb.spuId = spu.id
        </if>
        WHERE spu.dataStatus = 1
        AND spu.status = 'S_PS_ON'
        AND spu.chainId = #{chainId}
        AND spu.levelPriority &lt;= #{priority}

        <if test="onlineShopShow != null">
            AND spu.onlineShopShow = #{onlineShopShow}
        </if>
        <if test="appOnlineShopShow != null">
            AND spu.appOnlineShopShow = #{appOnlineShopShow}
        </if>
        <if test="tbIsShow != null">
            AND spu.tbIsShow = #{tbIsShow}
        </if>
        <if test="hfIsShow != null">
            AND spu.hfIsShow = #{hfIsShow}
        </if>


        <if test="minPrice != null">
            AND sku.priceFee &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND sku.priceFee &lt;= #{maxPrice}
        </if>
        <if test="tenantId != null">
            AND spu.tenantId = #{tenantId}
        </if>
        <if test="categoryIds != null and categoryIds.size() > 0">
            AND pcr.categoryId IN
            <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentSpuIdList != null and parentSpuIdList.size() > 0">
            AND spu.parentId IN
            <foreach collection="parentSpuIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="neParentSpuIdList != null and neParentSpuIdList.size() > 0">
            AND spu.parentId NOT IN
            <foreach collection="neParentSpuIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="spuIdList != null and spuIdList.size() > 0">
            AND spu.id IN
            <foreach collection="spuIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="neSpuIdList != null and neSpuIdList.size() > 0">
            AND spu.id NOT IN
            <foreach collection="neSpuIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryKey != null and queryKey != ''">
            AND binary spu.name LIKE CONCAT('%',#{queryKey},'%')
        </if>
        <if test="spuType != null and spuType!= ''">
            AND spu.spuType = #{spuType}
        </if>
        <if test="boxTypes != null and boxTypes.size() > 0 ">
            AND box.boxType IN
            <foreach collection="boxTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="maIsShow != null">
            AND spu.maIsShow = 1
        </if>
        <if test="spuIds != null and spuIds.size() > 0">
            AND spu.id IN
            <foreach collection="spuIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="spuIds2 != null and spuIds2.size() > 0 and choseType != '' and choseType != null and choseType == 'PART_EXIST'">
            AND spu.id IN
            <foreach collection="spuIds2" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="spuIds2 != null and spuIds2.size() > 0 and choseType != '' and choseType != null and choseType == 'PART_NOT_EXIST'">
            AND spu.id NOT IN
            <foreach collection="spuIds2" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="myBrowse == true">
            AND pb.memberId = #{memberId}
        </if>
        GROUP BY spu.id
        <choose>
            <when test="orderName != null and orderName != '' and order != null and order != ''">
                ORDER BY ${orderName} ${order}
            </when>
            <when test="spuIds != null and spuIds.size() > 0">
                ORDER BY FIELD(spu.id, ${spuIdStr})
            </when>
            <when test="myBrowse == true">
                ORDER BY spu.isTop DESC,pb.created DESC
            </when>
            <otherwise>
                ORDER BY spu.isTop DESC,spu.sortTime DESC
            </otherwise>
        </choose>
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>

    <select id="queryProductDetailById" resultMap="queryProductDetailById">
        SELECT spu.id,
        spu.parentId parentSpuId,
        spu.name,
        spu.categoryId,
        spu.bannerImage spuBannerImage,
        spu.mainImage spuMainImage,
        spu.priceFee spuOriginalPrice,
        spu.priceFee spuPriceFee,
        spu.costPriceFee spuCostPriceFee,
        spu.shareDesc,
        spu.sellingPoint,
        spu.status,
        spu.recoveryFee,
        spu.onlineShopShow,
        spu.appOnlineShopShow,
        spu.logisticsMode,
        spu.spuType,
        spu.extJson,
        spu.isCard,
        box.backgroundImage,
        box.lookerNum,
        box.boxType,
        box.boxDisplayMode,
        s1.sysName boxTypeName,
        pi.productIntro,
        ssam.skuId mapSkuId,
        pa.id attrId,
        pa.name attrName,
        pa.attrDesc attrDesc,
        pav.id attrValueId,
        pav.value attrValueName,
        pav.attrValueDesc attrValueDesc,
        sku.id skuId,
        sku.parentSkuId ,
        sku.attrs,
        sku.bannerImage,
        sku.mainImage,
        sku.priceFee,
        sku.priceFee originalPrice,
        sku.costPriceFee,
        sku.quantity,
        sku.productCode,
        sku.weight,
        sku.discountedPrice,
        skus.quantityLimit as quantityLimit,
        skus.serviceTime,
        skus.unit,
        sc.sysName unitDesc,
        card.cardType,
        card.duration,
        card.times,
        card.selectNum,
        pci.spuItemId,
        pci.skuItemId,
        pci.times itemTimes,
        itemSpu.name itemName,
        itemSpu.mainImage itemMainImage,
        IFNULL(col.dataStatus,false) isCollected,

        pc.id AS spuCategoryId,
        pc.productCategoryName AS spuCategoryName,
        pc.parentId AS spuCategoryParentId,
        pc.extJson categoryExtJson

        FROM t_t_product_spu spu
        LEFT JOIN t_t_product_box box ON box.spuId = spu.id
        LEFT JOIN t_b_sys_code s1 ON s1.sysCode = box.boxType
        LEFT JOIN t_t_product_sku sku ON spu.id = sku.spuId AND sku.dataStatus =1
        LEFT JOIN t_t_product_sku_service skus ON sku.id = skus.skuId AND spu.id = skus.spuId
        LEFT JOIN t_b_sys_code sc ON sc.sysCode = skus.unit
        LEFT JOIN t_t_product_spu_sku_attr_map ssam ON spu.id = ssam.spuId AND sku.id = ssam.skuId
        LEFT JOIN t_t_product_attr pa ON ssam.attrId = pa.id
        LEFT JOIN t_t_product_attr_value pav ON ssam.attrValueId = pav.id
        LEFT JOIN t_t_product_intro pi ON spu.id = pi.spuId
        LEFT JOIN t_t_product_card card ON card.spuId = spu.id
        LEFT JOIN t_t_product_card_item pci ON pci.spuId = spu.id
        LEFT JOIN t_t_product_spu itemSpu ON itemSpu.id = pci.spuItemId
        LEFT JOIN t_t_product_sku ssku ON ssku.id = pci.skuItemId
        LEFT JOIN t_t_product_collect col ON col.spuId = spu.id AND col.memberId = #{memberId}
        LEFT JOIN t_t_product_category_ref pcr ON pcr.spuId = spu.id
        LEFT JOIN t_t_product_category pc ON pcr.categoryId = pc.id
        WHERE spu.dataStatus = 1
        <if test="spuId != null">
            AND spu.id = #{spuId}
        </if>
        <if test="parentSpuId != null and tenantId != null">
            AND sku.parentSpuId = #{parentSpuId}
            AND sku.tenantId = #{tenantId}
        </if>
        AND spu.chainId = #{chainId}
    </select>

    <select id="queryShoppingCarts" resultMap="queryShoppingCarts">
        SELECT cart.id      cartId,
               cart.num,
               cart.spuId,
               cart.skuId,
               spu.name,
               spu.dataStatus,
               spu.spuType,
               sku.mainImage,
               sku.priceFee,
               sku.parentSpuId,
               sku.priceFee originalPrice,
               sku.costPriceFee,
               spu.status,
               sku.quantity

        FROM t_t_shopping_cart cart
                 LEFT JOIN t_t_product_spu spu ON cart.spuId = spu.id
                 LEFT JOIN t_t_product_sku sku ON cart.spuId = sku.spuId AND cart.skuId = sku.id
        WHERE cart.dataStatus = 1
          AND cart.chainId = #{chainId}
          AND cart.memberId = #{memberId}
          AND cart.client = 'ma'
          AND sku.id is not null
          AND cart.num > 0
          AND spu.status = 'S_PS_ON'
        ORDER BY cart.id DESC
    </select>

    <select id="queryShoppingCartSkus" resultMap="queryShoppingCartSkus">
        SELECT spu.id            spuId,
               ssam.skuId        mapSkuId,
               pa.id             attrId,
               pa.name           attrName,
               pa.attrDesc       attrDesc,
               pav.id            attrValueId,
               pav.value         attrValueName,
               pav.attrValueDesc attrValueDesc,
               sku.id            skuId,
               sku.mainImage,
               sku.priceFee,
               sku.costPriceFee,
               sku.quantity
        FROM t_t_product_spu spu
                 LEFT JOIN t_t_product_sku sku ON spu.id = sku.spuId
                 LEFT JOIN t_t_product_sku_service skus ON sku.id = skus.skuId AND spu.id = skus.spuId
                 LEFT JOIN t_t_product_spu_sku_attr_map ssam ON spu.id = ssam.spuId AND sku.id = ssam.skuId
                 LEFT JOIN t_t_product_attr pa ON ssam.attrId = pa.id
                 LEFT JOIN t_t_product_attr_value pav ON ssam.attrValueId = pav.id
        WHERE spu.dataStatus = 1
          AND spu.id = #{spuId}
          AND spu.chainId = #{chainId}
    </select>

    <select id="queryOrders" resultType="com.lewei.eshop.ma.message.vo.OrderListVO">
        SELECT DISTINCT o.id,
        o.orderSn,
        o.orderMoney,
        o.payTime,
        o.status,
        sc.sysName statusDesc,
        o.created,
        o.hasRefund,
        o.orderType,
        o.extJson as orderExJson,
        o.paymentMethod,
        cf.status as plStatus,
        cf.reason,
        sc2.sysName as plStatusDesc,
        cf.endTime as plIdEndTime,
        GROUP_CONCAT(DISTINCT concat(IFNULL(op.productName,' '),'@@',IFNULL(op.mainImage,'
        '),'@@',IFNULL(op.quantity,0),'@@',IFNULL(op.currentUnitPrice,0),'@@',IFNULL(ope.extJson,' ')) SEPARATOR '##')
        orderProductStr,
        SUM(op.quantity) count
        FROM t_t_order o FORCE INDEX(idx_memberId)
        LEFT JOIN t_t_order_product op ON o.id = op.orderId
        LEFT JOIN t_t_order_product_ex ope ON ope.orderProductId = op.id
        LEFT JOIN t_t_product_sku sku ON sku.id = op.skuId
        LEFT JOIN t_b_sys_code sc ON sc.sysCode = o.status
        LEFT JOIN t_t_order_crowd_ref ref on ref.orderId = o.id
        LEFT JOIN t_t_crowd_funding cf on cf.id = ref.crowdId
        LEFT JOIN t_b_sys_code sc2 on sc2.sysCode = cf.status
        WHERE o.dataStatus = 1
        AND o.chainId = #{chainId}
        AND o.memberId = #{memberId}
        AND o.memberDeleteStatus = 1
        <if test="status!=null and status!=''">
            AND o.status = #{status}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            AND o.status IN
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderTypeList != null and orderTypeList.size > 0">
            AND o.orderType IN
            <foreach item="item" index="index" collection="orderTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderTradeTypeList != null and orderTradeTypeList.size > 0">
            AND o.orderTradeType IN
            <foreach item="item" index="index" collection="orderTradeTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="paymentMethod != null and paymentMethod != ''">
            AND o.paymentMethod = #{paymentMethod}
        </if>
        <if test="orderTradeType != null and orderTradeType != ''">
            AND o.orderTradeType = #{orderTradeType}
        </if>
        <if test="orderType != null and orderType != ''">
            AND o.orderType = #{orderType}
        </if>
        <if test="tradeSubType != null and tradeSubType != ''">
            AND o.tradeSubType = #{tradeSubType}
        </if>
        <if test="neTradeSubType != null and neTradeSubType != ''">
            AND (o.tradeSubType != #{neTradeSubType} OR o.tradeSubType IS NULL )
        </if>
        GROUP BY o.id
        ORDER BY o.id DESC
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>

    <select id="queryOrderDetail" resultMap="queryOrderDetail">
        SELECT o.id,
               o.shippingUser,
               o.telNumber,
               o.address,
               o.provinceName,
               o.cityName,
               o.countyName,
               o.email,
               o.orderSn,
               o.created,
               o.paymentMethod,
               sc1.sysName    paymentMethodDesc,
               o.payTime,
               o.orderMoney,
               o.status,
               sc.sysName     statusDesc,
               o.shippingMoney,
               o.orderType,
               o.remark,
               cf.status   as plStatus,
               sc2.sysName as plStatusDesc,
               cf.endTime  as plIdEndTime,
               cf.reason,
               cf.estimatedDeliveryTime,
               o.couponPreferentialFee,
               o.extJson      orderExJson,
               op.id          orderProductId,
               op.productName,
               op.mainImage,
               op.currentUnitPrice,
               op.quantity,
               op.refundSucCount,
               op.spuId,
               op.skuId,
               ope.extJson,
               ops.company,
               ops.trackingNum,
               ops.shippingTime
        FROM t_t_order o
                 LEFT JOIN t_t_order_product op ON o.id = op.orderId
                 LEFT JOIN t_t_order_product_ex ope ON ope.orderProductId = op.id
                 LEFT JOIN t_t_order_product_ship ops ON ops.orderId = o.id AND ops.orderProductId = op.id
                 LEFT JOIN t_b_sys_code sc on sc.sysCode = o.status
                 LEFT JOIN t_b_sys_code sc1 on sc1.sysCode = o.paymentMethod
                 LEFT JOIN t_t_order_crowd_ref ref on ref.orderId = o.id
                 LEFT JOIN t_t_crowd_funding cf on cf.id = ref.crowdId
                 LEFT JOIN t_b_sys_code sc2 on sc2.sysCode = cf.status
        WHERE o.dataStatus = 1
          AND o.id = #{orderId}
    </select>

    <select id="prePay" resultMap="prePay">
        SELECT cart.id,
        cart.spuId,
        cart.skuId,
        spu.name productName,
        sku.mainImage,
        sku.priceFee,
        sku.priceFee originalPrice,
        sku.parentSpuId,
        sku.costPriceFee,
        cart.num,
        sku.quantity,
        spu.spuType,
        sku.priceFee * cart.num subTotalPriceFee,
        sku.costPriceFee * cart.num subTotalCostPriceFee,
        spu.freightFee,
        spu.freightTemplateId,
        sku.weight,
        sku.weight * cart.num totalWeight ,
        spu.logisticsMode
        FROM t_t_shopping_cart cart
        LEFT JOIN t_t_product_spu spu ON cart.spuId = spu.id
        LEFT JOIN t_t_product_sku sku ON cart.skuId = sku.id
        WHERE cart.dataStatus = 1
        <if test="cartIds != null and cartIds != ''">
            AND cart.id IN (${cartIds})
        </if>
        AND cart.memberId = #{memberId}
        AND cart.chainId = #{chainId}
    </select>

    <select id="prePayBuyNow" resultMap="prePayBuyNow">
        SELECT sku.id       skuId,
               sku.parentSkuId,
               spu.id       spuId,
               sku.parentSpuId,
               sku.tenantId,
               spu.name     productName,
               sku.mainImage,
               sku.priceFee,
               sku.priceFee originalPrice,
               sku.discountedPrice,
               sku.costPriceFee,
               spu.freightFee,
               spu.freightTemplateId,
               sku.weight,
               spu.spuType,
               box.boxType,
               sku.quantity,
               spu.logisticsMode,
               pa.id        attrId,
               pa.name      attrName,
               pav.id       attrValueId,
               pav.value    attrValueName
        FROM t_t_product_sku sku
                 LEFT JOIN t_t_product_spu spu ON sku.spuId = spu.id
                 LEFT JOIN t_t_product_spu_sku_attr_map ssam ON sku.spuId = ssam.spuId AND sku.id = ssam.skuId
                 LEFT JOIN t_t_product_attr pa ON ssam.attrId = pa.id
                 LEFT JOIN t_t_product_attr_value pav ON ssam.attrValueId = pav.id
                 LEFT JOIN t_t_product_box box ON box.spuId = sku.spuId
        WHERE sku.id = #{skuId}
    </select>

    <select id="querySaveOrderData" resultMap="querySaveOrderData">
        SELECT cart.id cartId,
        sku.id skuId,
        spu.id spuId,
        spu.spuType,
        spu.name spuName,
        spu.verifyType,
        sku.mainImage,
        sku.priceFee,
        sku.parentSpuId,
        sku.parentSkuId,
        sku.supplySkuId,
        spu.status,
        sku.quantity,
        cart.num,
        spu.plType,
        spu.plId,
        spu.logisticsMode,
        sku.primeCostFee
        FROM t_t_shopping_cart cart
        LEFT JOIN t_t_product_spu spu ON cart.spuId = spu.id
        LEFT JOIN t_t_product_sku sku ON cart.skuId = sku.id
        WHERE cart.dataStatus = 1
        <if test="cartIds != null and cartIds.size() > 0">
            AND cart.id IN
            <foreach item="cartId" collection="cartIds" open="(" separator="," close=")">
                #{cartId}
            </foreach>
        </if>
    </select>
    <select id="queryOrderCountByMemberId" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_t_order
        WHERE dataStatus = 1
        AND memberId =#{memberId}
        AND status = #{status}
        <if test="tenantId != null">
            AND tenantId = #{tenantId}
        </if>
    </select>
    <select id="queryCategories" resultType="com.lewei.eshop.entity.product.ProductCategory">
        SELECT pc.* FROM t_t_product_category pc
        LEFT JOIN t_t_product_spu spu ON spu.categoryId = pc.id
        <if test="tenantId != null">
            AND spu.tenantId = #{tenantId}
        </if>
        WHERE pc.chainId =#{chainId}
        AND pc.dataStatus = 1
        AND spu.dataStatus = 1
        AND spu.onlineShopShow = 1
        AND spu.status = 'S_PS_ON'
        <if test="maIsShow != null ">
            AND spu.maIsShow = #{maIsShow}
        </if>
        GROUP BY pc.id
        ORDER BY pc.priority
    </select>

    <select id="queryProductCategoryIdList" resultType="java.lang.Long">
        SELECT pcr.categoryId FROM t_t_product_category_ref pcr
        LEFT JOIN t_t_product_category pc ON pcr.categoryId = pc.id
        WHERE pc.dataStatus = 1
        AND pcr.chainId = #{chainId}
        AND pcr.spuId = #{spuId}
        <if test="isParent = true">
            AND pc.parentId IS NULL
        </if>
        <if test="isParent = false">
            AND pc.parentId IS NOT NULL
        </if>
    </select>

    <select id="querySelfPointAddress" resultType="com.lewei.eshop.common.vo.ma.SelfPickPointVo$Address">
        SELECT a.province,
               a.city,
               a.area,
               a.address,
               a.lon,
               a.lat,
               a1.area areaDesc,
               c.city  cityDesc,
               p.pro   provinceDesc
        FROM t_t_self_pick_point_address a
                 LEFT JOIN t_b_sys_city c on a.city = c.id
                 LEFT JOIN t_b_sys_area a1 on a.area = a1.id
                 LEFT JOIN t_b_sys_province p ON a.province = p.id
        WHERE a.chainId = #{chainId}

          AND a.dataStatus = 1
    </select>
    <update id="updateSkuQuantity">
        UPDATE t_t_product_sku
        SET quantity = quantity - #{num}
        WHERE id = #{skuId}
          AND quantity >= #{num}
          AND dataStatus = 1
    </update>
    <select id="queryDfkProducts" resultType="com.lewei.eshop.ma.message.vo.DfkOrderProductVo">
        SELECT spu.name, sku.dataStatus, spu.status
        FROM t_t_order_product op
                 LEFT JOIN t_t_product_sku sku ON op.skuId = sku.id
                 LEFT JOIN t_t_product_spu spu ON sku.spuId = spu.id
        WHERE op.chainId = #{chainId}
          AND op.orderId = #{orderId}
    </select>

    <select id="queryProductCheckLevel" resultType="java.lang.String">
        SELECT spu.name
        FROM t_t_order_product op
                 LEFT JOIN t_t_product_sku sku ON op.skuId = sku.id AND sku.dataStatus = 1
                 LEFT JOIN t_t_product_spu spu ON sku.spuId = spu.id
        WHERE op.chainId = #{chainId}
          AND op.orderId = #{orderId}
          AND spu.levelBuyPriority > #{priority}
    </select>

    <insert id="clickAppCollect">
        INSERT INTO t_pl_App_collect (`plId`, `memberId`, `chainId`, `tenantId`, `appType`, `created`, `dataStatus`)
        VALUES (#{plId}, #{memberId}, #{chainId}, NULL, #{appType}, now(), #{dataStatus})
        ON duplicate KEY UPDATE created    = now(),
                                dataStatus = #{dataStatus};
    </insert>

    <select id="queryMemberBuySecKillProductNum" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(quantity), 0)
        FROM t_t_order_product op
                 JOIN t_t_order o ON op.orderId = o.id
        WHERE op.chainId = #{chainId}
          AND o.memberId = #{memberId}
          AND o.status != 'S_OS_CANCELED'
          AND o.dataStatus = 1
          AND op.secKillId = #{secKillId}
    </select>


    <!--    <select id="queryOrderDetailShip" resultMap="">-->
    <!--        SELECT-->
    <!--        FROM t_t_order o-->
    <!--        LEFT JOIN t_t_order_product_ship ops ON ops.orderId = o.id-->
    <!--        LEFT JOIN -->
    <!--    </select>-->

    <resultMap id="queryOrderDetailShip" type="com.lewei.eshop.ma.message.vo.OrderDetailShipVO">
        <result property="id" column="id"/>
        <result property="shippingUser" column="shippingUser"/>
        <result property="telNumber" column="telNumber"/>
        <result property="address" column="address"/>
        <result property="provinceName" column="provinceName"/>
        <result property="cityName" column="cityName"/>
        <result property="countyName" column="countyName"/>
        <result property="orderSn" column="orderSn"/>
        <result property="created" column="created"/>
        <result property="paymentMethod" column="paymentMethod"/>
        <result property="payTime" column="payTime"/>
        <result property="logisticsMode" column="logisticsMode"/>
        <result property="orderMoney" column="orderMoney"/>
        <result property="paymentMoney" column="paymentMoney"/>
        <result property="status" column="status"/>
        <result property="shippingMoney" column="shippingMoney"/>
        <result property="orderVerifyCode" column="orderVerifyCode"/>
        <result property="orderType" column="orderType"/>
        <result property="remark" column="remark"/>
        <result property="shippingCompName" column="shippingCompName"/>
        <result property="shippingSn" column="shippingSn"/>
        <result property="mobile" column="mobile"/>
        <result property="memberName" column="memberName"/>
        <result property="shopAddress" column="shopAddress"/>
        <result property="pickupTime" column="pickupTime"/>
        <result property="receiveTime" column="receiveTime"/>
        <result property="memberCouponId" column="memberCouponId"/>
        <result property="couponPreferentialFee" column="couponPreferentialFee"/>
        <result property="reductionFee" column="reductionFee"/>
        <result property="payScore" column="payScore"/>
        <result property="scoreDeductionFee" column="scoreDeductionFee"/>
        <result property="evalStatus" column="evalStatus"/>
        <collection property="orderProducts" ofType="com.lewei.eshop.ma.message.vo.OrderDetailShipVO$OrderProductVO">
            <id property="orderProductId" column="orderProductId"/>
            <result property="productName" column="productName"/>
            <result property="mainImage" column="mainImage"/>
            <result property="currentUnitPrice" column="currentUnitPrice"/>
            <result property="quantity" column="quantity"/>
            <result property="totalPrice" column="totalPrice"/>
            <result property="spuLogisticsMode" column="spuLogisticsMode"/>
            <result property="orderProductStatus" column="orderProductStatus"/>
            <result property="lastRefundCode" column="lastRefundCode"/>
            <result property="refundSucCount" column="refundSucCount"/>
            <result property="spuId" column="spuId"/>
            <result property="skuId" column="skuId"/>
            <result property="shareDesc" column="shareDesc"/>
            <result property="payScore" column="opPayScore"/>
            <result property="isSecKill" column="isSecKill"/>

        </collection>
        <collection property="companies" ofType="com.lewei.eshop.ma.message.vo.OrderDetailShipVO$Company">
            <result property="company" column="company"/>
            <result property="trackingNum" column="trackingNum"/>
        </collection>
    </resultMap>

    <select id="queryOrderDetailShip" resultMap="queryOrderDetailShip">
        SELECT o.id,
               o.shippingUser,
               o.telNumber,
               o.address,
               o.provinceName,
               o.cityName,
               o.countyName,
               o.email,
               o.orderSn,
               o.created,
               o.paymentMethod,
               o.payTime,
               o.logisticsMode,
               o.orderMoney,
               o.paymentMoney,
               o.payScore,
               o.scoreDeductionFee,
               o.status,
               o.shippingMoney,
               o.verifyCode                                                       orderVerifyCode,
               o.orderType,
               o.remark,
               o.shippingSn,
               o.pickupTime,
               m.memberName,
               t.address                                                          shopAddress,
               m.mobile,
               o.shippingCompName,
               o.receiveTime,
               o.memberCouponId,
               o.couponPreferentialFee,
               o.reductionFee,
               op.id                                                              orderProductId,
               op.productName,
               op.mainImage,
               op.currentUnitPrice,
               op.quantity,
               op.totalPrice,
               op.orderProductStatus,
               op.lastRefundCode,
               op.refundSucCount,
               op.spuId,
               op.skuId,
               op.payScore                                                        opPayScore,
               ps.logisticsMode                                                   spuLogisticsMode,

               ps.shareDesc,

               o.evalStatus,
               IF(op.id IS NOT NULL, IF(op.secKillId IS NULL, false, true), null) isSecKill,
               ops.company,
               ops.trackingNum
        FROM t_t_order o
                 LEFT JOIN t_t_order_product op ON o.id = op.orderId
                 LEFT JOIN t_t_product_spu ps ON ps.id = op.spuId
                 LEFT JOIN t_b_tenant t ON o.tenantId = t.tenantId
                 LEFT JOIN t_t_member m ON m.id = o.memberId
                 LEFT JOIN t_t_order_product_ship ops ON ops.orderId = o.id
        WHERE o.dataStatus = 1
          AND o.id = #{orderId}
    </select>


    <resultMap id="queryOrderProductShipped" type="com.lewei.eshop.common.vo.member.OrderShippedVo">
        <result column="shipId" property="shipId"/>
        <result column="tel" property="tel"/>
        <result column="shippingUser" property="shippingUser"/>
        <result column="provinceName" property="provinceName"/>
        <result column="cityName" property="cityName"/>
        <result column="countyName" property="countyName"/>
        <result column="email" property="email"/>
        <result column="address" property="address"/>
        <result column="orderSn" property="orderSn"/>
        <result column="shippingMoney" property="shippingMoney"/>
        <result column="created" property="created"/>
        <result column="payTime" property="payTime"/>
        <result column="orderExJson" property="orderExJson"/>
        <collection property="tenants" ofType="com.lewei.eshop.common.vo.member.OrderShippedVo$Tenant">
            <result column="trackingNum" property="trackingNum"/>
            <result column="company" property="company"/>
            <result column="tenantName" property="tenantName"/>
            <collection property="products" ofType="com.lewei.eshop.common.vo.member.OrderShippedVo$Tenant$Product">
                <result column="orderProductId" property="orderProductId"/>
                <result column="productName" property="productName"/>
                <result column="currentUnitPrice" property="currentUnitPrice"/>
                <result column="quantity" property="quantity"/>
                <result column="mainImage" property="mainImage"/>
            </collection>
        </collection>
    </resultMap>
    <select id="orderProductShipped" resultMap="queryOrderProductShipped">
        SELECT o.id        AS shipId,
               o.address,
               o.orderSn,
               o.provinceName,
               o.cityName,
               o.countyName,
               o.email,
               o.shippingMoney,
               o.created,
               o.payTime,
               o.telNumber AS tel,
               o.shippingUser,
               o.extJson   AS orderExJson,
               ops.company,
               ops.trackingNum,
               t.shopName  AS tenantName,
               op.id       AS orderProductId,
               op.productName,
               op.currentUnitPrice,
               op.quantity,
               op.mainImage
        FROM t_t_order o
                 LEFT JOIN t_t_order_product_ship ops ON o.id = ops.orderId
                 LEFT JOIN t_t_order_product op ON op.id = ops.orderProductId
                 LEFT JOIN t_b_tenant t ON t.tenantId = ops.shipTenantId
        WHERE o.id = #{orderId}
    </select>

    <select id="queryOrderBoxItemMemberCountReward" resultType="java.lang.Integer">
        SELECT COUNT(ttobi.id)
        FROM t_t_order_box_item ttobi
        WHERE ttobi.dataStatus = 1
        AND ttobi.chainId = #{chainId}
        AND ttobi.boxItemId = #{boxItemId}
        AND ttobi.memberId = #{memberId}
        AND ttobi.isGift = 0
        <if test="controlPercentNum != null">
            AND ttobi.serialNum &lt;= #{controlPercentNum}
        </if>
    </select>

    <select id="queryProductBoxItemMemberCountReward" resultType="java.lang.Integer">
        SELECT COUNT(ttpbirt.id)
        FROM t_t_product_box_item_reward_tmpl ttpbirt
        WHERE ttpbirt.chainId = #{chainId}
        AND ttpbirt.boxItemId = #{boxItemId}
        AND ttpbirt.memberId = #{memberId}
        AND ttpbirt.isGift = 0
    </select>

    <update id="cancelOrder">
        UPDATE t_t_order o
            LEFT JOIN t_pl_member_coupon mc ON o.memberCouponId = mc.id
        SET o.updated             = NOW(),
            mc.memberCouponStatus = 'S_MCDS_WSY',
            o.status              = 'S_OS_CANCELED'
        WHERE o.id = #{orderId}
          AND o.status = 'S_OS_UNPAID'
    </update>

    <update id="deleteOrder">
        UPDATE t_t_order o
        SET o.updated            = NOW(),
            o.memberDeleteStatus = 0
        WHERE o.id = #{orderId}
          AND o.memberId = #{memberId}
          AND o.status IN ('S_OS_CANCELED', 'S_OS_DONE')
          AND o.memberDeleteStatus = 1
    </update>

    <resultMap id="queryCouponAuto" type="com.lewei.eshop.common.vo.coupon.CouponAutoDetailVo">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="isNewMember" column="isNewMember"/>
        <result property="status" column="status"/>
        <result property="minTradeMoney" column="minTradeMoney"/>
        <result property="maxTradeMoney" column="maxTradeMoney"/>
        <result property="tradeCount" column="tradeCount"/>
        <result property="minTradeCount" column="minTradeCount"/>
        <result property="maxTradeCount" column="maxTradeCount"/>
        <collection property="couponAttrs" ofType="com.lewei.eshop.common.vo.coupon.CouponAutoDetailVo$CouponAttr">
            <result property="couponId" column="couponId"/>
            <result property="num" column="num"/>
        </collection>
    </resultMap>

    <select id="queryCouponAuto" resultMap="queryCouponAuto">
        SELECT ca.id,
        ca.title,
        ca.isNewMember,
        ca.status,
        ca.minTradeMoney,
        ca.maxTradeMoney,
        ca.tradeCount,
        ca.minTradeCount,
        ca.maxTradeCount,
        caa.couponId,
        caa.num
        FROM t_pl_coupon_auto ca
        LEFT JOIN t_pl_coupon_auto_attr caa ON ca.id = caa.autoId
        WHERE ca.chainId = #{chainId}
        AND ca.dataStatus = 1
        AND ca.status = 'S_MAT_JXZ'
        <if test="isNewMember == true">
            AND ca.isNewMember = #{isNewMember}
        </if>
        <if test="isNewMember == false">
            AND ca.minTradeMoney &lt;= #{tradeMoney}
            AND ca.maxTradeMoney &gt;= #{tradeMoney}
            AND (ca.tradeCount OR
            (ca.minTradeCount &lt;= #{tradeCount}
            AND ca.maxTradeCount &gt;= #{tradeCount}
            ))
        </if>
    </select>

    <resultMap id="queryCouponList" type="com.lewei.eshop.common.vo.coupon.CouponListVo">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="preferentialType" column="preferentialType"/>
        <result property="preferentialTypeDesc" column="preferentialTypeDesc"/>
        <result property="applyTenantNum" column="applyTenantNum"/>
        <result property="couponStatus" column="couponStatus"/>
        <result property="couponStatusDesc" column="couponStatusDesc"/>
        <result property="totalNum" column="totalNum"/>
        <result property="leftNum" column="leftNum"/>
        <result property="receivedNum" column="receivedNum"/>
        <result property="usedNum" column="usedNum"/>
        <result property="isAtLeast" column="isAtLeast"/>
        <result property="atLeast" column="atLeast"/>
        <result property="benefit" column="benefit"/>
        <result property="dateType" column="dateType"/>
        <result property="beginTime" column="beginTime"/>
        <result property="endTime" column="endTime"/>
        <result property="fixedTerm" column="fixedTerm"/>
        <result property="isNewMember" column="isNewMember"/>
        <result property="isAtMost" column="isAtMost"/>
        <result property="atMost" column="atMost"/>
    </resultMap>

    <select id="queryCouponList" resultMap="queryCouponList">
        SELECT c.id,c.title,c.preferentialType,sc.sysName preferentialTypeDesc ,count(distinct ct.id)
        applyTenantNum,c.couponStatus,sc1.sysName couponStatusDesc,
        c.totalNum,c.leftNum,(c.totalNum - c.leftNum) receivedNum,COUNT(distinct mc.id)
        usedNum,c.isAtLeast,c.atLeast,c.benefit,c.dateType,c.beginTime,
        c.endTime,c.fixedTerm
        FROM t_pl_coupon c
        LEFT JOIN t_b_sys_code sc ON sc.sysCode = c.preferentialType
        LEFT JOIN t_b_sys_code sc1 ON sc1.sysCode = c.couponStatus
        LEFT JOIN t_pl_coupon_spu cs on c.id = cs.couponId
        LEFT JOIN t_pl_coupon_tenant ct on c.id = ct.couponId
        LEFT JOIN t_pl_member_coupon mc ON mc.baseCouponId = c.id AND mc.dataStatus = 1 AND mc.memberCouponStatus =
        'S_MCDS_YSY'
        WHERE c.chainId = #{chainId}
        <if test="couponIds != null and couponIds.size() > 0">
            AND c.id IN
            <foreach collection="couponIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY c.id
        ORDER BY c.created
    </select>

    <select id="queryBoxTbTicket" resultType="java.lang.String">
        SELECT tbSkuId
        FROM t_t_product_box_item pbi
                 LEFT JOIN t_t_product_box pb ON pbi.spuId = pb.spuId
                 LEFT JOIN t_pl_recharge_tb_config rtc ON rtc.id = pb.tbConfigId
        WHERE pbi.id = #{boxItemId}
          AND rtc.dataStatus = 1
    </select>

    <select id="queryProductTreasureReward"
            resultType="com.lewei.eshop.common.vo.product.treasure.MaProductTreasureRewardVo">
        SELECT ptr.id            rewardId,
               ptr.spuItemId,
               ptr.skuItemId,
               spu.name,
               sku.mainImage,
               sku.recoveryFee,
               sku.priceFee,
               ptr.primeCostFee,
               pbrc.id           rewardCategoryId,
               pbrc.categoryName rewardCategoryName,
               pbrc.pic          rewardCategoryPic,
               ptr.odds,
               ptr.showOdds
        FROM t_t_product_treasure_reward ptr
                 LEFT JOIN t_t_product_spu spu ON ptr.spuItemId = spu.id
                 LEFT JOIN t_t_product_sku sku ON ptr.skuItemId = sku.id
                 LEFT JOIN t_t_product_box_reward_category pbrc ON pbrc.id = ptr.categoryId
        WHERE ptr.chainId = #{chainId}
          AND ptr.spuId = #{spuId}
    </select>
</mapper>