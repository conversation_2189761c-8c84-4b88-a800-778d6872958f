cem40001=Activity reward
cem40002=Failed to save the form data
cem40003=Mailbox authentication
cem40004=Consumption restriction：Re consumption
cem40005=TopUpLimit：Re charge
cem40006=yuan participate
cem40007=Only newcomers can participate
cem40008=Sharing restriction：Then share friends for consumption
cem40009=Sharing restriction：Share the new person's visit again
cem40010=Sharing restriction：Share friend visit again
cem40011=time participate
cem40012=The box does not exist
cem40013=Sign in
cem40014=Sign-in gift distribution failed：
cem40015=Sign-in processing member growth value failed error = {}
cem40016=Failed to sign in to process the member title task error = {}
cem40017=Battle gift
cem40018=War income
cem40019=Balance withdrawal
cem40020=Blind box combat
cem40021=War consumption
cem40022=The application for withdrawal channel is wrong or the default payment method is empty. Automatic payment cannot be made
cem40023=Member not found
cem40024=Black market not found
cem40025=Black market auction：
cem40026=Black market auction
cem40027=etc
cem40028=The bid was not found
cem40029=Member does not exist
cem40030=There is no black market
cem40031=Black market transfer：
cem40032=Black market refund：
cem40033=The reward does not exist
cem40034=Box does not exist, please refresh and try again
cem40035=Wait for a few products
cem40036=Small routine
cem40037=APP
cem40038=Thread pool
cem40039=in，The total number of tasks is
cem40040= ，The number of completed tasks is
cem40041= ，The number of tasks currently being processed is
cem40042= ，The number of tasks in the buffer queue is
cem40043=No sec kill activity found, please refresh and try again
cem40044=This product does not participate in the sec kill activity
cem40045=time
cem40046=Buy All
cem40047=Check that the purchased quantity is not exceeded
cem40048=Detected that you have not spent enough times
cem40049=The self-built award
cem40050=Build your own blind box
cem40051=Buy
cem40052=Order does not exist
cem40053=Unsupported period type
cem40054=Information not found
cem40055=If the mobile phone number has multiple users, contact customer service
cem40056=If the email address has multiple users, contact customer service personnel
cem40057=Sensitive word call failed error = {}
cem40058=The identity information of the user does not match, so the identity information cannot be queried
cem40059=The user may have a special status such as household registration transfer or soldier
cem40060=The user is underage.
cem40061=Verification fails
cem40062=The link does not exist or is invalid
cem40063=New Member gift
cem40064=Unknown
cem40065=Sign-in event
cem40066=Full decrement activity
cem40067=Activity not started
cem40068=The exchange failed, the exchange code is wrong
cem40069=The conversion failed. The conversion code is invalid
cem40070=Welfare code
cem40071=Worship
cem40072=BeWorshipped
cem40073=The buyer has initiated a refund application and is waiting for the merchant to process it
cem40074=The value cannot be greater than the maximum number of returnable items
cem40075=Some service items have been verified and used, please check the number of applications
cem40076=Refund and return
cem40077=Refund only
cem40078=After-sale mode：
cem40079=，Reason for refund：
cem40080=，Refund amount：
cem40081=，Quantity of returned goods：
cem40082=，explain：
cem40083=A refund application was initiated and is waiting for the merchant to process it
cem40084=Submit an application
cem40085=The refund document does not exist
cem40086=The buyer reapplied for a refund
cem40087=Reapply for a refund
cem40088=The buyer has returned the goods and is waiting for the merchant's confirmation of receipt
cem40089=Express name：
cem40090=，Tracking number：
cem40091=，explain：
cem40092=Have returned the goods, waiting for the merchant to confirm receipt
cem40093=Buyer rescinds request for refund
cem40094=Withdrawal of application
cem40095=Cause of revocation：
cem40096=0yuan
cem40097=yuan
cem40098=No reward found
cem40099=Wait for a few products
cem40100=Consign for sale on commission
cem40101=The reward was not found
cem40102=Parameter exception
cem40103=The exchange record does not exist
cem40104=No treasure map found
cem40105=Grade synthesis
cem40106=The activity has expired. Please refresh and try again
cem40107=The pledge zone giveaway
cem40108=Battle cost - Refund
cem40109=Black market price refund
cem40110=Delivery failure, freight refund balance===》orderId = {}
cem40111=Delivery failure, freight refund balance
cem40112=Member level task consumption processing error error = {}
cem40113= Refund on failed purchase
cem40114=Failed to apply for refund for purchase of self-built rewards orderId={},e={}
cem40115=Self-built reward income
cem40116=Self-built reward purchase failed --> refund orderId : {}
cem40117=Self-built purchase exception error
cem40118=There is a problem with the merchant certification
cem40119=Express delivery
cem40120=Pay attention to you
cem40121=Forwarded you
cem40122=Commented on you
cem40123=Reply to you
cem40124=Praise you
cem40125=I liked your comment
cem40126=Call text content to detect interface exceptions
cem40127=Acquired lock key:
cem40128=####################Lockwait timeout key: 
cem40129=Black market deduction：
cem40130=Commodity data cache clearing key : {}
cem40131=Failed to sign in to process the member title task error = {}
cem40132=Invitation frequency processing error
cem40133=Un found activity！
cem40134=The member complaint information is as follows：
cem40135=Name of franchisee：
cem40136=Member's mobile phone number:
cem40137=Type of complaint:
cem40138=Content of complaint:
cem40139=Play payment callback error
cem40140=Self-built reward payment callback abnormal --> Box status anomaly order : {}
cem40141=Self-built reward payment callback abnormal --> Purchase failure order : {}
cem40142=No delivery warehouse is set up chainId: {}
cem40143=Too many queues max = {}, Prohibit putting into queue, redisQueue={}
cem40144=【Queue】redisQueue={} Number of current queues：{}, orderId {} orderSn {} ====>
cem40145=System kernel：
cem40146=The data to update was not found
cem40147=No record found
cem40148=Unsupported payment methods
cem40149=Order not found or order status error
cem40150=No data was found to update
cem40151=Hello
cem40152=In order to better serve you, please return to the registration or reset password page within 5 minutes, fill in the following verification code, and verify your email
cem40153=If you did not register or reset your password, ignore this message.
cem40154=Looking forward to serving you again.
cem40155=War restrictions: re participating in war consumption
cem40156=Failed to process member growth value error = {}
cem40157=DIY blind box
cem40158=DIY blind box restriction: participating in DIY blind box consumption again
cem40159=Send red package
cem40160=Receive red package
cem40161=Settlement amount transferred in
cem40162=Fetching game
cem40163=Give back to friends
cem40164=Friend feedback
cem40165=Receive mutual gifts from friends
cem40166=Income balance transferred
cem40167=Income balance transferred out
cem40168=synthetic return
cem40169=synthetic gift
cem40170=Mentioned you
cem40171=Partial disclosure
cem40172=All publicly available
cem40173=Not publicly available at all
cem40174=Bestiary exchange
cem40175=No product information found
cem40176=Collision game
cem40177=Voting rewards
cem40179=Different space