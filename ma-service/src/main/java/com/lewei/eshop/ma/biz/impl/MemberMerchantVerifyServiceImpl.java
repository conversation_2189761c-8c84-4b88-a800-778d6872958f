package com.lewei.eshop.ma.biz.impl;

import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.request.member.SaveMemberMerchantVerifyRequest;
import com.lewei.eshop.common.request.member.UpdateAccountInfoRequest;
import com.lewei.eshop.common.request.pub.IsVerifyCodeRequest;
import com.lewei.eshop.common.request.pub.VerifyCodeRequest;
import com.lewei.eshop.entity.member.MemberMerchantVerify;
import com.lewei.eshop.ma.SysConfig;
import com.lewei.eshop.ma.biz.IMemberMerchantVerifyService;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.client.paas.PayFeignClient;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.expression.Restrictions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/09/26
 */
@Service
@Transactional
@Slf4j
public class MemberMerchantVerifyServiceImpl implements IMemberMerchantVerifyService {
    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private RedisCacheProvider redisCacheProvider;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private PayFeignClient payFeignClient;
    @Autowired
    private SysConfig sysConfig;

    @Override
    public void saveMemberMerchantVerify(SaveMemberMerchantVerifyRequest request, Long memberId) {
        this.verifyCode(request.getCode(), request.getMobile());
        // 商户进件（微信-小微商户）
        request.getApplementWxMicroRequest().setNotifyUrl(sysConfig.getEshopService() + "/api/ma/member/merchant/verify/callback");
        Map businessCode = payFeignClient.applymentWxMicro(request.getApplementWxMicroRequest());
        if (businessCode != null){
            MemberMerchantVerify memberMerchantVerify = new MemberMerchantVerify();
            BeanUtils.copyProperties(request,memberMerchantVerify);
            memberMerchantVerify.setMemberId(memberId);
            memberMerchantVerify.setBusinessState(false);
            memberMerchantVerify.setCreated(DateFormatUtils.getNow());
            memberMerchantVerify.setCreateBy(memberId);
            memberMerchantVerify.setDataStatus(true);
            memberMerchantVerify.setBusinessCode((String) businessCode.get("businessCode"));
            dao.save(memberMerchantVerify);
        }
        this.deleteCode(request.getMobile());
    }

    @Override
    public Map queryApplyStatus(Long memberId) {
        Map resultMap = new HashMap<>();
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("memberId", memberId))
                .and(Restrictions.eq("dataStatus", 1));
        MemberMerchantVerify memberMerchantVerify =  dao.query(query, MemberMerchantVerify.class);
        if (memberMerchantVerify != null){
            resultMap = payFeignClient.applymentWx(memberMerchantVerify.getBusinessCode());
        }
        return resultMap;
    }

    /**
     * 发送验证码
     */
    @Override
    public void queryVerifyCode(String mobile) {
        // 生成验证码
        VerifyCodeRequest verifyCodeRequest = new VerifyCodeRequest();
        verifyCodeRequest.setMobile(mobile);
        verifyCodeRequest.setTime(3);
        publicFeignClient.generateVerifyCode(verifyCodeRequest);
    }

    /**
     * 验证验证码
     * @param code 验证码
     */
    public void verifyCode(String code, String mobile) {
        // 验证验证码
        IsVerifyCodeRequest isVerifyCodeRequest = new IsVerifyCodeRequest();
        isVerifyCodeRequest.setMobile(mobile);
        isVerifyCodeRequest.setCode(code);
        publicFeignClient.verifyCode(isVerifyCodeRequest);
    }

    /**
     * 删除验证码
     */
    public void deleteCode(String mobile) {
        // 删除验证码
        VerifyCodeRequest verifyCodeRequest = new VerifyCodeRequest();
        verifyCodeRequest.setMobile(mobile);
        publicFeignClient.deleteCode(verifyCodeRequest);
    }

    @Override
    public Map applymentWxPersonalBanks(Integer pageNo, Integer pageSize) {
        return payFeignClient.applymentWxPersonalBanks(pageNo, pageSize);
    }

    @Override
    public Map applymentWxMicroSettlement(Long payConfigId) {
        Map map = new HashMap<>();
        if (payConfigId != null){
            map = payFeignClient.applymentWxMicroSettlement(payConfigId);
        }
        return map;
    }

    @Override
    public void applymentWxMicroAccountInfo(UpdateAccountInfoRequest request) {
        payFeignClient.applymentWxMicroAccountInfo(request);
    }
}
