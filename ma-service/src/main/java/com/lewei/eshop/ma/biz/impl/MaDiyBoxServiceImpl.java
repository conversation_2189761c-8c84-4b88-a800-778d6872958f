package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.auth.ma.SSOMaAppCache;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.OrderUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.member.MemberBalanceTradeTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.diybox.DiyBoxPayReq;
import com.lewei.eshop.common.request.diybox.DiyBoxPrepayReq;
import com.lewei.eshop.common.request.diybox.QueryMaDiyBoxSpuReq;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.vo.diybox.DiyboxResultVo;
import com.lewei.eshop.common.vo.diybox.MaDiyBoxPrepayVo;
import com.lewei.eshop.common.vo.diybox.MaDiyBoxSpuDetailVo;
import com.lewei.eshop.common.vo.diybox.MaDiyBoxConfigSimpleVo;
import com.lewei.eshop.entity.diybox.types.DiyBoxPrizeTypeEnum;
import com.lewei.eshop.entity.member.types.MemberAuthorityEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.OrderProduct;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaDiyBoxService;
import com.lewei.eshop.ma.biz.IMaMemberService;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.lewei.eshop.ma.pay.DiyBoxCallbackProcessor;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.sso.MemberCache;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/17
 */
@Service
public class MaDiyBoxServiceImpl implements IMaDiyBoxService {

    private final String callBackUrl = "/api/ma/pay/diybox/callback";

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private DiyBoxCallbackProcessor diyBoxCallbackProcessor;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private IMaMemberService memberService;

    @Override
    public MaDiyBoxConfigSimpleVo queryMaDiyBoxConfigSimpleVo() {

        Ssqb queryMaDiyBoxConfigSimpleVo = Ssqb.create("com.lewei.eshop.ma.diybox.queryMaDiyBoxConfigSimpleVo");
        return dao.findForObj(queryMaDiyBoxConfigSimpleVo, MaDiyBoxConfigSimpleVo.class);

    }

    @Override
    public Pagination queryMaDiyBoxSpus(Long configId,QueryMaDiyBoxSpuReq req) {
        Ssqb queryMaDiyBoxSpus = Ssqb.create("com.lewei.eshop.ma.diybox.queryMaDiyBoxSpus")
                .setParam("pageNo",req.getPageNo())
                .setParam("pageSize",req.getPageSize())
                .setParam("queryKey",req.getQueryKey())
                .setParam("order",req.getOrder())
                .setParam("orderBy",req.getOrderBy())
                .setParam("configId",configId)
                ;
        return dao.findForPage(queryMaDiyBoxSpus);
    }


    @Override
    public MaDiyBoxPrepayVo diyBoxPrepay(DiyBoxPrepayReq request, Long memberId) {
        // 不可参与DIY盲盒
        boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_PART_DIY_BOX, memberId);
        if (isAuthority) {
            throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_PART_DIY_BOX);
        }
        MaDiyBoxConfigSimpleVo configSimpleVo = this.queryMaDiyBoxConfigSimpleVo();
        if (configSimpleVo == null || BooleanUtils.isNotTrue(configSimpleVo.getIsOpen())) {
            throw new BizCoreRuntimeException(MaErrorConstants.DIY_BOX_IS_NOT_OPEN);
        }
        if(BooleanUtils.isTrue(configSimpleVo.getIsGuaranteed()) && request.getSmallPrize() == null){
            throw new BizCoreRuntimeException(MaErrorConstants.SMALL_PRIZE_IS_NULL);
        }
        if (BooleanUtils.isNotTrue(configSimpleVo.getIsGuaranteed())){
            request.setSmallPrize(null);
        }
        if (request.getBigPrize().getOdds().compareTo(BigDecimal.valueOf(0.8))> 0){
            throw new BizCoreRuntimeException(MaErrorConstants.BIG_PRIZE_ODDS_ERROR);
        }

        if (request.getBigPrize().getOdds().compareTo(configSimpleVo.getMinimumOdds()) < 0){
            throw new BizCoreRuntimeException(MaErrorConstants.DIY_BOX_PRIZE_ODDS_ERROR,configSimpleVo.getMinimumOdds().multiply(BigDecimal.valueOf(100)));
        }

        if (request.getSmallPrize() != null){
            if (request.getSmallPrize().getOdds().add(request.getBigPrize().getOdds()).compareTo(BigDecimal.ONE) != 0){
                throw new BizCoreRuntimeException(MaErrorConstants.DIY_BOX_PRIZE_ODDS_ERROR);
            }
        }

        List<MaDiyBoxSpuDetailVo> spuDetailVos = new ArrayList<>();
        MaDiyBoxSpuDetailVo bigPrize =  queryBoxSpuDetail(request.getBigPrize().getSkuId());
        bigPrize.setPrizeType(DiyBoxPrizeTypeEnum.big.value());
        bigPrize.setOdds(request.getBigPrize().getOdds());
        bigPrize.setShowOdds(request.getBigPrize().getOdds());
        spuDetailVos.add(bigPrize);

        if (request.getSmallPrize() != null){
            MaDiyBoxSpuDetailVo smallPrize =  queryBoxSpuDetail(request.getSmallPrize().getSkuId());
            smallPrize.setPrizeType(DiyBoxPrizeTypeEnum.small.value());
            smallPrize.setOdds(request.getSmallPrize().getOdds());
            smallPrize.setShowOdds(request.getSmallPrize().getOdds());
            spuDetailVos.add(smallPrize);
            if (smallPrize.getPrimeCostFee().compareTo(bigPrize.getPrimeCostFee()) >= 0){
                throw new BizCoreRuntimeException(MaErrorConstants.BIG_PRIZE_FEE_LESS_THAN_SMALL_PRIZE_FEE);
            }
        }

        //实际成本价之和  +=  （成本*概率）
        BigDecimal totalPrimeCostFee =  BigDecimal.ZERO;
        for (MaDiyBoxSpuDetailVo spuDetailVo : spuDetailVos) {
            totalPrimeCostFee = totalPrimeCostFee.add(spuDetailVo.getPrimeCostFee().multiply(spuDetailVo.getOdds()));
        }
        //售价 = 实际成本价之和*（1+利润）
        BigDecimal price = totalPrimeCostFee.multiply((BigDecimal.ONE.add(configSimpleVo.getProfit()))).setScale(2, RoundingMode.HALF_UP);

        if (price.compareTo(BigDecimal.ZERO) == 0){
            price = BigDecimal.valueOf(0.01);
        }

        BigDecimal priceBalance =  price.multiply(publicFeignClient.queryChainConfig().getMoneyRatio());

        MaDiyBoxPrepayVo maDiyBoxPrepayVo = new MaDiyBoxPrepayVo();
        List<String> numList = Arrays.asList(configSimpleVo.getNums().split(","));
        maDiyBoxPrepayVo.setNums(numList);
        maDiyBoxPrepayVo.setPrice(price);
        maDiyBoxPrepayVo.setPriceBalance(priceBalance);
        maDiyBoxPrepayVo.setMaDiyBoxSpuDetailVos(spuDetailVos);

        return maDiyBoxPrepayVo;
    }

    @Override
    public SaveOrderVO diyBoxPay(DiyBoxPayReq payReq, MemberCache memberCache, SSOMaAppCache appCache, Long chainId) {
        DiyBoxPrepayReq prepayReq = new DiyBoxPrepayReq();
        prepayReq.setBigPrize(payReq.getBigPrize());
        prepayReq.setSmallPrize(payReq.getSmallPrize());

        MaDiyBoxPrepayVo prepayVo =  this.diyBoxPrepay(prepayReq,memberCache.getId());

        if (prepayVo.getPrice().compareTo(payReq.getPrice()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }

        if (!prepayVo.getNums().contains(payReq.getNum().toString())){
            throw new BizCoreRuntimeException(MaErrorConstants.DIY_BOX_NUM_ERROR);
        }

        BigDecimal totalPrice = prepayVo.getPrice().multiply(BigDecimal.valueOf(payReq.getNum()));


        MaDiyBoxSpuDetailVo bigPrize = prepayVo.getMaDiyBoxSpuDetailVos().stream().filter(a-> Objects.equals(a.getPrizeType(),DiyBoxPrizeTypeEnum.big.value())).collect(Collectors.toList()).get(0);

        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberCache.getId());
        order.setOpenId(memberCache.getOpenId());
        order.setPaymentMethod(payReq.getPaymentMethod());
        order.setOrderMoney(totalPrice);
        order.setPaymentMoney(totalPrice);

        order.setOrderTitle(bigPrize.getName());
        order.setOrderType(OrderEnum.S_OOT_DIY_BOX.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberCache.getId());
        order.setPlatform(appCache.getPlatform());
        order.setOrderTradeType(OrderTradeTypeEnum.S_OTT_TRADE.value());

        Map<String,Object> map = new HashMap<>();
        map.put("diyBoxInfo",prepayVo);
        map.put("num",payReq.getNum());
        order.setExtJson(JSONObject.toJSONString(map));
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payReq.getPaymentMethod())){
            order.setOrderBalance(prepayVo.getPriceBalance());
        }

        dao.save(order);


        OrderProduct orderProduct = new OrderProduct();
        orderProduct.setOrderId(order.getId());
        orderProduct.setSpuId(bigPrize.getSpuId());
        orderProduct.setSkuId(bigPrize.getSkuId());
        orderProduct.setProductName(bigPrize.getName());
        orderProduct.setMainImage(bigPrize.getMainImage());
        orderProduct.setCurrentUnitPrice(payReq.getPrice());
        orderProduct.setQuantity(payReq.getNum());
        orderProduct.setTotalPrice(totalPrice);
        orderProduct.setCreated(DateFormatUtils.getNow());
        orderProduct.setCreateBy(order.getMemberId());
        orderProduct.setPrimeCostFee(BigDecimal.ZERO);
        dao.save(orderProduct);

        SaveOrderVO returnVo = new SaveOrderVO();
        returnVo.setOrderId(order.getId());

        if (OrderUtil.isCashPay(payReq.getPaymentMethod())) {
            returnVo.setOrderId(order.getId());
            Map<String, Object> extMap = new HashMap<>();
            extMap.put("memberBankId", payReq.getMemberBankId());
            extMap.put("orderId", order.getId());
            //调取微信支付
            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), memberCache.getOpenId(), chainId, appCache.getAppId(),payReq.getPaymentMethod(),
                    callBackUrl, null, payReq.getPayMethod(), "default",memberCache.getMobile(),extMap);
            returnVo.setPayUrl(payUrl);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payReq.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(order.getPaymentMoney());
            // 买家
            balanceRequest.setMemberId(memberCache.getId());
            balanceRequest.setPlId(order.getId());
            // 查询卖家
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40157"));
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_DIYBOX.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterPay(order);
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }
        return returnVo;
    }

    @Override
    public List<DiyboxResultVo> queryDiyboxResultVo(Long orderId) {
        Ssqb queryDiyboxResultVo = Ssqb.create("com.lewei.eshop.ma.diybox.queryDiyboxResultVo")
                .setParam("orderId",orderId);
        return dao.findForList(queryDiyboxResultVo, DiyboxResultVo.class);
    }

    @Override
    public Pagination queryDiyboxRecordVo(Long memberId, BigDecimal sg, BigDecimal eg, String prizeType, Integer pageNo, Integer pageSize) {
        return memberFeignClient.queryMemberDiyBoxRewardLogs(memberId,null,null,null,null,null,sg,eg,prizeType,pageNo,pageSize);
    }

    private MaDiyBoxSpuDetailVo  queryBoxSpuDetail(Long skuId){
        Ssqb queryBoxSpuDetail = Ssqb.create("com.lewei.eshop.ma.diybox.queryBoxSpuDetail")
                .setParam("skuId",skuId);
        return dao.findForObj(queryBoxSpuDetail, MaDiyBoxSpuDetailVo.class);
    }

    private void afterPay(Order order) {
        taskExecutor.execute(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderCode", order.getOrderSn());
            diyBoxCallbackProcessor.onPayTradeSuccess(order, jsonObject);
        });
    }

}
