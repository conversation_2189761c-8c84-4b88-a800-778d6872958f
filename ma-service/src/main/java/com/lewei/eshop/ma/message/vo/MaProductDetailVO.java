package com.lewei.eshop.ma.message.vo;

import com.lewei.eshop.common.i18n.annotation.I18nSysCode;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/1/10
 */
@Data
public class MaProductDetailVO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 父spuId
     */
    private Long parentSpuId;

    /**
     * 商品名称(spu)
     */
    private String name;

    /**
     * 分类Id
     */
    private Long categoryId;

    /**
     * 轮播图片
     */
    private String spuBannerImage;

    /**
     * 商品介绍主图
     */
    private String spuMainImage;
    /**
     * 原价
     */
    private BigDecimal  spuOriginalPrice;
    /**
     * 围观人数
     */
    private Long lookerNum;
    /**
     * 盲盒类型
     */
    private String  boxType;
    /**
     * 盲盒类型
     */
    @I18nSysCode
    private String boxTypeName;
    /**
     * 显示模式  S_BDM_LIMIT/盲盒  S_BDM_GASHAPON_MACHINE/扭蛋机
     */
    private String boxDisplayMode;
    /**
     * 原价
     */
    private String  backgroundImage;


    /**
     * spu 售价
     */
    private BigDecimal spuPriceFee;

    /**
     * spu 成本价
     */
    private BigDecimal spuCostPriceFee;

    /**
     * 分享描述
     */
    private String shareDesc;

    /**
     * 卖点
     */
    private String sellingPoint;

    /**
     * 商品详情(介绍)
     */
    private String productIntro;
    /**
     *状态
     */
    private String status;
    /**
     * 小程序是否展示
     */
    private Boolean onlineShopShow;
    /**
     * app是否展示
     */
    private Boolean appOnlineShopShow;

    /**
     * 商品销量
     */
    private Integer salesCount;
    /**
     * 提取方式
     */
    private String logisticsMode;

    /**
     * 类型
     */
    private String cardType;
    /**
     * 有效期
     */
    private Integer duration;
    /**
     * 次数
     */
    private Integer times;

    private Integer selectNum;
    /**
     * 是否收藏
     */
    private Boolean isCollected;
    /**
     * 回收价
     */
    private BigDecimal recoveryFee;
    /**
     * 类型
     */
    private String spuType;
    /**
     * 剩余盲盒套盒数
     */
    private Integer leftBoxItemNum;

    private String extJson;
    /**
     * 是否是卡牌
     */
    private Boolean isCard;

    private List<CardItem> cardItems;

    /**
     * 规格与属性
     */
    private List<MaAttr> attrs;

    /**
     * sku集合
     */
    private List<MaSku> skus;

    /**
     * 产品分类
     */
    private List<Category> categories = new ArrayList<>();

    /**
     * sku与attr和attrValue的对应关系
     */
    private List<MaSkuAttrAndValueMap> skuAttrAndValueMaps;

    @Data
    public static class MaAttr {

        /**
         * 规格主键
         */
        private Long attrId;

        /**
         * 规格名称
         */
        private String attrName;

        /**
         * 规格描述
         */
        private String attrDesc;

        /**
         * 属性
         */
        private List<MaAttrValue> attrValues;

        @Data
        public static class MaAttrValue {

            /**
             * 属性主键
             */
            private Long attrValueId;

            /**
             * 商品属性值
             */
            private String attrValueName;

            /**
             * 商品属性值描述
             */
            private String attrValueDesc;
        }
    }

    @Data
    public static class MaSku {

        /**
         * SKU ID
         */
        private Long skuId;

        private Long parentSkuId;

        /**
         * 销售属性值{attr_value_id}-{attr_value_id}
         */
        private String attrs;

        /**
         * 轮播图
         */
        private String bannerImage;

        /**
         * 主图
         */
        private String mainImage;
        /**
         * 原价
         */
        private BigDecimal  originalPrice;


        /**
         * 售价
         */
        private BigDecimal priceFee;
        /**
         * 折扣价
         */
        private BigDecimal discountedPrice;

        /**
         * 成本价
         */
        private BigDecimal costPriceFee;

        /**
         * 库存
         */
        private Integer quantity;

        /**
         * 商品编码
         */
        private String productCode;

        /**
         * 库存上限
         */
        private Integer quantityLimit;

        /**
         * 服务时长
         */
        private Integer serviceTime;
        /**
         * 服务时长单位
         */
        private String unit;
        /**
         * 服务时长单位描述
         */
        @I18nSysCode
        private String unitDesc;
        /**
         * 重量
         */
        private BigDecimal weight;

        /**
         * 自购佣金 金额
         */
        private BigDecimal selfBuyProfitAmount;

        /**
         * 直接下级会员购买时，获得的奖励(分享收益)
         */
        private BigDecimal memberBuyProfitAmount;

        private Boolean isSecKill;
    }

    @Data
    public static class MaSkuAttrAndValueMap {

        /**
         * SKU ID
         */
        private Long mapSkuId;

        private List<MapAttrAndValue> mapAttrAndValues;

        @Data
        public static class MapAttrAndValue {
            /**
             * 商品属性ID
             */
            private Long attrId;

            /**
             * 商品属性值ID
             */
            private Long attrValueId;
        }
    }

    @Data
    public static class CardItem {
        /**
         * 关联服务id
         */
        private Long spuItemId;

        private Long skuItemId;

        /**
         * 主图
         */
        private String mainImage;

        /**
         * 名称
         */
        private String name;
        /**
         * 规格名称
         */
        private String attrValues;

        /**
         * 次数
         */
        private Integer times;
    }




    @Data
    public static class Category {

        private Long categoryId;

        private String categoryName;

        private Long parentId;

        private String extJson;
    }
}
