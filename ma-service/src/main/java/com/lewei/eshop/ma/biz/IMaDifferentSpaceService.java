package com.lewei.eshop.ma.biz;

import com.lewei.eshop.common.request.different.DifferentSpaceDrawRequest;
import com.lewei.eshop.common.request.different.DifferentSpaceRecordRequest;
import com.lewei.eshop.common.vo.different.MaDifferentSpaceRewardVo;
import com.lewei.eshop.common.vo.different.MaDifferentSpaceVo;
import com.lewei.eshop.entity.space.DifferentSpaceRewardRecord;
import com.xcrm.common.page.Pagination;

import java.util.List;

/**
 * 异空间接口层
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
public interface IMaDifferentSpaceService {
    /**
     * 查询异空间奖品
     * @param differentSpaceId
     * @return
     */
    List<MaDifferentSpaceRewardVo> queryDifferentSpaceReward(Long differentSpaceId);

    /**
     * 查询异空间信息
     * @param spuId
     * @return
     */
    MaDifferentSpaceVo queryDifferentSpace(Long spuId, Long memberId);

    /**
     * 抽奖
     * @param request
     * @param memberId
     */
    MaDifferentSpaceRewardVo drawDifferentSpace(DifferentSpaceDrawRequest request, Long memberId);

    /**
     *
     * 查询异空间记录
     * @param request
     * @param memberId
     * @return
     */
    Pagination queryDifferentSpaceRewardRecord(DifferentSpaceRecordRequest request, Long memberId);
}
