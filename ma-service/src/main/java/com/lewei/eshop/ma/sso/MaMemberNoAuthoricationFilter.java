package com.lewei.eshop.ma.sso;

import com.alibaba.fastjson.JSON;
import com.lewei.eshop.auth.ma.MaJerseyConstants;
import com.lewei.eshop.auth.ma.SSOMaAppCache;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.jersey.common.JerseyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ContainerResponseFilter;
import java.io.IOException;

import static com.lewei.eshop.cache.CacheKeys.MEMBER_AUTH_CACHE_KEY;

/**
 * 验证小程序member合法性
 *
 *
 * <AUTHOR>
 * @date 2020年01月09日
 */
@Slf4j
public class MaMemberNoAuthoricationFilter implements ContainerRequestFilter, ContainerResponseFilter {


    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private RedisCacheProvider redisCacheProvider;

    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {

        this.queryMemberCache(requestContext);

    }

    @Override
    public void filter(ContainerRequestContext requestContext, ContainerResponseContext responseContext)
            throws IOException {

    }

    protected MemberCache queryMemberCache(ContainerRequestContext requestContext){
        String openId = requestContext.getHeaderString(MaJerseyConstants.HTTP_HEADER_OPEN_ID);
        MemberCache member = null;
        if (StringUtils.isNotBlank(openId)) {
            SSOMaAppCache ssoMaAppCache = (SSOMaAppCache)requestContext.getProperty(JerseyConstants.REQ_SSO_USER_APP);
            member = redisCacheProvider.getFromJson(MEMBER_AUTH_CACHE_KEY + openId + "@" + ssoMaAppCache.getChainId(), MemberCache.class);
            if(member == null) {
                Ssqb memberQuery = Ssqb.create("com.lewei.eshop.ma.queryMemberCacheByOpenId")
                        .setParam("openId", openId);
                member = dao.findForObj(memberQuery, MemberCache.class);
                if (member != null) {
                    member.setOpenId(openId);
                    redisCacheProvider.set(MEMBER_AUTH_CACHE_KEY + openId + "@" + ssoMaAppCache.getChainId(), JSON.toJSONString(member),  60L);
                }
            }
        }

        requestContext.setProperty(MaJerseyConstants.REQ_MEMBER, member);
        return member;
    }


}
