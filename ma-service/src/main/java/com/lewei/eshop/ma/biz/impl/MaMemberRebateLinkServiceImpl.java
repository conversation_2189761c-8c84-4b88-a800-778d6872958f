package com.lewei.eshop.ma.biz.impl;

import com.lewei.eshop.auth.annotation.RateLimiter;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.eachOther.MemberRebateConfirmRequest;
import com.lewei.eshop.common.request.eachOther.MemberRebateLinkRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.vo.eachOther.MemberRebateLinkVo;
import com.lewei.eshop.common.vo.member.MemberBalanceVo;
import com.lewei.eshop.entity.eachOther.MemberGiftLink;
import com.lewei.eshop.entity.eachOther.MemberRebateLink;
import com.lewei.eshop.entity.eachOther.types.LinkStatusEnum;
import com.lewei.eshop.entity.member.MemberBalance;
import com.lewei.eshop.entity.member.MemberRebateConfig;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaMemberRebateLinkService;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.global.IGlobalHandler;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.DateZonetimeUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 玩家回赠
 * <AUTHOR>
 * @date 2023/10/18
 */
@Service
@Transactional
@Slf4j
public class MaMemberRebateLinkServiceImpl implements IMaMemberRebateLinkService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    AppFeignClient appFeignClient;
    @Autowired
    private IGlobalHandler globalHandler;
    @Autowired
    private MemberFeignClient memberFeignClient;

    @Override
    @RateLimiter(isLoop = true)
    public Long saveMemberRebateLink(MemberRebateLinkRequest request, Long memberId, Long chainId) {
        // 回赠开关控制
        SaasQueryBuilder queryConfig = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1));
        MemberRebateConfig memberRebateConfig = dao.query(queryConfig, MemberRebateConfig.class);
        if (memberRebateConfig != null){
            if (BooleanUtils.isFalse(memberRebateConfig.getIsOpen())){
                throw new BizCoreRuntimeException(MaErrorConstants.GIFT_FUNCTION_FOR_REMOVE);
            }
            // 余额是否充足
            MemberBalanceVo balanceVo = memberFeignClient.getMemberBalance(memberId, null);
            BigDecimal totalBalance = balanceVo.getBalance().add(balanceVo.getGift());
            if (totalBalance.compareTo(request.getRebateMoney()) < 0){
                throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_BALANCE_NOT_ENOUGH);
            }
            // 是否是允许回赠用户
            if (BooleanUtils.isFalse(memberRebateConfig.getIsAllMember()) && StringUtils.isNotEmpty(memberRebateConfig.getMemberIds())){
                List<String> memberIds = Arrays.asList(memberRebateConfig.getMemberIds().split(","));
                if (!memberIds.contains(memberId.toString())) {
                    throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_NOT_CONFIG);
                }
            }
            // 不可回赠自己
            if (request.getMemberId().equals(memberId)) {
                throw new BizCoreRuntimeException(MaErrorConstants.CAN_NOT_REBATE_SELF);
            }
            Date now = DateFormatUtils.getNow();
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                    .and(Restrictions.gt("created", DateZonetimeUtils.getFirstTimeOfDay(now,globalHandler.getGlobalData().getZone())))
                    .and(Restrictions.lt("created", DateZonetimeUtils.getLastTimeOfDay(now,globalHandler.getGlobalData().getZone())))
                    .and(Restrictions.eq("giveMember", memberId));
            List<MemberRebateLink> memberRebateLinks = dao.queryList(queryBuilder, MemberRebateLink.class);
            // 回赠总次数
            if (memberRebateConfig.getRebateLimit() != null){
                int num = memberRebateLinks.size();
                if (num > 0 && memberRebateConfig.getRebateLimit().compareTo(num) <= 0){
                    throw new BizCoreRuntimeException(MaErrorConstants.REBATE_LINK_GIVE_NUM_CHECK);
                }
            }
            // 回赠总金额
            if (memberRebateConfig.getRebateMoney() != null && memberRebateConfig.getRebateMoney().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal totalMoney = BigDecimal.ZERO;
                for (MemberRebateLink m : memberRebateLinks) {
                    totalMoney = totalMoney.add(m.getRebateMoney());
                }
                if (totalMoney.compareTo(BigDecimal.ZERO) > 0) {
                    if (totalMoney.compareTo(memberRebateConfig.getRebateMoney()) >= 0) {
                        throw new BizCoreRuntimeException(MaErrorConstants.REBATE_LINK_GIVE_MONEY_CHECK);
                    }
                }
                if (request.getRebateMoney().compareTo(memberRebateConfig.getRebateMoney()) > 0) {
                    throw new BizCoreRuntimeException(MaErrorConstants.REBATE_LINK_GIVE_MONEY_CHECK);
                }
            }
        }else {
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REBATE_NOT_CONFIG);
        }

        MemberRebateLink memberRebateLink = new MemberRebateLink();
        memberRebateLink.setLinkId(request.getLinkId());
        memberRebateLink.setGiveMember(memberId);
        memberRebateLink.setGetMember(request.getMemberId());
        memberRebateLink.setLinkStatus(LinkStatusEnum.S_MLS_DLQ.value());
        memberRebateLink.setCreated(DateFormatUtils.getNow());
        memberRebateLink.setDataStatus(true);
        memberRebateLink.setRebateMoney(request.getRebateMoney());
        dao.save(memberRebateLink);
        return memberRebateLink.getId();
    }

    @Override
    @RateLimiter(isLoop = true)
    public void saveMemberRebateConfirm(MemberRebateConfirmRequest request, Long memberId, Long chainId) {
        MemberRebateLink memberRebateLink = dao.queryById(request.getLinkId(), MemberRebateLink.class);
        if (memberRebateLink != null) {
            // 处理余额（赠出）
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(memberRebateLink.getRebateMoney());
            balanceRequest.setMemberId(memberId);
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40163"));
            balanceRequest.setPlId(memberRebateLink.getId());
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 处理余额（受赠）
            balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(memberRebateLink.getRebateMoney());
            balanceRequest.setMemberId(memberRebateLink.getGetMember());
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40164"));
            balanceRequest.setPlId(memberRebateLink.getId());
            balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            memberRebateLink.setLinkStatus(LinkStatusEnum.S_MLS_YLQ.value());
            dao.update(memberRebateLink);

            MemberGiftLink memberGiftLink = dao.queryById(memberRebateLink.getLinkId(),MemberGiftLink.class);
            if (memberGiftLink != null) {
                memberGiftLink.setRebateMoney(memberRebateLink.getRebateMoney());
                memberGiftLink.setIsGave(true);
                dao.update(memberGiftLink);
            }
        }
    }

    @Override
    public List<MemberRebateLinkVo> queryMemberRebateList(String linkStatus, Long memberId) {
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.member.rebate.link.queryMemberRebateList")
                .setParam("linkStatus", linkStatus)
                .setParam("memberId", memberId);
        return dao.findForList(query, MemberRebateLinkVo.class);
    }

    @Override
    public Pagination queryMemberRebatePage(String linkStatus, Long memberId, PageRequest request) {
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.member.rebate.link.queryMemberRebateList")
                .setParam("linkStatus", linkStatus)
                .setParam("memberId", memberId)
                .setParam("pageSize", request.getPageSize())
                .setParam("pageNo", request.getPageNo())
                ;
        return dao.findForPage(query);
    }
}
