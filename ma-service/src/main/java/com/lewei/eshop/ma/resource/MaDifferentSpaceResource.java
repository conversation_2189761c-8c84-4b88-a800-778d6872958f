package com.lewei.eshop.ma.resource;

import com.lewei.eshop.common.request.different.DifferentSpaceDrawRequest;
import com.lewei.eshop.common.request.different.DifferentSpaceRecordRequest;
import com.lewei.eshop.common.request.doll.DollBoxPayReq;
import com.lewei.eshop.ma.biz.IMaCollisionService;
import com.lewei.eshop.ma.biz.IMaDifferentSpaceService;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

/**
 * 异空间网络控制层
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
@Path("/different/space")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class MaDifferentSpaceResource extends BaseAuthedResource {

    @Autowired
    private IMaDifferentSpaceService maDifferentSpaceService;


    /**
     * 查询异空间奖品信息
     * @param differentSpaceId 异空间id
     * @return SaveOrderVO
     */
    @GET
    @Path("/reward/{differentSpaceId}")
    public Response queryDifferentSpaceReward(@PathParam("differentSpaceId")Long differentSpaceId) {
        log.info("MaDifferentSpaceResource.queryDifferentSpaceDetail(differentSpaceId = {})",differentSpaceId);
        return Response.ok(  maDifferentSpaceService.queryDifferentSpaceReward(differentSpaceId)).build();
    }


    /**
     * 查询异空间信息
     * @param spuId 异空间id
     * @return SaveOrderVO
     */
    @GET
    @Path("/{spuId}")
    public Response queryDifferentSpace(@PathParam("spuId")Long spuId) {
        log.info("MaDifferentSpaceResource.queryDifferentSpace(spuId = {})",spuId);
        return Response.ok(maDifferentSpaceService.queryDifferentSpace(spuId,super.getMemberId())).build();
    }

    @POST
    @Path("/draw")
    public Response drawDifferentSpace(@Valid DifferentSpaceDrawRequest request) {
        log.info("MaDifferentSpaceResource.queryDifferentSpace(request = {})",request);
        return Response.ok(maDifferentSpaceService.drawDifferentSpace(request,super.getMemberId())).build();
    }

    @GET
    @Path("/reward/record")
    public Response queryDifferentSpaceRewardRecord(@Valid @BeanParam DifferentSpaceRecordRequest request) {
        log.info("MaDifferentSpaceResource.queryDifferentSpaceRewardRecord(request = {})",request);
        return Response.ok(maDifferentSpaceService.queryDifferentSpaceRewardRecord(request,super.getMemberId())).build();
    }


}
