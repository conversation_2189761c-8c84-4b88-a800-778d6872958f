package com.lewei.eshop.ma.biz.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.auth.ma.SSOMaAppCache;
import com.lewei.eshop.auth.ma.SSOMaAppService;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.member.MemberBalanceTradeTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.member.IncomeToBalanceRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.member.MemberMerchantCoinRequest;
import com.lewei.eshop.common.vo.member.MemberBalanceVo;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberBox;
import com.lewei.eshop.entity.member.MemberBoxReward;
import com.lewei.eshop.entity.member.types.MemberBoxStatusEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.OrderProduct;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IChainConfigService;
import com.lewei.eshop.ma.biz.IMaMemberBlacklistService;
import com.lewei.eshop.ma.biz.IMaMemberBoxService;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.paas.PayFeignClient;
import com.lewei.eshop.ma.message.request.MemberBoxPayRequest;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.pay.MemberBoxCallbackProcessor;
import com.lewei.eshop.ma.sso.MemberCache;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 自建盒
 *
 * <AUTHOR>
 * @time 2022/9/28
 */
@Service
@Transactional
public class MaMemberBoxServiceImpl implements IMaMemberBoxService {

    @Autowired
    private BaseDaoSupport dao;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private MemberBoxCallbackProcessor memberBoxCallbackProcessor;

    @Autowired
    private PayFeignClient payFeignClient;

    @Autowired
    private IChainConfigService chainConfigService;

    @Autowired
    private IOrderPayService orderPayService;

    @Autowired
    private SSOMaAppService ssoMaAppService;
    @Autowired
    private IMaMemberBlacklistService blacklistService;

    /**
     * 微信支付回调
     */
    private final String FIX_CALL_BACK_URL = "/api/ma/member/box/callback";

    /**
     * 立即购买
     * <p>
     * <p>
     * 1. 验证盒子是否可购买
     * 2. 本人无法对自己发布的下单
     * 3. 校验座位是否还在
     * 4. 下单
     * 5. 保存相应记录
     * 6. 支付
     *
     * @param request       下单信息
     * @param memberCache   会员信息
     * @param ssoMaAppCache 小程序
     * @return
     */
    @Override
    public SaveOrderVO buyMemberBox(MemberBoxPayRequest request, MemberCache memberCache, SSOMaAppCache ssoMaAppCache) {
        // 1. 验证盒子是否可购买
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                .and(Restrictions.eq("id", request.getMemberBoxId()));
        MemberBox memberBox = dao.query(query, MemberBox.class);
        if (null == memberBox) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_NO_SUCH_SKU);
        }
        if (!MemberBoxStatusEnum.S_MBS_JXZ.value().equals(memberBox.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_OUT_OF_STOCK);
        }


        // 2. 本人无法对自己发布的下单
        if (memberBox.getMemberId().equals(memberCache.getId())) {
            throw new BizCoreRuntimeException(MaErrorConstants.UNABLE_TO_PURCHASE_SELF_PUBLISHED_ITEMS);
        }

        this.validateBlackList(memberBox.getMemberId(), memberBox.getId(), memberCache.getId());

        // 3. 校验座位是否还在
        query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                .and(Restrictions.eq("seatNumber", request.getSeatNumber()))
                .and(Restrictions.eq("memberBoxId", request.getMemberBoxId()));
        MemberBoxReward memberBoxReward = dao.query(query, MemberBoxReward.class);

        if (null == memberBoxReward || BooleanUtil.isFalse(memberBoxReward.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.AH_SLOW_HAND_DID_NOT_GRAB_REFRESH_TO_BUY_AGAIN);
        }


        // 4. 下单
        Long memberId = memberCache.getId();
        Long chainId = memberCache.getChainId();
        Long tenantId = memberCache.getTenantId();
        String openId = ssoMaAppService.queryMemberOpenId(memberId, ssoMaAppCache.getAppId());
        // 订单金额
        BigDecimal price = memberBox.getPrice();
        Date now = DateFormatUtils.getNow();


        // 获取下单人信息
        SaasQueryBuilder queryMember = SaasQueryBuilder.where(Restrictions.eq("id", memberBox.getMemberId()));
        Member sellerMember = dao.query(queryMember, Member.class);

        // 保存订单
        Order order = new Order();
        order.setChainId(chainId);
        order.setTenantId(tenantId);
        order.setOrderSn(CodecUtil.createOrderId());
        order.setWechatAccount("");
        order.setMemberId(memberId);
        order.setOpenId(openId);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(price);
        order.setPaymentMoney(price);
//        order.setShippingMoney(BigDecimal.ZERO);
//        order.setShippingUser(member.getMemberName());
//        order.setTelNumber(member.getMobile());
//        order.setProvinceName(request.getProvinceName());
//        order.setCityName(request.getCityName());
//        order.setCountyName(request.getCountyName());
//        order.setAddress(request.getAddress());
//        order.setPostalCode(request.getPostalCode());
//        order.setNationalCode(request.getNationalCode());
        order.setOrderTitle(sellerMember.getMemberName() + BizMessageSource.getInstance().getMessage("cem40049"));
        order.setOrderType(OrderEnum.S_OOT_MEMBER_BOX.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());

        order.setCreated(now);
        order.setCreateBy(memberId);
        order.setPlatform(ssoMaAppCache.getPlatform());
        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_ESHOP.value());
        if (request.getPaymentMethod().equals(OrderPaymentTypeEnum.S_OPM_BALANCE.value())) {
            // TODO: 2022/9/28 乘币比金额的比例?
            BigDecimal moneyRatio = chainConfigService.queryChainConfig().getMoneyRatio();
            BigDecimal coin = order.getOrderMoney().multiply(moneyRatio);
            order.setOrderBalance(coin);
        }
        Map<String,Object> map = new HashMap<>();
        map.put("memberBoxId",request.getMemberBoxId());
        map.put("seatNumber",request.getSeatNumber());
        map.put("sellerMemberId",memberBox.getMemberId());
        order.setExtJson(JSONObject.toJSONString(map));
        dao.save(order);

        //订单ID
        Long orderId = order.getId();

        //生成订单明细
        OrderProduct orderProduct = new OrderProduct();
        orderProduct.setChainId(chainId);
        orderProduct.setTenantId(tenantId);
        orderProduct.setOrderId(orderId);
        orderProduct.setSpuId(memberBoxReward.getSpuId());
        orderProduct.setSkuId(memberBoxReward.getSkuId());
        orderProduct.setProductName(BizMessageSource.getInstance().getMessage("cem40050"));
        orderProduct.setMainImage(memberBox.getMainImage());
        orderProduct.setCurrentUnitPrice(price);
        orderProduct.setQuantity(1);
        orderProduct.setTotalPrice(price);
        orderProduct.setCreated(now);
        orderProduct.setCreateBy(memberId);
//        orderProduct.setBoxItemId(memberBox.getId());
        orderProduct.setPrimeCostFee(memberBoxReward.getPrimeCostFee());
        dao.save(orderProduct);





        // 6. 支付

        SaveOrderVO returnVo = new SaveOrderVO();
        returnVo.setOrderId(orderId);

        if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(request.getPaymentMethod())) {
            // todo 微信支付

            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), openId, chainId, ssoMaAppCache.getAppId(), request.getPaymentMethod(),
                    FIX_CALL_BACK_URL);
            returnVo.setPayUrl(payUrl);
//            // 查询商户认证
//            query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
//                    .and(Restrictions.eq("memberId", memberBox.getMemberId()));
//            MemberMerchantVerify merchantVerify = dao.query(query, MemberMerchantVerify.class);
//
//            OrderWxMicroRequest orderWxMicroRequest = new OrderWxMicroRequest();
//            orderWxMicroRequest.setPayMethod("wx_js_api");
//            orderWxMicroRequest.setPayConfigId(merchantVerify.getPayConfigId());
//            orderWxMicroRequest.setPaymentMoney(price);
//            orderWxMicroRequest.setOrderTitle(BizMessageSource.getInstance().getMessage("cem40051") + sellerMember.getMemberName() + BizMessageSource.getInstance().getMessage("cem40049"));
//            orderWxMicroRequest.setOrderCodes(order.getOrderSn());
//            orderWxMicroRequest.setNotifyUrl(FIX_CALL_BACK_URL);
//            orderWxMicroRequest.setOpenId(member.getOpenId());
//
//            // 服务费小于0.6%时 不分账处理
//            query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1));
//            MemberBoxConfigConfig config = dao.query(query, MemberBoxConfigConfig.class);
//
//            if(config.getServiceCharge().compareTo(new BigDecimal("0.6")) < 1){
//                orderWxMicroRequest.setProfitSharing("N");
//            }else {
//                orderWxMicroRequest.setProfitSharing("Y");
//            }
//            payFeignClient.orderWxMicro(orderWxMicroRequest);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(price);
            // 买家
            balanceRequest.setMemberId(memberId);
            balanceRequest.setPlId(memberBox.getId());
            // 查询卖家
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40051") + order.getOrderTitle());
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_MEMBER_BOX.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterPay(order);
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }
        return returnVo;
    }

    @Override
    public void transferIncomeToBalance(Long memberId, IncomeToBalanceRequest request) {
        // 商户币是否充足
        MemberBalanceVo balanceVo = memberFeignClient.getMemberBalance(memberId, null);
        BigDecimal merchantCoin = balanceVo.getMerchantCoin();
        if (request.getPrice().compareTo(merchantCoin) > 0) {
            throw new BizCoreRuntimeException(MaErrorConstants.INPUT_PRICE_CANT_GANT_BALANCE);
        }

        // 处理余额
        MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setBalance(request.getPrice());
        balanceRequest.setMemberId(memberId);
        balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40166"));
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        memberFeignClient.memberBalanceHandle(balanceRequest);

        // 处理商户币
        MemberMerchantCoinRequest merchantCoinRequest = new MemberMerchantCoinRequest();
        merchantCoinRequest.setCoin(request.getPrice());
        merchantCoinRequest.setMemberId(memberId);
        merchantCoinRequest.setContent(BizMessageSource.getInstance().getMessage("cem40167"));
        merchantCoinRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
        memberFeignClient.memberMerchantCoinHandler(merchantCoinRequest);
    }

    /**
     * 余额支付后
     *
     * @param order 订单
     */
    private void afterPay(Order order) {
        taskExecutor.execute(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderCode", order.getOrderSn());
            memberBoxCallbackProcessor.onPayTradeSuccess(order, jsonObject);
        });
    }

    private void validateBlackList(Long memberId,Long marketId,Long blackMemberId){
        boolean flag1 = blacklistService.queryIsMemberBlack(memberId,blackMemberId);
        if (flag1){
            throw new BizCoreRuntimeException(MaErrorConstants.BE_MEMBER_BLACK);
        }
        boolean flag2 = blacklistService.queryIsMarKetBlack(marketId,blackMemberId);
        if (flag2){
            throw new BizCoreRuntimeException(MaErrorConstants.BE_MARKET_BLACK);
        }
    }
}
