package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.auth.annotation.RateLimiter;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.request.activity.ActivityGIftReq;
import com.lewei.eshop.common.request.different.DifferentSpaceDrawRequest;
import com.lewei.eshop.common.request.different.DifferentSpaceRecordRequest;
import com.lewei.eshop.common.vo.different.MaDifferentSpaceRewardVo;
import com.lewei.eshop.common.vo.different.MaDifferentSpaceVo;
import com.lewei.eshop.entity.activity.types.ActivityTypeEnum;
import com.lewei.eshop.entity.space.DifferentSpace;
import com.lewei.eshop.entity.space.DifferentSpaceReward;
import com.lewei.eshop.entity.space.DifferentSpaceRewardRecord;
import com.lewei.eshop.entity.space.type.DifferentSpaceStatusEnum;
import com.lewei.eshop.ma.MaCacheProperties;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaDifferentSpaceService;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.message.mina.entity.MaMessageSubTypeEnum;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 异空间实现类
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
@Service
@Transactional
@Slf4j
public class MaDifferentSpaceServiceImpl implements IMaDifferentSpaceService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private RedisCacheProvider redisCacheProvider;

    @Autowired
    private AppFeignClient appFeignClient;

    @Override
    public List<MaDifferentSpaceRewardVo> queryDifferentSpaceReward(Long differentSpaceId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.different.space.queryDifferentSpaceReward")
                .setParam("differentSpaceId", differentSpaceId);
        return dao.findForList(ssqb,MaDifferentSpaceRewardVo.class);
    }

    @Override
    public MaDifferentSpaceVo queryDifferentSpace(Long spuId, Long memberId) {
        //查询当前异空间信息
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.different.space.queryDifferentSpace")
                .setParam("memberId", memberId)
                .setParam("spuId", spuId);
        MaDifferentSpaceVo vo = dao.findForObj(ssqb, MaDifferentSpaceVo.class);
        if(Objects.nonNull(vo)){
            String key = MaCacheKeys.DIFFERENT_SPACE_KEY + vo.getId();
            Integer currentNum = (Integer) redisCacheProvider.getRedisTemplate().opsForHash().get(key,memberId.toString());
            if (currentNum != null) {
                //单签进度值
                vo.setCurrentNum(currentNum);
            }else{
                vo.setCurrentNum(0);
            }
        }
        return vo;
    }

    @Override
    @RateLimiter(isLoop = true)
    public MaDifferentSpaceRewardVo drawDifferentSpace(DifferentSpaceDrawRequest request, Long memberId) {
        Long differentSpaceId = request.getDifferentSpaceId();
        DifferentSpace differentSpace = dao.queryById(differentSpaceId, DifferentSpace.class);
        if(Objects.isNull(differentSpace)){
            throw new BizCoreRuntimeException(MaErrorConstants.DIFFERENT_SPACE_IS_NOT_FIND);
        }
        if (!differentSpace.getStatus().equals(DifferentSpaceStatusEnum.S_DSS_UP.value())) {
            throw new BizCoreRuntimeException(MaErrorConstants.DIFFERENT_SPACE_STATUS_ERROR);
        }
        //查询当前异空间信息
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.different.space.reduceDifferentSpaceMember")
                .setParam("differentSpaceId", differentSpaceId)
                .setParam("memberId",memberId);
        int i = dao.updateByMybatis(ssqb);
        if(i == 0){
            throw new BizCoreRuntimeException(MaErrorConstants.DIFFERENT_SPACE_MEMBER_QUANTITY_ERROR);
        }


        List<MaDifferentSpaceRewardVo> spaceRewardVos = this.queryDifferentSpaceReward(differentSpaceId);
        //总概率 可以支持概率总和不等一
        Double sumOdds = spaceRewardVos.stream().mapToDouble(MaDifferentSpaceRewardVo::getOdds).sum();


        List<Double> rewardOddsList = new ArrayList<>();
        List<Double> sortRewardOddsList = new ArrayList<>() ;
        Double tempOdds = 0d;
        for (MaDifferentSpaceRewardVo itemRewardVo : spaceRewardVos) {
            tempOdds += itemRewardVo.getOdds();
            rewardOddsList.add(tempOdds / sumOdds);
        }
        //随机数在哪个概率区间内，则是哪个奖品
        double randomDouble = Math.random();
        sortRewardOddsList.addAll(rewardOddsList);
        //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
        sortRewardOddsList.add(randomDouble);
        Collections.sort(sortRewardOddsList);
        int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);

        //中奖奖品
        MaDifferentSpaceRewardVo rewardVo = spaceRewardVos.get(lotteryIndex);
        //格式化奖品json
        Map<String, String> giftMap = formatGifJson(rewardVo);
        //发奖
        ActivityGIftReq req = new ActivityGIftReq();
        req.setActivityId(0L);
        req.setActivityType(ActivityTypeEnum.S_AT_DIFFERENT_SPACE.value());
        req.setGiftJson(JSONObject.toJSONString(giftMap));
        req.setGiftType("product");
        req.setIsSendMsg(false);
        req.setTitle(BizMessageSource.getInstance().getMessage("cem40179"));
        req.setMemberIds(Collections.singletonList(memberId));
        appFeignClient.handleActivityGift(req);
        //增加获奖数量
        Ssqb addGiftNum = Ssqb.create("com.lewei.eshop.ma.different.space.addDifferentSpaceMemberGift")
                .setParam("differentSpaceId", differentSpaceId)
                .setParam("memberId",memberId);
        dao.updateByMybatis(addGiftNum);

        //保存中奖记录
        DifferentSpaceRewardRecord record = new DifferentSpaceRewardRecord();
        record.setDifferentSpaceId(differentSpaceId);
        record.setSpuId(rewardVo.getSpuId());
        record.setRewardName(rewardVo.getSpuName());
        record.setCategoryId(rewardVo.getCategoryId());
        record.setCategoryName(rewardVo.getCategoryName());
        record.setCategoryPic(rewardVo.getCategoryPic());
        record.setMainImage(rewardVo.getSpuMainImage());
        record.setMemberId(memberId);
        record.setCreated(DateFormatUtils.getNow());
        record.setDataStatus(Boolean.TRUE);
        dao.save(record);

        //返回中奖信息
        return rewardVo;

    }

    @Override
    public Pagination queryDifferentSpaceRewardRecord(DifferentSpaceRecordRequest request, Long memberId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.different.space.queryDifferentSpaceRewardRecord")
                .setParam("pageNo",request.getPageNo())
                .setParam("pageSize",request.getPageSize())
                .setParam("differentSpaceId", request.getDifferentSpaceId());
        if(BooleanUtils.isTrue(request.getIsMember())){
            ssqb .setParam("memberId", memberId);
        }
        return dao.findForPage(ssqb);
    }

    private static Map<String, String> formatGifJson(MaDifferentSpaceRewardVo rewardVo) {
        Map<String, String> giftMap = new HashMap<>();
        giftMap.put("type","product");
        JSONObject productJson = new JSONObject();
        productJson.put("name", rewardVo.getSpuName());
        productJson.put("skuId", rewardVo.getSkuId());
        productJson.put("spuId", rewardVo.getSpuId());
        productJson.put("amount",1);
        productJson.put("price", rewardVo.getPriceFee());
        productJson.put("mainImage", rewardVo.getSpuMainImage());
        productJson.put("primeCostFee", rewardVo.getPrimeCostFee());
        JSONArray productJsons = new JSONArray();
        productJsons.add(productJson);
        giftMap.put("productJson",JSONArray.toJSONString(productJsons));
        return giftMap;
    }
}
