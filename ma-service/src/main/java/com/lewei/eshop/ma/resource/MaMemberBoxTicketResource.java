package com.lewei.eshop.ma.resource;

import com.lewei.eshop.common.vo.member.MemberBoxTicketVo;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.constraints.NotNull;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;
import java.util.List;

/**
* 会员闯关票资源类
* <AUTHOR>
* @date 2022/11/04
**/
@Path("/member/box/tickets")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class MaMemberBoxTicketResource extends BaseAuthedResource {

    @Autowired
    private MemberFeignClient memberFeignClient;


    /**
     * 查询会员闯关票
     * @param boxItemId 套盒id
     * @return  List<MemberBoxTicketVo>
     */
    @GET
    public Response queryMemberBoxTicketList(@NotNull(message = "boxItemId is required") @QueryParam("boxItemId") Long boxItemId) {
        log.debug("MaMemberBoxTicketResource.queryMemberBoxTicketList(boxItemId = {})",boxItemId);
        List<MemberBoxTicketVo> memberBoxTicketVos =  memberFeignClient.queryMemberBoxTicketList(super.getMemberId(),boxItemId);
        return Response.ok(memberBoxTicketVos).build();
    }


}