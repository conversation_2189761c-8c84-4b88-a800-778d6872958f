package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.data.product.ProductEnum;
import com.lewei.eshop.common.request.member.MemberBalancePayRequest;
import com.lewei.eshop.entity.app.ApplicationConfig;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.types.MemberAuthorityEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeSubTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaMemberRewardService;
import com.lewei.eshop.ma.biz.IMaMemberService;
import com.lewei.eshop.ma.biz.IMaOrderShipService;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.message.request.OrderShipPayReq;
import com.lewei.eshop.ma.message.request.PrePayAddressRequest;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.lewei.eshop.ma.message.vo.ShipPrepayVo;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.pay.PayCallbackProcessorV2;
import com.lewei.eshop.ma.sso.MemberCache;
import com.lewei.log.trace.MDCTraceUtils;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/18
 */
@Service
@Transactional
public class MaOrderShipServiceImpl implements IMaOrderShipService {

    private final String callBackUrl = "/api/ma/pay/wx/callback";


    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IMaMemberRewardService memberRewardService;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private PayCallbackProcessorV2 payCallbackProcessorV2;
    @Autowired
    private AppFeignClient appFeignClient;
    @Autowired
    private IMaMemberService memberService;


    @Override
    public ShipPrepayVo orderShipPrepay(Long orderId) {
        Order order = dao.queryById(orderId,Order.class);
        if (order == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_COMMON_ERROR, BizMessageSource.getInstance().getMessage("cem40052"));
        }
        if (!OrderEnum.S_OS_PAYED.value().equals(order.getStatus())){
            throw new BizCoreRuntimeException(MaErrorConstants.ORDER_STATUS_ERROR_OR_ORDER_DELETE);
        }
        PrePayAddressRequest request = JSON.parseObject(order.getExtJson(),PrePayAddressRequest.class);

        Ssqb queryShipProducts = Ssqb.create("com.lewei.eshop.ma.order.queryShipProducts")
                .setParam("orderId",orderId);
        List<ShipPrepayVo.ShipRewardVo> shipRewardVos = dao.findForList(queryShipProducts,ShipPrepayVo.ShipRewardVo.class);

        //验证配置会员是否支持预售发货
        ApplicationConfig sendGoodsSetting = appFeignClient.queryApplicationConfigInfo("SendGoodsSetting");
        Boolean sendFlag = Boolean.TRUE;
        if(Objects.nonNull(sendGoodsSetting)){
            JSONObject jsonObject = JSONObject.parseObject(sendGoodsSetting.getExtJson());
            JSONArray memberIds = jsonObject.getJSONArray("memberIds");
            if(ListUtil.isNotEmpty(memberIds)){
                if(BooleanUtils.isTrue(sendGoodsSetting.getIsMaShow()) && memberIds.contains(String.valueOf(order.getMemberId()))){
                    sendFlag = Boolean.FALSE;
                }
            }
        }
        if(sendFlag){
            List<ShipPrepayVo.ShipRewardVo> preSaleRewardVos = shipRewardVos.stream().filter(a-> BooleanUtils.isTrue(a.getIsPreSale())).collect(Collectors.toList());
            //预售发货验证
            if (ListUtil.isNotEmpty(preSaleRewardVos)){
                throw new BizCoreRuntimeException(MaErrorConstants.IS_PRE_SALE_ERROR);
            }
        }



        return memberRewardService.getShipPrepayVo(request,null,shipRewardVos,false);
    }

    @Override
    public SaveOrderVO shipPay(OrderShipPayReq request, MemberCache memberCache,String appId, String platform) {
        // 校验封禁
        boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_DELIVERY, memberCache.getId());
        if (isAuthority) {
            throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_DELIVERY);
        }

        ShipPrepayVo prepayVo = this.orderShipPrepay(request.getOrderId());
        if (prepayVo == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.TREASURE_PRODUCT_EXPIRED);
        }
        if (prepayVo.getTotalFee().compareTo(request.getTotalFee()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.TREASURE_PRODUCT_EXPIRED);
        }
        Order productOrder = dao.queryById(request.getOrderId(),Order.class);

        //支付产生虚拟订单
        Order order = new Order();
        order.setDataStatus(false);
        order.setCreated(DateFormatUtils.getNow());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberCache.getId());
        order.setOpenId(memberCache.getOpenId());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(request.getTotalFee());
        order.setPaymentMoney(request.getTotalFee());
        order.setShippingMoney(request.getTotalFee());
        order.setShippingUser(productOrder.getShippingUser());
        order.setTelNumber(productOrder.getTelNumber());
        order.setProvinceName(productOrder.getProvinceName());
        order.setCityName(productOrder.getCityName());
        order.setCountyName(productOrder.getCountyName());
        order.setAddress(productOrder.getAddress());
        order.setEmail(productOrder.getEmail());
        order.setOrderTitle(productOrder.getOrderTitle());
        order.setOrderType(OrderEnum.S_OOT_PRODUCT.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setRemark(order.getRemark());
        order.setLogisticsMode(ProductEnum.S_LM_LOGISTICS.value());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberCache.getId());
        order.setPlatform(platform);
        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_ESHOP.value());
        order.setOrderTradeType(OrderTradeTypeEnum.S_OTT_SHIP.value());
        order.setTradeSubType(OrderTradeSubTypeEnum.S_OTST_ORDER.value());
        Map<String, Object> extJsonMap = new HashMap<>(2);
        extJsonMap.put("shipSubOrders", prepayVo.getSubOrders());
        extJsonMap.put("traceId", MDCTraceUtils.getTraceId());
        extJsonMap.put("productOrderId",productOrder.getId());
        order.setExtJson(JSON.toJSONString(extJsonMap));
        order.setDataStatus(false);
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(order.getPaymentMethod())){
            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            order.setOrderBalance(order.getOrderMoney().multiply(chainConfig.getMoneyRatio()));
        }
        dao.save(order);
        SaveOrderVO saveOrderVO = new SaveOrderVO();
        saveOrderVO.setOrderId(order.getId());
        callPayApi(request.getPaymentMethod(),order,memberCache.getId(),memberCache.getOpenId(), appId,saveOrderVO, request.getPayMethod());

        return saveOrderVO;
    }

    private void callPayApi(String paymentMethod, Order order, Long memberId,String openId, String appId, SaveOrderVO returnVo, String payMethod) {

        if (order.getOrderMoney().compareTo(BigDecimal.ZERO) > 0 ){
            if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(paymentMethod) || OrderPaymentTypeEnum.S_OPM_ALIPAY.value().equals(paymentMethod)) {
                Member member = dao.queryById(memberId, Member.class);
                //调取微信支付
                String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                        order.getOrderSn(), openId, XcrmThreadContext.getChainId(), appId,paymentMethod,callBackUrl, null, payMethod, null,member.getMobile());
                returnVo.setPayUrl(payUrl);
            } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(paymentMethod)) {
                memberFeignClient.memberBalancePay(new MemberBalancePayRequest(null, memberId, order.getOrderMoney(), order.getOrderTitle(), order.getId()));
                this.afterPay(order);
            }
        }else {
            this.afterPay(order);
        }
    }

    public void afterPay(Order order) {

        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("orderCode", order.getOrderSn());
                payCallbackProcessorV2.onPayShipTradeSuccess(order,jsonObject);
            }
        });
    }
}
