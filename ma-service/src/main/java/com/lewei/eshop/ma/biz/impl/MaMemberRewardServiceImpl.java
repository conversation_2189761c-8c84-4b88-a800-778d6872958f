package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.auth.annotation.RateLimiter;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.OrderUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.data.product.ProductEnum;
import com.lewei.eshop.common.request.member.MemberBalancePayRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.member.MemberCategoryDailyDataReq;
import com.lewei.eshop.common.request.order.TradeFlowReq;
import com.lewei.eshop.common.vo.config.PickUpConfigVo;
import com.lewei.eshop.common.vo.member.*;
import com.lewei.eshop.entity.activity.ActivityRecord;
import com.lewei.eshop.entity.activity.types.ActivityTypeEnum;
import com.lewei.eshop.entity.app.ApplicationConfig;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberReward;
import com.lewei.eshop.entity.member.MemberRewardRecycle;
import com.lewei.eshop.entity.member.MemberRewardRecycleRef;
import com.lewei.eshop.entity.member.types.MemberRewardStatusEnum;
import com.lewei.eshop.entity.member.types.RewardReceiveWayEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeSubTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeTypeEnum;
import com.lewei.eshop.entity.product.ProductTreasureLog;
import com.lewei.eshop.entity.product.types.RewardTypeEnum;
import com.lewei.eshop.entity.sso.types.MaPlatformEnum;
import com.lewei.eshop.entity.trade.types.TradeTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaMemberRewardService;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.message.request.*;
import com.lewei.eshop.ma.message.vo.PrePayVO;
import com.lewei.eshop.ma.message.vo.PrePayVOV2;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.lewei.eshop.ma.message.vo.ShipPrepayVo;
import com.lewei.eshop.ma.pay.FreightFeeProcessor;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.pay.PayCallbackProcessorV2;
import com.lewei.log.trace.MDCTraceUtils;
import com.lewei.search.client.entity.MemberRewardRecycleEsEntity;
import com.lewei.search.client.service.ICommonEsProvider;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.StringUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/31
 */
@Service
@Transactional
@Slf4j
public class MaMemberRewardServiceImpl implements IMaMemberRewardService {

    private final String callBackUrl = "/api/ma/pay/wx/callback";


    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private FreightFeeProcessor freightFeeProcessor;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private PayCallbackProcessorV2 payCallbackProcessorV2;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private AppFeignClient appFeignClient;
    @Autowired
    private RedisCacheProvider redisCacheProvider;
    @DubboReference(check = false, version = "1.0.0")
    private ICommonEsProvider commonEsProvider;


    @Override
    public void batchUpdateMemberRewardStatus(List<Long> rewardIds, String status) {

        Ssqb  batchUpdateMemberRewardStatus = Ssqb.create("com.lewei.eshop.ma.product.box.batchUpdateMemberRewardStatus")
                .setParam("rewardIds",rewardIds)
                .setParam("finalStatus", MemberRewardStatusEnum.fromCode(status).finalStatus())
                .setParam("status",status);
        dao.updateByMybatis(batchUpdateMemberRewardStatus);

    }

    @Override
    public Integer batchUpdateMemberRewardStatus(List<Long> rewardIds, String status, String oldStatus) {
        Ssqb  batchUpdateMemberRewardStatus = Ssqb.create("com.lewei.eshop.ma.product.box.batchUpdateMemberRewardStatus")
                .setParam("rewardIds",rewardIds)
                .setParam("finalStatus", MemberRewardStatusEnum.fromCode(status).finalStatus())
                .setParam("oldStatus",oldStatus)
                .setParam("status",status);
       return dao.updateByMybatis(batchUpdateMemberRewardStatus);
    }

    @Override
    public int updateMemberRewardStatus(Long rewardId, String status, String oldStatus) {
        Ssqb  updateMemberRewardStatus = Ssqb.create("com.lewei.eshop.ma.product.box.updateMemberRewardStatus")
                .setParam("rewardId",rewardId)
                .setParam("status", status)
                .setParam("finalStatus", MemberRewardStatusEnum.fromCode(status).finalStatus())
                .setParam("oldStatus",oldStatus)
                ;
        return dao.updateByMybatis(updateMemberRewardStatus);
    }

    @Override
    public void batchLockOrUnlockMemberReward(UpdateMemberRewardStatusRequest request,Long memberId) {
        List<MemberReward> rewards = this.queryMemberRewards(request.getRewardIds());
        List<MemberReward> tempRewards;
        String oldStatus = "";
        if (MemberRewardStatusEnum.S_MRS_LOCK.value().equals(request.getStatus())){
            tempRewards = rewards.stream().filter(a-> !Objects.equals(a.getStatus(),MemberRewardStatusEnum.S_MRS_UNAPPLY.value())).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(tempRewards)){
                throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_UNAPPLY_TO_LOCK);
            }
            oldStatus = MemberRewardStatusEnum.S_MRS_UNAPPLY.value();
        } else if (MemberRewardStatusEnum.S_MRS_UNAPPLY.value().equals(request.getStatus())){
            tempRewards = rewards.stream().filter(a-> !Objects.equals(a.getStatus(),MemberRewardStatusEnum.S_MRS_LOCK.value())).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(tempRewards)){
                throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_LOCK_TO_UNAPPLY);

            }
            oldStatus = MemberRewardStatusEnum.S_MRS_LOCK.value();
        }else {
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_ERROR);
        }

        tempRewards = rewards.stream().filter(a->!Objects.equals(a.getMemberId(),memberId)).collect(Collectors.toList());;
        if (ListUtil.isNotEmpty(tempRewards)){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_EXPIRED);
        }

        for (Long rewardId : request.getRewardIds()) {
                //可能会发生死锁
            try {
                int l = this.updateMemberRewardStatus(rewardId,request.getStatus(),oldStatus);
                if (l < 1){
                    throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_EXPIRED);
                }
            } catch (Exception e) {
                throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_EXPIRED);
            }

        }
      //  this.batchUpdateMemberRewardStatus(request.getRewardIds(),request.getStatus());
    }


    @Override
    public List<MemberRewardVo> queryMemberRewardList(Long memberId, Long orderId,List<Long> memberRewardIds) {
        Ssqb  queryMemberRewardList = Ssqb.create("com.lewei.eshop.ma.product.box.queryMemberRewardList")
                .setParam("memberId",memberId)
                .setParam("memberRewardIds",memberRewardIds)
                .setParam("orderId",orderId);
        return dao.findForList(queryMemberRewardList,MemberRewardVo.class);
    }


    @Override
    public List<MemberRewardVo> queryMemberRewardListOrder(Long memberId, Long orderId,List<Long> memberRewardIds,String order,String orderName) {
        Ssqb  queryMemberRewardList = Ssqb.create("com.lewei.eshop.ma.product.box.queryMemberRewardList")
                .setParam("memberId",memberId)
                .setParam("memberRewardIds",memberRewardIds)
                .setParam("orderId",orderId)
                .setParam("order",order)
                .setParam("orderName",orderName);
        return dao.findForList(queryMemberRewardList,MemberRewardVo.class);
    }

    @Override
    public List<MemberRewardGroupVo> queryMemberRewardGroup(Long memberId,Boolean isCard) {
        if (null == isCard){
            isCard = false;
        }
        Ssqb  queryMemberRewardList = Ssqb.create("com.lewei.eshop.ma.product.box.queryMemberRewardList")
                .setParam("memberId",memberId)
                .setParam("isCard",isCard)
                .setParam("status",MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
        List<MemberRewardVo> rewardVos =  dao.findForList(queryMemberRewardList,MemberRewardVo.class);

        List<MemberRewardGroupVo> groupVos = new ArrayList<>();

        if (ListUtil.isNotEmpty(rewardVos)){
            Map<String,List<MemberRewardVo>> mapping = rewardVos.stream().collect(Collectors.groupingBy(a->a.getName()+"@#@"+a.getSkuItemId()));
            mapping.forEach((k,v)->{
                String[] temp = k.split("@#@");
                MemberRewardGroupVo  groupVo = new MemberRewardGroupVo();
                groupVo.setName(temp[0]);
                groupVo.setNum(v.size());
                groupVo.setRewardVos(v);
                groupVos.add(groupVo);
            });
        }

        return groupVos;
    }

    @Override
    public List<MemberRewardCountVo> queryMemBerRewardCount(Long memberId,Boolean isCard,String queryKey,String spuIds,Boolean hasPostcard) {
        if (isCard ==null){
            isCard = false;
        }
        List<String> spu = new ArrayList<>();
        if(StringUtils.isNotBlank(spuIds)){
            spu  = Arrays.asList(spuIds.split(","));
        }
        Ssqb  queryMemBerRewardCount = Ssqb.create("com.lewei.eshop.ma.product.box.queryMemBerRewardCount")
                .setParam("memberId",memberId)
                .setParam("spuIds",spu)
                .setParam("queryKey",queryKey)
                .setParam("hasPostcard",hasPostcard)
                .setParam("isCard",isCard)
                ;
        return dao.findForList(queryMemBerRewardCount,MemberRewardCountVo.class);
    }

    @Override
    public ShipPrepayVo shipPrepay(ShipPrepayRequest request, Long levelId,String platform,Long memberId) {
        Ssqb queryShipPrepayVo  = Ssqb.create("com.lewei.eshop.ma.order.queryShipRewardVo")
                .setParam("rewardIds",request.getRewardIds()).setParam("rewardType", RewardTypeEnum.S_SRT_ONLINE.value());
        List<ShipPrepayVo.ShipRewardVo> allRewardVos = dao.findForList(queryShipPrepayVo, ShipPrepayVo.ShipRewardVo.class);

        Ssqb queryShipRewardGroupVo  = Ssqb.create("com.lewei.eshop.ma.order.queryShipRewardGroupVo")
                .setParam("rewardIds",request.getRewardIds()).setParam("rewardType", RewardTypeEnum.S_SRT_ONLINE.value());
        List<ShipPrepayVo.ShipRewardGroupVo> allRewardGroupVos = dao.findForList(queryShipRewardGroupVo, ShipPrepayVo.ShipRewardGroupVo.class);

        //自动回收
        boolean isAutoRecovery = getIsAutoRecovery(request.getAddress().getProvince(),request.getAddress().getCity(),request.getAddress().getArea(),request.getAddress().getAddress(),platform);

        if (!isAutoRecovery){
            //验证配置会员是否支持预售发货
            ApplicationConfig sendGoodsSetting = appFeignClient.queryApplicationConfigInfo("SendGoodsSetting");
            Boolean sendFlag = Boolean.TRUE;
            if(Objects.nonNull(sendGoodsSetting)){
                JSONObject jsonObject = JSONObject.parseObject(sendGoodsSetting.getExtJson());
                JSONArray memberIds = jsonObject.getJSONArray("memberIds");
                if(BooleanUtils.isTrue(sendGoodsSetting.getIsMaShow()) && ListUtil.isNotEmpty(memberIds)){
                     if(memberIds.contains(String.valueOf(memberId))){
                         sendFlag = Boolean.FALSE;
                     }
                }
            }
            if(sendFlag){
                //预售验证
                for(ShipPrepayVo.ShipRewardVo allReward : allRewardVos){
                    if (allReward.getIsPreSale() != null){
                        if (BooleanUtils.isTrue(allReward.getIsPreSale())){
                            throw new BizCoreRuntimeException(MaErrorConstants.IS_PRE_SALE_ERROR);
                        }
                    }
                }
            }

        }


        ShipPrepayVo shipPrepayVo =  getShipPrepayVo(request.getAddress(), levelId, allRewardVos, isAutoRecovery);
        if (shipPrepayVo != null) {
            shipPrepayVo.setShipRewardVos(allRewardGroupVos);
        }
        return shipPrepayVo;
    }
    @Override
    public ShipPrepayVo getShipPrepayVo(PrePayAddressRequest request, Long levelId, List<ShipPrepayVo.ShipRewardVo> allRewardVos, boolean isAutoRecovery) {
        ShipPrepayVo shipPrepayVo = null;
        if (ListUtil.isNotEmpty(allRewardVos)){
            //按照供应商ID进行分组
            Map<Long, List<ShipPrepayVo.ShipRewardVo>> rewardMapping = allRewardVos.stream().collect(Collectors.groupingBy(ShipPrepayVo.ShipRewardVo::getSupplyChainId));
            List<ShipPrepayVo.PrepaySubOrder> subOrders = new ArrayList<>();
            for(Map.Entry<Long, List<ShipPrepayVo.ShipRewardVo>> entry : rewardMapping.entrySet()) {
                Long supplyChainId = entry.getKey();
                List<ShipPrepayVo.ShipRewardVo> rewards = entry.getValue();
                PrePayVOV2 prePayVOV2 = new PrePayVOV2();
                for (ShipPrepayVo.ShipRewardVo rewardVo : rewards) {
                    if (rewardVo.getNum() == null || rewardVo.getNum() == 0){
                        rewardVo.setNum(1);
                    }
                    PrePayVO prePayVO = new PrePayVO();
                    BeanUtils.copyProperties(rewardVo, prePayVO);
                    prePayVO.setProductName(rewardVo.getName());
                    prePayVOV2.getPrePayVOList().add(prePayVO);
                }

                if (!isAutoRecovery){
                    //邮费处理
                    freightFeeProcessor.calculateFreightFee(prePayVOV2, ProductEnum.S_LM_LOGISTICS.value(), request, levelId);
                }
                //构建子订单
                ShipPrepayVo.PrepaySubOrder subOrder = new ShipPrepayVo.PrepaySubOrder();
                subOrder.setSupplyChainId(supplyChainId);
                subOrder.setOrderFreightFee(prePayVOV2.getSubTotalFreightFee());
                subOrder.setShipRewardVos(rewards);

                subOrders.add(subOrder);
            }

            shipPrepayVo = new ShipPrepayVo(subOrders, allRewardVos);
        }
        return shipPrepayVo;
    }

    @Override
    public boolean getIsAutoRecovery(Integer province, Integer city, Integer area, String address,String platform) {
        boolean isAutoRecovery = false;
        if(platform.equals(MaPlatformEnum.APP.value())){
            ApplicationConfig applicationConfig= appFeignClient.queryApplicationConfigInfo("appRecycle-auto");
            if(applicationConfig != null && BooleanUtils.isFalse(applicationConfig.getIsMaShow())){
                return false;
            }
        }
        PickUpConfigVo pickUpConfigVo = publicFeignClient.queryPickUpConfig();
        if(pickUpConfigVo != null && BooleanUtils.isTrue(pickUpConfigVo.getIsOpen())) {
            //订单发货省、市与配置匹配
            if(province.equals(pickUpConfigVo.getProvinceCode()) && city.equals(pickUpConfigVo.getCityCode())) {
                Long wareHouseId = pickUpConfigVo.getWareHouseId();
                if(wareHouseId != null && wareHouseId.compareTo(0L) > 0) {
                    isAutoRecovery = true;
                }
                if (pickUpConfigVo.getAreaCode() != 0){
                    if (!area.equals(pickUpConfigVo.getAreaCode())){
                        isAutoRecovery = false;
                    }
                }
                if (!StringUtil.isEmpty(pickUpConfigVo.getAddress())){
                    if (!pickUpConfigVo.getAddress().equals(address)){
                        isAutoRecovery = false;
                    }
                }
            }
        }
        return isAutoRecovery;
    }

    @Override
    @RateLimiter
    public SaveOrderVO shipPay(ShipPayRequest request, Long levelId,Long memberId,String openId,String appId,String platform) {
        if (request.getRewardIds().size() > 100){
            throw new BizCoreRuntimeException(MaErrorConstants.SHIP_COUNT_ERROR);
        }
        ShipPrepayRequest prepayRequest  = new ShipPrepayRequest();
        prepayRequest.setRewardIds(request.getRewardIds());
        //兼容
        request.getAddressRequest().setAddress(request.getAddress());
        prepayRequest.setAddress(request.getAddressRequest());

        ShipPrepayVo prepayVo = this.shipPrepay(prepayRequest,levelId,platform,memberId);
        if (prepayVo == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }
        if (request.getTotalFee().compareTo(prepayVo.getTotalFee()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }
        List<ShipPrepayVo.ShipRewardVo> rewardVos = prepayVo.getShipRewardVoList();
        List<ShipPrepayVo.ShipRewardVo> tempRewardVos = rewardVos.stream().filter(a->!Objects.equals(MemberRewardStatusEnum.S_MRS_UNAPPLY.value(),a.getRewardStatus())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(tempRewardVos)){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_UNAPPLY_TO_UNDELIVERED);
        }

        List<ShipPrepayVo.ShipRewardVo> offLineRewardVos = rewardVos.stream().filter(a->Objects.equals(RewardTypeEnum.S_SRT_OFFLINE.value(),a.getRewardType())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(offLineRewardVos)){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_ONLINE_TO_UNDELIVERED);
        }

        //Map<Long, ShipPrepayVo.PrepaySubOrder> splitOrderMap = prepayVo.getSplitOrderMap();
        BigDecimal subTotalFreightFee = prepayVo.getSubTotalFreightFee();

        //订单标题
        String orderTitle = rewardVos.get(0).getName();
        if (rewardVos.size() > 1) {
            orderTitle = orderTitle + BizMessageSource.getInstance().getMessage("cem40099");
        }

        //List<Long> rewardIds = rewardVos.stream().map(ShipPrepayVo.ShipRewardVo::getRewardId).collect(Collectors.toList());

        //下单
        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberId);
        order.setOpenId(openId);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(subTotalFreightFee);
        order.setPaymentMoney(subTotalFreightFee);
        order.setShippingMoney(subTotalFreightFee);
        order.setShippingUser(request.getShippingUser());
        order.setTelNumber(request.getTelNumber());
        order.setProvinceName(request.getProvinceName());
        order.setCityName(request.getCityName());
        order.setCountyName(request.getCountyName());
        order.setAddress(request.getAddress());
        order.setEmail(request.getEmail());
        order.setPostalCode(request.getPostalCode());
        order.setNationalCode(request.getNationalCode());
        order.setOrderTitle(orderTitle);
        order.setOrderType(OrderEnum.S_OOT_PRODUCT.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setRemark(request.getRemark());
        order.setLogisticsMode(ProductEnum.S_LM_LOGISTICS.value());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberId);
        order.setPlatform(platform);
        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_ESHOP.value());
        order.setOrderTradeType(OrderTradeTypeEnum.S_OTT_SHIP.value());
        order.setTradeSubType(OrderTradeSubTypeEnum.S_OTST_REWARD.value());
        Map<String, Object> extJsonMap = new HashMap<>(2);
        extJsonMap.put("shipSubOrders", prepayVo.getSubOrders());
        extJsonMap.put("traceId", MDCTraceUtils.getTraceId());
        order.setExtJson(JSON.toJSONString(extJsonMap));
        order.setDataStatus(false);
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(order.getPaymentMethod())){
            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            order.setOrderBalance(order.getOrderMoney().multiply(chainConfig.getMoneyRatio()));
        }
        dao.save(order);
        Long orderId = order.getId() ;

//        List<OrderProduct> orderProducts = new ArrayList<>();
//
//        for (ShipPrepayVo.ShipRewardVo rewardVo : rewardVos) {
//            OrderProduct orderProduct = new OrderProduct();
//            orderProduct.setOrderId(orderId);
//            orderProduct.setPlId(rewardVo.getRewardId());
//            orderProduct.setParentSpuId(rewardVo.getSpuId());
//            orderProduct.setParentSkuId(rewardVo.getSkuId());
//            orderProduct.setSpuId(rewardVo.getSpuId());
//            orderProduct.setSkuId(rewardVo.getSkuId());
//            orderProduct.setSupplySkuId(rewardVo.getSupplySkuId());
//            orderProduct.setProductType(ProductEnum.S_ST_PRODUCT.value());
//            orderProduct.setProductName(rewardVo.getName());
//            orderProduct.setMainImage(rewardVo.getMainImage());
//            orderProduct.setAttrValues(rewardVo.getAttrValues());
//            orderProduct.setCurrentUnitPrice(BigDecimal.ZERO);
//            orderProduct.setPrimeCostFee(rewardVo.getPrimeCostFee());
//            orderProduct.setQuantity(rewardVo.getNum());
//            orderProduct.setNotShipQuantity(rewardVo.getNum());
//            orderProduct.setTotalPrice(BigDecimal.ZERO);
//            orderProduct.setCreated(new Timestamp(System.currentTimeMillis()));
//            orderProduct.setCreateBy(memberId);
//            orderProducts.add(orderProduct);
//        }
//        dao.batchSave(orderProducts,OrderProduct.class);

        SaveOrderVO saveOrderVO = new SaveOrderVO();
        saveOrderVO.setOrderId(orderId);
        callPayApi(request.getPaymentMethod(),request.getPayMethod(),order,memberId,openId, appId,saveOrderVO,request.getAddressRequest(),request.getMemberBankId() );

        return saveOrderVO;
    }


    @Override
    public String shipRepay(Long orderId, Long memberId, String openId, String appId) {
        Order order = dao.queryById(orderId,Order.class);
        if (order == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40052"));
        }
        if (!Objects.equals(OrderEnum.S_OS_UNPAID.value(),order.getStatus())){
            throw new BizCoreRuntimeException(MaErrorConstants.ORDER_HAS_EXPIRED);
        }
        String rewardIds = JSON.parseObject(order.getExtJson()).getString("rewardIds");

        List<Long> rewardIdList = Arrays.stream(rewardIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<MemberReward> rewards = this.queryMemberRewards(rewardIdList);
        if (ListUtil.isEmpty(rewards)){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_HAS_EXPIRED);
        }
        if (rewards.size() != rewardIdList.size() ){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_HAS_EXPIRED);
        }

        String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                order.getOrderSn(), openId, XcrmThreadContext.getChainId(), appId,order.getPaymentMethod(),callBackUrl);
        return payUrl;
    }

    @Override
    @RateLimiter(expired = 20,isLoop = true,message = "Decomposition process please try again later")
    public void memberRewardRecycle(MemberRewardRecycleRequest request,Long memberId, Boolean isAutoRecovery,Boolean isCard,String platform) {

        if (request.getRewardIds().size() > 2000){
            throw new BizCoreRuntimeException(MaErrorConstants.RECYCLE_COUNT_ERROR);
        }

        Set<Long> rewardIdSet = new HashSet<>(request.getRewardIds());
        List<MemberRewardVo> rewardVos = this.queryMemberRewardList(memberId,null, new ArrayList<>(rewardIdSet));
               if (ListUtil.isEmpty(rewardVos)){
                   throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40033"));
        }
        if (rewardVos.size() != rewardIdSet.size()){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_EXPIRED);
        }

        List<MemberRewardVo> unReclaimVos = rewardVos.stream().filter(a-> BooleanUtils.isFalse(a.getIsSupportRecycling())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(unReclaimVos)){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_UN_SUPPORT_RECYCLING);

        }

        List<MemberRewardVo> neUnapplyList = rewardVos.stream().filter(a->! Objects.equals(a.getStatus(), MemberRewardStatusEnum.S_MRS_UNAPPLY.value())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(neUnapplyList)){
            String rewardName = neUnapplyList.stream().map(MemberRewardVo::getName).collect(Collectors.joining(","));
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_NOT_UNAPPLY_NO_RECYCLE,rewardName);
        }

        BigDecimal totalRecycleFee =  rewardVos.stream().map(MemberRewardVo::getRecoveryFee).reduce(BigDecimal.ZERO,BigDecimal::add);
        if (!isAutoRecovery ){
            if (totalRecycleFee.compareTo(request.getTotalRecycleFee()) != 0){
                throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_RECYCLE_FEE_ERROR);
            }
        }

        String updateStatus =  MemberRewardStatusEnum.S_MRS_SOLD.value() ;

        for (MemberRewardVo rewardVo : rewardVos) {
//            int l =  this.updateMemberRewardStatus(Long.parseLong(rewardVo.getId()),updateStatus,MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
//            if (l < 1){
//                throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_RECYCLE_FAIL);
//            }
            if (rewardVo.getSupplySpuId() != null && rewardVo.getSupplySpuId().compareTo(0L) > 0){
                //还原供应商预占库存 updateOccuppyStockForRecycle
                QueryBuilder  updateOccuppyStockForRecycle = QueryBuilder.create("com.lewei.eshop.ma.product.box.updateOccuppyStockForRecycle")
                        .setParam("spuId",rewardVo.getSupplySpuId())
                        ;
                //dao.updateByMybatis(updateOccuppyStockForRecycle);
            }
            //还原预加盟商占库存 updateOccuppyStockForRecycle
            QueryBuilder  updateAgentOccuppyStockForRecycle = QueryBuilder.create("com.lewei.eshop.ma.product.box.updateOccuppyStockForRecycle")
                    .setParam("spuId",rewardVo.getSpuItemId())
                    ;
            //dao.updateByMybatis(updateAgentOccuppyStockForRecycle);

        }
        BigDecimal totalPrimeCostFee = BigDecimal.ZERO;
        JSONArray extJsonArray = new JSONArray();
        Map<Long, List<MemberRewardVo>> collect = rewardVos.stream().collect(Collectors.groupingBy(e -> e.getSpuId()));
        for (Long s : collect.keySet()) {
            List<MemberRewardVo> memberRewardVos = collect.get(s);
            MemberRewardVo memberRewardVo = memberRewardVos.get(0);
            BigDecimal primeCostFee = memberRewardVo.getPrimeCostFee();
            int num = memberRewardVos.size();
            if(null != primeCostFee){
                totalPrimeCostFee = totalPrimeCostFee.add(primeCostFee.multiply(BigDecimal.valueOf(num)));
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("num", num);
            jsonObject.put("mainImage", memberRewardVo.getMainImage());
            jsonObject.put("rewardCategoryName", memberRewardVo.getCategoryName());
            jsonObject.put("rewardName", memberRewardVo.getName());
            jsonObject.put("primeCostFee", primeCostFee);
            jsonObject.put("recycleFee", memberRewardVo.getRecoveryFee());
            jsonObject.put("rewardSource", memberRewardVo.getSource());
            extJsonArray.add(jsonObject);
        }

        Integer l = this.batchUpdateMemberRewardStatus(request.getRewardIds(),updateStatus,MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
        if (l < request.getRewardIds().size()){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_RECYCLE_FAIL);
        }



        MemberRewardRecycle rewardRecycle = new MemberRewardRecycle();
        rewardRecycle.setCreated(DateFormatUtils.getNow());
        rewardRecycle.setMemberId(memberId);
        rewardRecycle.setRecycleSn(CodecUtil.createOrderId());
        rewardRecycle.setRecycleSource(platform);
        rewardRecycle.setTotalRecycleFee(totalRecycleFee);
        rewardRecycle.setNum(rewardVos.size());
        rewardRecycle.setTotalPrimeCostFee(totalPrimeCostFee);
        rewardRecycle.setExtJson(JSONObject.toJSONString(extJsonArray));
        ChainConfig chainConfig = publicFeignClient.queryChainConfig();
        rewardRecycle.setTotalRecycleBalance(totalRecycleFee.multiply(chainConfig.getMoneyRatio()));
        rewardRecycle.setIsCard(isCard == null ? false : isCard);
        dao.save(rewardRecycle);

        TradeFlowReq tradeFlow = new TradeFlowReq();
        tradeFlow.setTradeContent(BizMessageSource.getInstance().getMessage("cem40100"));
        tradeFlow.setOrderId(0L);
        tradeFlow.setMemberId(memberId);
        tradeFlow.setTradeAmount(totalRecycleFee);
        tradeFlow.setTradeType(TradeTypeEnum.S_TT_PC_RECYCLE.value());


        tradeFlow.setTradeSource(platform);

        memberFeignClient.saveTradeFlow(tradeFlow);


        MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setBalance(totalRecycleFee);
        balanceRequest.setMemberId(memberId);
        balanceRequest.setPlId(rewardRecycle.getId());
        balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40100"));
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        memberFeignClient.memberBalanceHandle(balanceRequest);


        saveMemberRewardRecycleRef(rewardVos,rewardRecycle.getChainId(),rewardRecycle.getId(),MDCTraceUtils.getTraceId());


        try {
            //存储回收记录到es
            MemberRewardRecycleEsEntity rewardRecycleEsEntity = new MemberRewardRecycleEsEntity();
            BeanUtils.copyProperties(rewardRecycle,rewardRecycleEsEntity);
            commonEsProvider.save(rewardRecycleEsEntity);
        } catch (Exception e) {
            log.error("saveMemberRewardRecycle to es error id = " + rewardRecycle.getId(), e);
        }


    }

    @Override
    public String generateVerifyCode(Long memberRewardId) {

        MemberReward reward = dao.queryById(memberRewardId,MemberReward.class);
        if (reward == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40033"));
        }

        if (Objects.equals(MemberRewardStatusEnum.S_MRS_SOLD.value(),reward.getStatus())){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_SOLD_STATUS_VERIFY_CODE);
        }

        if (StringUtils.isNotEmpty(reward.getVerifyCode())){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_HAD_VERIFY_CODE);
        }


        while (true){
            String verifyCode = RandomStringUtils.randomNumeric(8);
            QueryBuilder queryBuilder = QueryBuilder.create("com.lewei.eshop.ma.product.box.queryVerifyCodeNum")
                    .setParam("verifyCode",verifyCode);

            Integer  l = dao.findForObj(queryBuilder,Integer.class);
            if (l < 1){
                reward.setVerifyCode(verifyCode);
                reward.setId(memberRewardId);
                reward.setUpdated(DateFormatUtils.getNow());
                dao.update(reward);
                return verifyCode;
            }
        }
    }


    @Override
    public OffLineMemberRewardVo queryOffLineMemberRewardDetail(Long memberRewardId) {

        Ssqb  queryOffLineMemberRewardDetail = Ssqb.create("com.lewei.eshop.ma.member.reward.queryOffLineMemberRewardDetail")
                .setParam("memberRewardId",memberRewardId);
        return dao.findForObj(queryOffLineMemberRewardDetail,OffLineMemberRewardVo.class);
    }

    private List<MemberReward> queryMemberRewards(List<Long> rewardIds){
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.in("id",rewardIds))
                .and(Restrictions.eq("dataStatus",1));
        return dao.queryList(query,MemberReward.class);
    }


    @Override
    public Map openProductTreasure(Long memberId, Long memberRewardId) {
        Ssqb queryMemberProductTreasure = Ssqb.create("com.lewei.eshop.ma.member.reward.queryMemberProductTreasure")
                .setParam("memberId", memberId)
                .setParam("memberRewardId", memberRewardId);
        MemberProductTreasureVo treasureVo = dao.findForObj(queryMemberProductTreasure, MemberProductTreasureVo.class);
        if (treasureVo == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_ERROR);
        }

        //更新状态
        int l = this.updateMemberRewardStatus(memberRewardId, MemberRewardStatusEnum.S_MRS_BOXED.value(), MemberRewardStatusEnum.S_MRS_UNBOXED.value());
        if (l == 0) {
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_ERROR);
        }
        //开奖处理
        List<MemberProductTreasureVo.Reward> rewards = treasureVo.getRewards();

        if (ListUtil.isNotEmpty(rewards)){
            //奖品排序 取回收价最小的
            List<Double> rewardOddsList = new ArrayList<>();
            List<Double> sortRewardOddsList = new ArrayList<>() ;
            //总概率 可以支持概率总和不等一
            Double sumOdds = rewards.stream().mapToDouble(MemberProductTreasureVo.Reward::getOdds).sum();
            Double tempOdds = 0d;
            for (MemberProductTreasureVo.Reward itemRewardVo : rewards) {
                tempOdds += itemRewardVo.getOdds();
                rewardOddsList.add(tempOdds / sumOdds);
            }

            //随机数在哪个概率区间内，则是哪个奖品
            double randomDouble = Math.random();
            sortRewardOddsList.addAll(rewardOddsList);
            //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
            sortRewardOddsList.add(randomDouble);
            Collections.sort(sortRewardOddsList);
            int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);

            MemberProductTreasureVo.Reward rewardVo =  rewards.get(lotteryIndex);

            MemberReward memberReward = new MemberReward();
            BeanUtils.copyProperties(rewardVo, memberReward);
            memberReward.setMemberId(memberId);
            memberReward.setCreated(DateFormatUtils.getNow());
            memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
            memberReward.setReceiveWay(RewardReceiveWayEnum.S_MRRW_PRODUCT_TREASURE.value());
            memberReward.setSpuId(rewardVo.getSpuItemId());
            memberReward.setOrderId(0L);
            memberReward.setBoxItemId(0L);
            memberReward.setIsGift(false);
            memberReward.setIsCard(false);
            memberReward.setRewardId(0L);
            dao.save(memberReward);

            ProductTreasureLog log = new ProductTreasureLog();
            log.setMemberRewardId(treasureVo.getMemberRewardId());
            log.setMemberId(memberId);
            log.setName(rewardVo.getName());
            log.setMainImage(rewardVo.getMainImage());
            log.setCreated(DateFormatUtils.getNow());
            log.setPrimeCostFee(rewardVo.getPrimeCostFee());
            log.setSpuId(treasureVo.getSpuId());
            log.setBoxItemId(treasureVo.getBoxItemId());
            log.setCategoryName(rewardVo.getCategoryName());
            dao.save(log);

            Map<String,String> map = new HashMap<>();
            map.put("name",memberReward.getName());
            map.put("mainImage",memberReward.getMainImage());
            return map;
        }

        return null;
    }


    @Override
    @RateLimiter(isLoop = true)
    public List<OpenTreasureRewardVo> openProductTreasure(Long memberId, List<Long> memberRewardIds) {

        if (memberRewardIds.size() > 100){
            throw new BizCoreRuntimeException(MaErrorConstants.OPEN_TREASURE_NUM_ERROR);

        }
        Ssqb queryMemberProductTreasure = Ssqb.create("com.lewei.eshop.ma.member.reward.queryMemberProductTreasureList")
                .setParam("memberId", memberId)
                .setParam("memberRewardIds", memberRewardIds);
        List<MemberProductTreasureVo> treasureVos = dao.findForList(queryMemberProductTreasure, MemberProductTreasureVo.class);
        if (treasureVos.size() != memberRewardIds.size()){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_ERROR);
        }

        //更新状态
        int l = this.batchUpdateMemberRewardStatus(memberRewardIds, MemberRewardStatusEnum.S_MRS_BOXED.value(), MemberRewardStatusEnum.S_MRS_UNBOXED.value());
        if (l != memberRewardIds.size() ) {
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_ERROR);
        }


        Map<Long,List<MemberProductTreasureVo>> mapping = treasureVos.stream().collect(Collectors.groupingBy(MemberProductTreasureVo::getSpuId));

        List<OpenTreasureRewardVo> openTreasureRewardVos = new ArrayList<>();

        Map<Long,BigDecimal> primeCostFeeMap = new HashMap<>();

        mapping.forEach((spuId,treasureVoList)->{

            Ssqb queryMemberProductTreasureRewards = Ssqb.create("com.lewei.eshop.ma.member.reward.queryMemberProductTreasureRewards")
                    .setParam("spuId", spuId);
            List<MemberProductTreasureVo.Reward> rewards = dao.findForList(queryMemberProductTreasureRewards, MemberProductTreasureVo.Reward.class);


            if (ListUtil.isNotEmpty(rewards)){
                //奖品排序 取回收价最小的
                List<Double> rewardOddsList = new ArrayList<>();
                List<Double> sortRewardOddsList = new ArrayList<>() ;
                //总概率 可以支持概率总和不等一
                Double sumOdds = rewards.stream().mapToDouble(MemberProductTreasureVo.Reward::getOdds).sum();
                Double tempOdds = 0d;
                for (MemberProductTreasureVo.Reward itemRewardVo : rewards) {
                    tempOdds += itemRewardVo.getOdds();
                    rewardOddsList.add(tempOdds / sumOdds);
                }

                for (MemberProductTreasureVo treasureVo : treasureVoList) {
                    //随机数在哪个概率区间内，则是哪个奖品
                    double randomDouble = Math.random();
                    sortRewardOddsList.addAll(rewardOddsList);
                    //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                    sortRewardOddsList.add(randomDouble);
                    Collections.sort(sortRewardOddsList);
                    int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);

                    MemberProductTreasureVo.Reward rewardVo =  rewards.get(lotteryIndex);

                    MemberReward memberReward = new MemberReward();
                    BeanUtils.copyProperties(rewardVo, memberReward);
                    memberReward.setMemberId(memberId);
                    memberReward.setCreated(DateFormatUtils.getNow());
                    memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                    memberReward.setReceiveWay(RewardReceiveWayEnum.S_MRRW_PRODUCT_TREASURE.value());
                    memberReward.setSpuId(rewardVo.getSpuItemId());
                    memberReward.setOrderId(0L);
                    memberReward.setBoxItemId(0L);
                    memberReward.setIsGift(false);
                    memberReward.setIsCard(rewardVo.getIsCard());
                    memberReward.setRewardId(0L);
                    dao.save(memberReward);

                    ProductTreasureLog log = new ProductTreasureLog();
                    log.setMemberRewardId(treasureVo.getMemberRewardId());
                    log.setMemberId(memberId);
                    log.setName(rewardVo.getName());
                    log.setMainImage(rewardVo.getMainImage());
                    log.setCreated(DateFormatUtils.getNow());
                    log.setPrimeCostFee(rewardVo.getPrimeCostFee());
                    log.setSpuId(treasureVo.getSpuId());
                    log.setBoxItemId(treasureVo.getBoxItemId());
                    log.setCategoryName(rewardVo.getCategoryName());
                    dao.save(log);
                    sortRewardOddsList.clear();

                    OpenTreasureRewardVo openTreasureRewardVo = new OpenTreasureRewardVo();
                    openTreasureRewardVo.setMainImage(memberReward.getMainImage());
                    openTreasureRewardVo.setName(memberReward.getName());
                    openTreasureRewardVo.setCategoryName(rewardVo.getCategoryName());
                    openTreasureRewardVos.add(openTreasureRewardVo);

                    if (treasureVo.getCategoryId() != null && treasureVo.getCategoryId() != 0L){
                        BigDecimal primeCostFee = primeCostFeeMap.get(treasureVo.getCategoryId());
                        if (primeCostFee == null){
                            primeCostFee = rewardVo.getPrimeCostFee();
                        }else {
                            primeCostFee = primeCostFee.add(rewardVo.getPrimeCostFee());
                        }
                        primeCostFeeMap.put(treasureVo.getCategoryId(), primeCostFee);
                    }

                    // 赠送宝箱开箱成本
                    if (treasureVo.getBoxItemId() == 0L) {
                        ActivityRecord record = new ActivityRecord();
                        record.setActivityId(0L);
                        record.setMemberId(memberId);
                        record.setActivityType(getActivityType(treasureVo.getReceiveWay()));
                        record.setGiftType("treasure");
                        record.setCreated(DateFormatUtils.getNow());
                        record.setEndPrimeCostFee(rewardVo.getPrimeCostFee());
                        dao.save(record);
                    }
                }
            }
        });

        this.saveMemberCategoryData(primeCostFeeMap,memberId);

        return openTreasureRewardVos;
    }

    private void callPayApi(String payType, String payMethod, Order order, Long memberId, String openId, String appId, SaveOrderVO returnVo ,PrePayAddressRequest addressRequest,Long memberBankId) {

        if (order.getOrderMoney().compareTo(BigDecimal.ZERO) > 0 ){
            if (OrderUtil.isCashPay(payType)) {
                Member member = dao.queryById(memberId, Member.class);
                Map<String, Object> extJsonMap = new HashMap<>();
                extJsonMap.put("shippingUser", order.getShippingUser());
                extJsonMap.put("telNumber", order.getTelNumber());
                extJsonMap.put("province", addressRequest.getProvince());
                extJsonMap.put("city", addressRequest.getCity());
                extJsonMap.put("area", addressRequest.getArea());
                extJsonMap.put("address", addressRequest.getAddress());
                extJsonMap.put("memberBankId",memberBankId);
                extJsonMap.put("orderId",order.getId());
                //调取微信支付
                String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                        order.getOrderSn(), openId, XcrmThreadContext.getChainId(), appId,payType,callBackUrl,null,payMethod,null,member.getMobile(),extJsonMap);
                returnVo.setPayUrl(payUrl);
            } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payType)) {
                memberFeignClient.memberBalancePay(new MemberBalancePayRequest(null, memberId, order.getOrderMoney(), order.getOrderTitle(), order.getId()));
                this.afterPay(order);
            }
        }else {
            this.afterPay(order);
        }


    }

    public void afterPay(Order order) {

        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("orderCode", order.getOrderSn());
                payCallbackProcessorV2.onPayShipTradeSuccess(order,jsonObject);
            }
        });
    }


    public void saveMemberRewardRecycleRef(List<MemberRewardVo> rewardVos,Long chainId,Long rewardRecycleId,String traceId) {

        taskExecutor.execute(() -> {
                String lang = XcrmThreadContext.getLocale();
                XcrmThreadContext.setChainId(chainId);
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                XcrmThreadContext.setLocale(lang);
                MDCTraceUtils.putTraceId(traceId);

            try {
                List<MemberRewardRecycleRef> rewardRecycleRefs = new ArrayList<>();
                MemberRewardRecycleRef rewardRecycleRef;

                for (MemberRewardVo rewardVo : rewardVos) {
                    rewardRecycleRef = new MemberRewardRecycleRef();
                    rewardRecycleRef.setCreated(DateFormatUtils.getNow());
                    rewardRecycleRef.setMemberRewardId(Long.parseLong(rewardVo.getId()));
                    rewardRecycleRef.setRecycleFee(rewardVo.getRecoveryFee());
                    rewardRecycleRef.setPrimeCostFee(rewardVo.getPrimeCostFee());
                    rewardRecycleRef.setMainImage(rewardVo.getMainImage());
                    rewardRecycleRef.setRewardName(rewardVo.getName());
                    rewardRecycleRef.setRewardCategoryName(rewardVo.getCategoryName());
                    rewardRecycleRef.setRewardSource(rewardVo.getSource());
                    rewardRecycleRef.setRecycleId(rewardRecycleId);
                    rewardRecycleRefs.add(rewardRecycleRef);
                }
                dao.batchSave(rewardRecycleRefs,MemberRewardRecycleRef.class);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            } finally {
                XcrmThreadContext.removeChainId();
                XcrmThreadContext.removeAccessType();
                XcrmThreadContext.removeLocale();
                MDCTraceUtils.removeTraceId();
            }
        });
    }

    public void saveMemberCategoryData(Map<Long,BigDecimal> primeCostFeeMap, Long memberId){
        if (MapUtils.isNotEmpty(primeCostFeeMap)){
            Long chainId = XcrmThreadContext.getChainId();
            String traceId = MDCTraceUtils.getTraceId();
            taskExecutor.execute(() -> {
                XcrmThreadContext.setChainId(chainId);
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                MDCTraceUtils.putTraceId(traceId);
                List<MemberCategoryDailyDataReq> memberCategoryDailyDataReqs = new ArrayList<>();
                primeCostFeeMap.forEach((id, primeCostFee) -> {
                    MemberCategoryDailyDataReq memberCategoryDailyDataReq = new MemberCategoryDailyDataReq();
                    memberCategoryDailyDataReq.setCategoryId(id);
                    memberCategoryDailyDataReq.setMemberId(memberId);
                    memberCategoryDailyDataReq.setEndPrimeCostFee(primeCostFee);
                    memberCategoryDailyDataReqs.add(memberCategoryDailyDataReq);
                });
                memberFeignClient.batchSaveMemberCategoryDailyData(memberCategoryDailyDataReqs);
            });
        }
    }

    private String getActivityType(String receiveWay){
        if (RewardReceiveWayEnum.S_MRRW_BUSINESS.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_ISSUE.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_REWARD.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_REWARD_GIFT.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_MEMBER_LEVEL.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_MEMBER_LEVEL.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_WELFARE_CODE.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_WELFARE_CODE.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_NEWCOMER_GIFT_BAG.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_NEWCOMER_GIFT_BAG.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_REWARD_MINE.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_REWARD_MINE.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_SIGN.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_SIGN.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_POINT_TASK.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_POINT_TASK.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_FIGHT.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_FIGHT.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_TREASURE_ACTIVITY.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_TREASURE_ACTIVITY.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_POSTCARD.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_POSTCARD.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_NEWCOMER_ACTIVITY.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_NEWCOMER_ACTIVITY.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_LOSS_COMPENSATE.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_LOSS_COMPENSATE.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_ROLL.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_ROLL.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_TURNTABLE.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_TURNTABLE.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_OQLEVEL.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_OQ_LEVEL.value();
        }
        if (RewardReceiveWayEnum.S_MRRW_COLLISION.value().equals(receiveWay)){
            return ActivityTypeEnum.S_AT_COLLISION.value();
        }
        return "";
    }
}
