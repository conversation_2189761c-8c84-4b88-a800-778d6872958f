package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lewei.common.redis.template.RedisRepository;
import com.lewei.eshop.auth.ma.SSOMaAppCache;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.member.MemberBalanceTradeTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.doll.DollBoxPayReq;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.vo.doll.DollDetailVo;
import com.lewei.eshop.common.vo.doll.DollSimpleDetailDto;
import com.lewei.eshop.entity.app.ApplicationConfig;
import com.lewei.eshop.entity.doll.Doll;
import com.lewei.eshop.entity.doll.types.DollStatusEnum;
import com.lewei.eshop.entity.member.types.MemberAuthorityEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaDollService;
import com.lewei.eshop.ma.biz.IMaMemberService;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.lewei.eshop.ma.pay.DollCallbackProcessor;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.sso.MemberCache;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * 抓娃娃机业务实现
 *
 * <AUTHOR>
 * @since 2023/8/7
 */
@Service
@Transactional
public class MaDollServiceImpl implements IMaDollService {

    private final String callBackUrl = "/api/ma/pay/doll/callback";


    @Autowired
    private AppFeignClient appFeignClient;
    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private DollCallbackProcessor dollCallbackProcessor;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private IMaMemberService memberService;
    @Autowired
    private RedisRepository redisRepository;

    @Override
    public Pagination queryDoll(PageRequest pageRequest,String showConfig) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.doll.queryDoll")
                .setParam("showConfig", showConfig)
                .setParam("status", DollStatusEnum.S_DS_SJ.value())
                .setParam("pageSize", pageRequest.getPageSize())
                .setParam("pageNo", pageRequest.getPageNo());
        return dao.findForPage(ssqb);
    }

    @Override
    public DollDetailVo queryDollDetail(Long id) {
        return appFeignClient.queryDollDetail(id);
    }

    @Override
    public SaveOrderVO dollBoxPay(DollBoxPayReq payReq, MemberCache memberCache, SSOMaAppCache appCache, Long chainId) {
        // 不可参与抓取游戏
        boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_PART_GRAB_GAME, memberCache.getId());
        if (isAuthority) {
            throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_PART_DIY_BOX);
        }
        Doll doll = dao.queryById(payReq.getDollId(), Doll.class);
        if (doll == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.RESOURCE_NOT_EXIST_OR_DELETED);
        }
        if (DollStatusEnum.S_DS_XJ.value().equals(doll.getStatus())){
            throw new BizCoreRuntimeException(MaErrorConstants.WX_BE_TAKEN_OFF);
        }
        if (payReq.getPrice().compareTo(doll.getPriceFee()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }

        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberCache.getId());
        order.setOpenId(memberCache.getOpenId());
        order.setPaymentMethod(payReq.getPaymentMethod());
        order.setOrderMoney(payReq.getPrice());
        order.setPaymentMoney(payReq.getPrice());

        order.setOrderTitle(doll.getName());
        order.setOrderType(OrderEnum.S_OOT_DOLL_BOX.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberCache.getId());
        order.setPlatform(appCache.getPlatform());
        order.setOrderTradeType(OrderTradeTypeEnum.S_OTT_TRADE.value());

        Map<String,Object> map = new HashMap<>();
        map.put("dollId",payReq.getDollId());
        map.put("mainImage",doll.getMainImage());
        map.put("name",doll.getName());
        order.setExtJson(JSONObject.toJSONString(map));
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payReq.getPaymentMethod())){
            BigDecimal moneyRatio = publicFeignClient.queryChainConfig().getMoneyRatio();
            order.setOrderBalance(payReq.getPrice().multiply(moneyRatio));
        }

        dao.save(order);


        SaveOrderVO returnVo = new SaveOrderVO();
        returnVo.setOrderId(order.getId());

        if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(payReq.getPaymentMethod())) {
            returnVo.setOrderId(order.getId());
            //调取微信支付
            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), memberCache.getOpenId(), chainId, appCache.getAppId(),payReq.getPaymentMethod(),
                    callBackUrl);
            returnVo.setPayUrl(payUrl);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payReq.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(order.getPaymentMoney());
            // 买家
            balanceRequest.setMemberId(memberCache.getId());
            balanceRequest.setPlId(order.getId());
            // 查询卖家
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40162"));
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_DOLL.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterPay(order);
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }
        return returnVo;
    }

    private void afterPay(Order order) {
        taskExecutor.execute(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderCode", order.getOrderSn());
            dollCallbackProcessor.onPayTradeSuccess(order, jsonObject);
        });
    }

    @Override
    public DollSimpleDetailDto queryDollSimpleDetailDto(Long dollId) {
        Ssqb queryDollSimpleDetailDto = Ssqb.create("com.lewei.eshop.ma.doll.queryDollSimpleDetailDto")
                .setParam("dollId",dollId);
        return dao.findForObj(queryDollSimpleDetailDto,DollSimpleDetailDto.class);
    }

    @Override
    public Map queryDollOrderResult(Long orderId) {
        String result = (String) redisRepository.get(MaCacheKeys.DOLL_RESULT + orderId);
        if (StringUtils.isNotEmpty(result)){
            return JSON.parseObject(result,Map.class);
        }
        return null;
    }

    @Override
    public Pagination queryDollRewardLogsBottom(Long dollId, Boolean opportunity, Integer pageNo, Integer pageSize) {
        Boolean isWin = null;
        if (BooleanUtils.isFalse(opportunity)){
            isWin = false;
        }
        Ssqb queryProductBoxItemPage = Ssqb.create("com.lewei.eshop.ma.doll.queryDollRewardLogsBottom")
                .setParam("dollId",dollId)
                .setParam("pageSize", pageSize)
                .setParam("pageNo", pageNo)
                .setParam("isWin", isWin);
        return dao.findForPage(queryProductBoxItemPage);
    }
}
