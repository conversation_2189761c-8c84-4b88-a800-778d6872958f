package com.lewei.eshop.ma.resource;

import com.lewei.eshop.auth.ma.MaBaseAuthedResource;
import com.lewei.eshop.auth.ma.MaJerseyConstants;
import com.lewei.eshop.ma.sso.MemberCache;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Context;

/**
 * 授权登录后 资源基类
 *
 * <AUTHOR>
 */
public class BaseAuthedResource extends MaBaseAuthedResource {

    @Context
    private ContainerRequestContext containerRequestContext;

    /**
     * 会员id
     *
     * @return
     */
    public Long getMemberId() {
        MemberCache memberCache = getMemberCache();
        if (memberCache != null) {
            return memberCache.getId();
        }
        return null;
    }
    public Long getLevelId() {
        MemberCache memberCache = getMemberCache();
        if (memberCache != null) {
            return memberCache.getLevelId();
        }
        return null;
    }

    public String getUserCode() {
        MemberCache memberCache = getMemberCache();
        if (memberCache != null) {
            return memberCache.getUserCode();
        }
        return null;
    }
    public String getOpenId() {
        MemberCache memberCache = getMemberCache();
        if (memberCache != null) {
            return memberCache.getOpenId();
        }
        return null;
    }
    public Long getPayConfigId() {
        MemberCache memberCache = getMemberCache();
        if (memberCache != null) {
            return memberCache.getPayConfigId();
        }
        return null;
    }

    public MemberCache getMemberCache() {
        return (MemberCache) containerRequestContext.getProperty(MaJerseyConstants.REQ_MEMBER);
    }
}
