package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lewei.common.redis.template.RedisRepository;
import com.lewei.eshop.auth.annotation.RateLimiter;
import com.lewei.eshop.auth.ma.SSOMaAppCache;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.OrderUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.member.MemberBalanceTradeTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.data.product.ProductEnum;
import com.lewei.eshop.common.request.activity.ActivityGIftReq;
import com.lewei.eshop.common.request.collision.*;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.member.MemberRecordReq;
import com.lewei.eshop.common.vo.collision.*;
import com.lewei.eshop.entity.activity.types.ActivityTypeEnum;
import com.lewei.eshop.entity.app.coupon.MemberCoupon;
import com.lewei.eshop.entity.collision.*;
import com.lewei.eshop.entity.collision.type.CollisionGiftTypeEnum;
import com.lewei.eshop.entity.collision.type.CollisionTypeEnum;
import com.lewei.eshop.entity.member.MemberPictureFrameRef;
import com.lewei.eshop.entity.member.MemberReward;
import com.lewei.eshop.entity.member.types.MemberRewardStatusEnum;
import com.lewei.eshop.entity.member.types.RewardReceiveWayEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.order.types.OrderTradeTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IMaCollisionService;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.pay.CollisionCallbackProcessor;
import com.lewei.eshop.ma.pay.CollisionConCallbackProcessor;
import com.lewei.eshop.ma.pay.DiyBoxCallbackProcessor;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.sso.MemberCache;
import com.lewei.global.IGlobalHandler;
import com.lewei.log.trace.MDCTraceUtils;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.db.saas.IIdWorker;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.lewei.eshop.entity.collision.type.CollisionRuleTypeEnum.*;

/**
 * 碰撞游戏业务实现
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Service
@Transactional
@Slf4j
public class IMaCollisionServiceImpl implements IMaCollisionService {

    private final String callBackUrl = "/api/ma/pay/collision";

    private final String callConBackUrl = "/api/ma/pay/collision/con";

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private AppFeignClient appFeignClient;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private CollisionCallbackProcessor collisionCallbackProcessor;

    @Autowired
    private CollisionConCallbackProcessor collisionConCallbackProcessor;

    @Autowired
    private RedisRepository redisRepository;

    @Autowired
    private IIdWorker idWorker;

    @Autowired
    private IGlobalHandler globalHandler;


    @Override
    public Pagination queryCollisionList(MaCollisionQueryRequest request) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionList")
                .setParam("type", request.getType())
                .setParam("viewRange", request.getViewRange())
                .setParam("pageNo",request.getPageNo())
                .setParam("pageSize",request.getPageSize());
        return dao.findForPage(ssqb);
    }

    @Override
    public MaCollisionDetailVo queryCollisionDetail(Long id) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionDetail")
                .setParam("id", id);
        return dao.findForObj(ssqb,MaCollisionDetailVo.class);
    }

    @Override
    public Pagination queryCollisionOrder(MaCollisionOrderQueryRequest request, Long memberId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionOrderList")
                .setParam("status", request.getStatus())
                .setParam("memberId", memberId)
                .setParam("pageNo",request.getPageNo())
                .setParam("pageSize",request.getPageSize());
        return dao.findForPage(ssqb);
    }

    @Override
    public Integer queryCollisionOrderCount(String status, Long memberId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionOrderCount")
                .setParam("status", status)
                .setParam("memberId", memberId);
        return dao.findForInt(ssqb);
    }

    /**
     * 查询订单详情
     * @param orderId
     * @return
     */
    @Override
    public MaCollisionOrderDetailVo queryCollisionOrderDetail(Long orderId){
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionOrderDetail")
                .setParam("orderId", orderId);
       return dao.findForObj(ssqb,MaCollisionOrderDetailVo.class);
    };

    @Override
    public MaMemberCollisionInfoVo queryMemberCollisionInfo(Long orderId, Long memberId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryMemberCollisionInfo")
                .setParam("memberId", memberId)
                .setParam("orderId",orderId);
        return dao.findForObj(ssqb,MaMemberCollisionInfoVo.class);
    }

    @Override
    public List<MaCollisionStageContinuedVo> queryCollisionStageContinued(Long collisionId, Integer stage) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionStageContinued")
                .setParam("collisionId", collisionId)
                .setParam("stage",stage);
        return dao.findForList(ssqb,MaCollisionStageContinuedVo.class);
    }

    @Override
    public List<MaCollisionStageRuleVo> queryCollisionStageRule(Long collisionId, Integer stage) {
        Long chainId = XcrmThreadContext.getChainId();
        List<MaCollisionStageRuleVo> list = (List<MaCollisionStageRuleVo>)redisRepository.getRedisTemplate().opsForHash().get(MaCacheKeys.COLLISION_STAGE_RULE + chainId, collisionId + "-" + stage);
        if(ListUtil.isNotEmpty(list)){
            return list;
        }
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionStageRule")
                .setParam("collisionId", collisionId)
                .setParam("stage",stage);
        list = dao.findForList(ssqb,MaCollisionStageRuleVo.class);
        if(ListUtil.isNotEmpty(list)){
            redisRepository.getRedisTemplate().opsForHash().put(MaCacheKeys.COLLISION_STAGE_RULE + chainId, collisionId + "-" + stage, list);
        }
        return list;
    }

    public List<MaCollisionPrizeVo> queryCollisionPrize(Long collisionId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.queryCollisionPrize")
                .setParam("collisionId", collisionId);
        return dao.findForList(ssqb,MaCollisionPrizeVo.class);
    }

    @Override
    @RateLimiter(isLoop = true)
    public MaCollisionFinishVo collisionFinish(MaCollisionStartReq request, MemberCache memberCache) {
        MaMemberCollisionInfoVo collisionVo = queryMemberCollisionInfo(request.getOrderId(), memberCache.getId());
        if(Objects.isNull(collisionVo)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_NOT_FOUND);
        }
        if(!collisionVo.getStatus().equals(OrderEnum.S_OS_PROGRESS.value())){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_STATUS_IS_ERROR_NOT_FINISH);
        }
        if(collisionVo.getLeftNum() > 0){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_LEFTNUM_IS_ERROR_NOT_FINISH);
        }
        Date now = DateFormatUtils.getNow();
        Long memberId = memberCache.getId();


        //棋盘上奖品入奖池
        String gameJson = collisionVo.getGameJson();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, MaCollisionPrizeVo> map = null;
        try {
            map = objectMapper.readValue(gameJson, objectMapper.getTypeFactory().constructMapType(Map.class, String.class, MaCollisionPrizeVo.class));
            System.out.println(map);
        } catch (JsonProcessingException e) {
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_GAME_JSON_IS_ERROR);
        }
        for (int i = 0; i < 10; i++) {
            MaCollisionPrizeVo prizeVo = map.get(String.valueOf(i));
            if (Objects.nonNull(prizeVo)) {
                //对对碰奖品记录
                MemberCollisionPrize memberCollisionPrize = new MemberCollisionPrize();
                memberCollisionPrize.setMemberCollisionId(collisionVo.getId());
                memberCollisionPrize.setOrderId(collisionVo.getOrderId());
                memberCollisionPrize.setSpuId(prizeVo.getSpuId());
                memberCollisionPrize.setSkuId(prizeVo.getSkuId());
                memberCollisionPrize.setProductName(prizeVo.getName());
                memberCollisionPrize.setProductMainImage(prizeVo.getMainImage());
                memberCollisionPrize.setPrimeCostFee(prizeVo.getPrimeCostFee());
                memberCollisionPrize.setCreated(now);
                memberCollisionPrize.setDataStatus(Boolean.TRUE);
                dao.save(memberCollisionPrize);
            }
        }

        JSONArray jsonArray = new JSONArray();

        //查询次数奖励
        MaCollisionDetailVo collisionDetailVo = this.queryCollisionDetail(collisionVo.getCollisionId());
        if (BooleanUtils.isTrue(collisionDetailVo.getIsTimeGift())){
            List<MaCollisionDetailVo.CollisionGift> collisionGifts = collisionDetailVo.getCollisionGifts();
            if (ListUtil.isNotEmpty(collisionGifts)){
                //对碰次数奖励
                Optional<MaCollisionDetailVo.CollisionGift> max = collisionGifts.stream()
                        .filter(collisionGift -> collisionGift.getTimes() <= collisionVo.getGiftTimes()
                                && CollisionGiftTypeEnum.S_CGT_DP.value().equals(collisionGift.getType()))
                        .max(Comparator.comparingInt(MaCollisionDetailVo.CollisionGift::getTimes));
                MaCollisionDetailVo.CollisionGift collisionGift = max.orElse(null);
                if (Objects.nonNull(collisionGift)){
                        JSONObject prize = JSON.parseObject(collisionGift.getGiftJson());
                        prize.put("num",1);
                        prize.put("type",CollisionGiftTypeEnum.S_CGT_DP.value());
                        jsonArray.add(prize);
                        //对对碰奖品记录
                        saveGiftMemberCollisionPrize(collisionVo, prize, now);
                }

                //全家福次数奖励
                //获得全家福次数
                Integer giftQjfTimes = collisionVo.getGiftQjfTimes();
                List<MaCollisionDetailVo.CollisionGift> collisionQjfGifts = collisionGifts.stream()
                        .filter(collisionQjfGift -> CollisionGiftTypeEnum.S_CGT_QJF.value().equals(collisionQjfGift.getType())).collect(Collectors.toList());
                //没有规则或则全家福次数为0时
                if (ListUtil.isNotEmpty(collisionQjfGifts) && giftQjfTimes > 0){
                    for (MaCollisionDetailVo.CollisionGift collisionQjfGift : collisionQjfGifts) {

                        JSONObject prize = JSON.parseObject(collisionQjfGift.getGiftJson());
                        int giftNum = giftQjfTimes;
                        if(Objects.nonNull(prize.getInteger("num"))){
                            giftNum = prize.getInteger("num") * giftQjfTimes;
                        }
                        for (int i = 0; i < giftNum; i++) {
                            //对对碰奖品记录
                            saveGiftMemberCollisionPrize(collisionVo, prize, now);
                        }
                        prize.put("num",giftNum);
                        prize.put("type",CollisionGiftTypeEnum.S_CGT_QJF.value());
                        jsonArray.add(prize);
                    }

                }


                //许愿次数奖励
                Integer giftXYTimes = collisionVo.getGiftXYTimes();
                List<MaCollisionDetailVo.CollisionGift> collisionXyGifts = collisionGifts.stream()
                        .filter(collisionQjfGift -> CollisionGiftTypeEnum.S_CGT_XY.value().equals(collisionQjfGift.getType())).collect(Collectors.toList());
                if (ListUtil.isNotEmpty(collisionXyGifts) && giftXYTimes > 0){
                    for (MaCollisionDetailVo.CollisionGift collisionXyGift : collisionXyGifts) {
                        JSONObject prize = JSON.parseObject(collisionXyGift.getGiftJson());
                        int giftNum = giftXYTimes;
                        if(Objects.nonNull(prize.getInteger("num"))){
                            giftNum = prize.getInteger("num") * giftXYTimes;
                        }
                        for (int i = 0; i < giftNum; i++) {
                            //对对碰奖品记录
                            saveGiftMemberCollisionPrize(collisionVo, prize, now);
                        }
                        prize.put("num",giftNum);
                        prize.put("type",CollisionGiftTypeEnum.S_CGT_XY.value());
                        jsonArray.add(prize);
                    }
                }
            }
        }

        //奖品入赏袋
        Ssqb queryPrize = Ssqb.create("com.lewei.eshop.ma.collision.queryMemberCollisionPrizeGroup")
                .setParam("orderId",request.getOrderId())
                .setParam("memberCollisionId", collisionVo.getId());
        List<MaCollisionPrizeGroupVo> collisionPrizes = dao.findForList(queryPrize, MaCollisionPrizeGroupVo.class);
        List<MemberReward> memberRewards = new ArrayList<>();
        MemberReward memberReward;
        BigDecimal collisionCost = BigDecimal.ZERO;
        //宝箱json
        JSONArray treasureJsonArray = new JSONArray();
        for (MaCollisionPrizeGroupVo collisionPrize : collisionPrizes) {
            for (int i = 0; i < collisionPrize.getNum(); i++) {
                if(collisionPrize.getSpuType().equals(ProductEnum.S_ST_TREASURE.value())){
                    //处理宝箱 (宝箱记录在营销统计中)
                    JSONObject treasureJson = new JSONObject();
                    treasureJson.put("name", collisionPrize.getProductName());
                    treasureJson.put("price", collisionPrize.getPriceFee());
                    treasureJson.put("spuId", collisionPrize.getSpuId());
                    treasureJson.put("skuId", collisionPrize.getSkuId());
                    treasureJson.put("amount", 1);
                    treasureJson.put("mainImage", collisionPrize.getProductMainImage());
                    treasureJson.put("primeCostFee", collisionPrize.getPrimeCostFee());
                    treasureJsonArray.add(treasureJson);
                }else{
                    //奖品
                    collisionCost =  collisionCost.add(collisionPrize.getPrimeCostFee());
                    memberReward = new MemberReward();
                    memberReward.setIsGift(false);
                    memberReward.setMainImage(collisionPrize.getProductMainImage());
                    memberReward.setName(collisionPrize.getProductName());
                    memberReward.setOrderId(request.getOrderId());
                    memberReward.setMemberId(memberId);
                    memberReward.setBoxItemId(0L);
                    memberReward.setCreated(now);
                    memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                    memberReward.setReceiveWay(RewardReceiveWayEnum.S_MRRW_COLLISION.value());
                    memberReward.setRewardId(0L);
                    memberReward.setSource("ma");
                    memberReward.setPrimeCostFee(collisionPrize.getPrimeCostFee());
                    memberReward.setSpuId(collisionPrize.getSpuId());
                    memberRewards.add(memberReward);
                }

            }
        }
        dao.batchSave(memberRewards,MemberReward.class);
        //这里开始发奖处理
        List<ActivityGIftReq> reqs = new ArrayList<>();
        //处理宝箱
        if(ListUtil.isNotEmpty(treasureJsonArray)){
            JSONObject treasureGift = new JSONObject();
            treasureGift.put("type", "treasure");
            treasureGift.put("treasureJson", treasureJsonArray);
            reqs.add(new ActivityGIftReq(treasureGift.toJSONString(), collisionVo.getId(),
                    Collections.singletonList(memberId), ActivityTypeEnum.S_AT_COLLISION.value(), "treasure",
                    BizMessageSource.getInstance().getMessage("cem40176"), null, false));
        }
        //处理积分
        JSONObject gift = new JSONObject();
        gift.put("type", "score");
        gift.put("amount", collisionVo.getScoreTotal());
        reqs.add(new ActivityGIftReq(gift.toJSONString(),collisionVo.getId(),
                Collections.singletonList(memberId), ActivityTypeEnum.S_AT_COLLISION.value(),"score",
                BizMessageSource.getInstance().getMessage("cem40176"),null,false));

       //批量给发奖
        appFeignClient.batchHandelActivityGift(reqs);


        //更改游戏订单状态
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.collision.updateCollisionOrderFinish")
                .setParam("orderId", request.getOrderId());
        dao.updateByMybatis(ssqb);

        //记录游戏时间
        MemberCollision memberCollision = new MemberCollision();
        memberCollision.setId(collisionVo.getId());
        memberCollision.setFinishTime(DateFormatUtils.getNow());
        memberCollision.setGiftJson(jsonArray.toJSONString());
        dao.update(memberCollision);

        //处理游戏结束统计信息
        //查询购买金额
        Order order = dao.queryById(collisionVo.getOrderId(), Order.class);
        //续包金额
        Ssqb queryMoney = Ssqb.create("com.lewei.eshop.ma.collision.querySumCollisionOrderMoney")
                .setParam("orderId", request.getOrderId());
        BigDecimal money = dao.findForObj(queryMoney, BigDecimal.class);

        MemberRecordReq recordReq = new MemberRecordReq();
        recordReq.setMemberId(memberId);
        recordReq.setCollisionCost(collisionCost);
        recordReq.setCollisionProductNum(memberRewards.size());
        //主订单金额+续包金额
        recordReq.setCollisionMoney(order.getPaymentMoney().add(money));
        memberFeignClient.insertOrUpdateMemberRecord(recordReq);

        Date date = globalHandler.getChainLocalDate();
        Ssqb insertMemberCollisionStatistics = Ssqb.create("com.lewei.eshop.ma.collision.insertMemberCollisionStatistics")
                .setParam("id",idWorker.nextId())
                .setParam("memberId",memberId)
                .setParam("storageDataTime",date)
                .setParam("collisionId",collisionVo.getCollisionId())
                .setParam("totalFee",recordReq.getCollisionMoney())
                .setParam("totalPrimeCost",recordReq.getCollisionCost())
                .setParam("num", 1);
        dao.updateByMybatis(insertMemberCollisionStatistics);


        //返回获奖信息
        MaCollisionFinishVo finishVo = new MaCollisionFinishVo();
        finishVo.setGiftScore(collisionVo.getScoreTotal());
        finishVo.setMemberPrize(collisionPrizes);
        return finishVo;

    }

    /**
     * 保存对对碰赠送奖品信息
     * @param collisionVo
     * @param prize
     * @param now
     */
    private void saveGiftMemberCollisionPrize(MaMemberCollisionInfoVo collisionVo, JSONObject prize, Date now) {
        MemberCollisionPrize memberCollisionPrize = new MemberCollisionPrize();
        memberCollisionPrize.setMemberCollisionId(collisionVo.getId());
        memberCollisionPrize.setOrderId(collisionVo.getOrderId());
        memberCollisionPrize.setSpuId(prize.getLong("spuId"));
        memberCollisionPrize.setSkuId(prize.getLong("skuId"));
        memberCollisionPrize.setProductName(prize.getString("name"));
        memberCollisionPrize.setProductMainImage(prize.getString("mainImage"));
        memberCollisionPrize.setPrimeCostFee(prize.getBigDecimal("primeCostFee"));
        memberCollisionPrize.setCreated(now);
        memberCollisionPrize.setDataStatus(Boolean.TRUE);
        dao.save(memberCollisionPrize);
    }


    @Override
    public void updateLuckSkuCollision(MaCollisionLuckSkuRequest request, Long memberId) {
        SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderId", request.getOrderId()))
                .and(Restrictions.eq("memberId", memberId));
        MemberCollision memberCollision = dao.query(queryBuilder, MemberCollision.class);
        if(Objects.isNull(memberCollision)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_NOT_FOUND);
        }
        //校验幸运奖品是否在奖品列表内
        SaasQueryBuilder queryPrize = SaasQueryBuilder.where(Restrictions.eq("collisionId", memberCollision.getCollisionId()))
                .and(Restrictions.eq("skuId", request.getLuckSkuId()));
        Integer i = dao.queryForInt(queryPrize, CollisionPrize.class);
        if(i <= 0){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_LUCKY_SKU_IS_ERROR);
        }

        memberCollision.setLuckySkuId(request.getLuckSkuId());
        dao.update(memberCollision);
    }

    @Override
    @RateLimiter(isLoop = true)
    public Map<String, Object> collisionStart(MaCollisionStartReq request, MemberCache memberCache) {
        //查询对局信息
        MaMemberCollisionInfoVo collisionVo = queryMemberCollisionInfo(request.getOrderId(), memberCache.getId());

        if(Objects.isNull(collisionVo)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_NOT_FOUND);
        }

        Long collisionId = collisionVo.getCollisionId();
        //奖品列表
        List<MaCollisionPrizeVo> prizeVos = this.queryCollisionPrize(collisionId);
        //当前阶段规则
        List<MaCollisionStageRuleVo> ruleVos = this.queryCollisionStageRule(collisionId, collisionVo.getStage());
        //规则 奖品  为空时 无法正常进行游戏
        if(ListUtil.isEmpty(ruleVos) || ListUtil.isEmpty(prizeVos)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_MEMBER_IS_NOT_FIND);

        }
        //校验游戏状态
        if(!collisionVo.getStatus().equals(OrderEnum.S_OS_PROGRESS.value())){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_STATUS_IS_ERROR_NOT_GAME);
        }
        //校验幸运色
        if(Objects.isNull(collisionVo.getLuckySkuId())){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_LUCKY_SKU_IS_NOT_FIND);
        }

        //对对碰游戏类型(普通/闯关)
        String collisionType = collisionVo.getCollisionType();
        //幸运奖品 skuId
        Long luckySkuId = collisionVo.getLuckySkuId();

        //游戏宫格数据
        String gameJson = collisionVo.getGameJson();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, MaCollisionPrizeVo> map = null;
        try {
            map = objectMapper.readValue(gameJson, objectMapper.getTypeFactory().constructMapType(Map.class, String.class, MaCollisionPrizeVo.class));
            System.out.println(map);
        } catch (JsonProcessingException e) {
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_GAME_JSON_IS_ERROR);
        }

        //所有连线规则
        List<String> lxRules = bulidLXRule();

        //剩余包数
        Integer leftNum = collisionVo.getLeftNum();
        //奖励对碰次数
        Integer giftTimes = collisionVo.getGiftTimes();
        //奖励全家福次数
        Integer giftQjfTimes = collisionVo.getGiftQjfTimes();
        //奖励许愿次数
        Integer giftXYTimes = collisionVo.getGiftXYTimes();

        //触发规则数
        Integer total = collisionVo.getTotal();
        //累计获得积分
        BigDecimal score = collisionVo.getScoreTotal();
        //阶段通关次数(仅闯关,最后一关/普通时 为 null)
        Integer successNum = collisionVo.getSuccessNum();
        //本次获得包数
        Integer num1 = 0;
        //发包
        for (int i = 1; i < 10; i++) {
            String value = String.valueOf(i);
            MaCollisionPrizeVo prizeVo = map.get(value);
            //空格则发包
            if (Objects.isNull(prizeVo) && leftNum > 0) {
                leftNum = leftNum -1;
                map.put(value, prizeVos.get(new Random().nextInt(prizeVos.size())));
//                    map.put(value, prizeVos.get(i-1));
            }
        }

        String gameBeginJson = getGameJson(map);

        //本次拆包获得的奖品
        List<MaCollisionPrizeVo> memberPrize = new ArrayList<>();

        //处理规则逻辑
        List<MaCollisionTriggerRuleVo> triggerVo = new ArrayList<>();
        //是否触发规则标识
        Boolean ruleFlag = false;
        for (MaCollisionStageRuleVo ruleVo : ruleVos) {
            String type = ruleVo.getType();
            Integer giftNum = ruleVo.getGiftNum();
            BigDecimal giftScore = ruleVo.getGiftScore();
            switch (type) {
                case "S_CRT_XY":
                    //许愿
                    for (Map.Entry<String, MaCollisionPrizeVo> entry : map.entrySet()) {
                        String key = entry.getKey();
                        MaCollisionPrizeVo value = entry.getValue();
                        //当格子中奖品skuId 与许愿色skuId 想通时则触发规则  格子信息 null 剩余包数+1
                        if (Objects.nonNull(value) && value.getSkuId().equals(luckySkuId) && !value.getIsTriggerXY()) {
                            //触发许愿的不在触发
                            MaCollisionPrizeVo prizeVo = new MaCollisionPrizeVo();
                            BeanUtils.copyProperties(value, prizeVo);
                            prizeVo.setIsTriggerXY(true);
                            map.put(key, prizeVo);
                            total = total + 1;
                            leftNum = leftNum + giftNum;
                            score = giftScore.add(score);
                            num1 = num1 + giftNum;
                            giftXYTimes = giftXYTimes + 1;
                            ruleFlag = true;
                            //处理汇总规则
                            triggerVo.add(new MaCollisionTriggerRuleVo(type,key,0,giftNum,giftScore,successNum,total,leftNum,score,giftTimes));
                        }
                    }
                    break;
                case "S_CRT_QJF":
                    //全家福
                    if (checkDuplicateValues(map)) {
                        //处理会员获奖信息
                        memberPrize.addAll(map.values());

                        //处理计数
                        total = total + 1;
                        leftNum = leftNum + giftNum;
                        num1 = num1 + giftNum;
                        score = giftScore.add(score);
                        giftQjfTimes = giftQjfTimes + 1;
                        //全场为空
                        map = bulidGameBoard();
                        ruleFlag = true;
                        //处理汇总规则
                        triggerVo.add(new MaCollisionTriggerRuleVo(type,"",9,giftNum,giftScore,successNum,total,leftNum,score,giftTimes));
                    }
                    break;
                case "S_CRT_LX":
                    //连线
                    for (String lx : lxRules) {
                        String[] split = lx.split(",");
                        MaCollisionPrizeVo prizeVo1 = map.get(split[0]);
                        MaCollisionPrizeVo prizeVo2 = map.get(split[1]);
                        MaCollisionPrizeVo prizeVo3 = map.get(split[2]);
                        if (Objects.nonNull(prizeVo1) && Objects.nonNull(prizeVo2) && Objects.nonNull(prizeVo3)) {
                            if (prizeVo1.getSkuId().equals(prizeVo2.getSkuId()) && prizeVo2.getSkuId().equals(prizeVo3.getSkuId())) {
                                //处理会员获奖信息
                                memberPrize.add(map.get(split[0]));
                                memberPrize.add(map.get(split[1]));
                                memberPrize.add(map.get(split[2]));
                                //处理计数
                                total = total + 1;
                                leftNum = leftNum + giftNum;
                                score = giftScore.add(score);
                                num1 = num1 + giftNum;
                                //棋盘中位置奖品 更新null
                                map.put(split[0], null);
                                map.put(split[1], null);
                                map.put(split[2], null);
                                ruleFlag = true;
                                //处理汇总规则
                                triggerVo.add(new MaCollisionTriggerRuleVo(type,split[0] + "," + split[1] + "," + split[2],3,giftNum,giftScore,successNum,total,leftNum,score,giftTimes));
                            }
                        }
                    }
                    break;
                case "S_CRT_DP":
                    //对碰
                    for (int i = 0; i < 9; i++) {
                        MaCollisionPrizeVo outer = map.get(String.valueOf(i));
                        if(Objects.isNull(outer)){
                            continue;
                        }
                        for (int j = i + 1; j < 10; j++) {
                            MaCollisionPrizeVo inner = map.get(String.valueOf(j));
                            if(Objects.isNull(inner)){
                                continue;
                            }
                            if (outer.getSkuId().equals(inner.getSkuId())) {
                                //记录位置信息
                                String p1 = String.valueOf(i);
                                String p2 = String.valueOf(j);

                                //处理会员获奖信息
                                memberPrize.add(map.get(p1));
                                memberPrize.add(map.get(p2));

                                //棋盘中位置奖品 更新null
                                map.put(p1, null);
                                map.put(p2, null);

                                total = total + 1;
                                leftNum = leftNum + giftNum;
                                score = giftScore.add(score);
                                num1 = num1 + giftNum;
                                ruleFlag = true;
                                giftTimes = giftTimes + 1;
                                //处理汇总规则
                                triggerVo.add(new MaCollisionTriggerRuleVo(type,p1+","+p2,2,giftNum,giftScore,successNum,total,leftNum,score,giftTimes));
                                break;
                            }

                        }
                    }
                    break;
                case "S_CRT_QK":
                    //清空
                    if (checkAllNull(map)) {
                        total = total + 1;
                        leftNum = leftNum + giftNum;
                        score = giftScore.add(score);
                        num1 = num1 + giftNum;
                        ruleFlag = true;

                        //处理汇总规则
                        triggerVo.add(new MaCollisionTriggerRuleVo(type,"",2,giftNum,giftScore,successNum,total,leftNum,score,giftTimes));
                    }
                    break;
            }

        }


        MemberCollision memberCollision = new MemberCollision();
        memberCollision.setId(collisionVo.getId());
        memberCollision.setGameJson(getGameJson(map));
        memberCollision.setLeftNum(leftNum);
        memberCollision.setGiftTimes(giftTimes);
        memberCollision.setGiftQjfTimes(giftQjfTimes);
        memberCollision.setGiftXYTimes(giftXYTimes);
        memberCollision.setScoreTotal(score);
        memberCollision.setUpdated(DateFormatUtils.getNow());

        //处理通关
        Boolean isStage = Boolean.FALSE;
        //闯关模式  并且不是最后一关(通关次数不为空||通关次数!=0)  通关次数 <= 累计触发规则次数
        if(collisionType.equals(CollisionTypeEnum.S_CT_CG.value())
                && (Objects.nonNull(collisionVo.getSuccessNum())) && collisionVo.getSuccessNum() <= total) {
            isStage = Boolean.TRUE;
            memberCollision.setStage(collisionVo.getStage() + 1);
            memberCollision.setTotal(0);
            memberCollision.setIsAlreadyContinued(Boolean.FALSE);
        }else{
            //普通模式 或者闯关模式未闯关
            memberCollision.setTotal(total);
        }
        dao.update(memberCollision);

        //异步添加会员奖品数据
        saveMemberCollisionPrize(memberPrize,memberCache.getChainId(),collisionVo.getId(),collisionVo.getOrderId());


        //构建返回数据
        Map<String, Object> vo = new HashMap<>();
        //剩余包数
        vo.put("leftNum",leftNum);
        //拆包棋盘信息
        vo.put("gameBeginJson",gameBeginJson);
        //获得积分
        vo.put("score",score);
        //触发规则数
        vo.put("total",total);
        // 对碰奖励次数
        vo.put("giftTimes",giftTimes);
        // 全家福奖励次数
        vo.put("giftQjfTimes",giftQjfTimes);
        // 许愿奖励次数
        vo.put("giftXYTimes",giftXYTimes);
        //是否闯过本关标识
        vo.put("isStage",isStage);
        //结束标识(没触发任何规则,剩余包数 小于等于 0)
        vo.put("isFinish",!ruleFlag && leftNum <= 0);
        //触发规则
        vo.put("triggerList",triggerVo);
        return vo;

    }

    private static String getGameJson(Map<String, MaCollisionPrizeVo> map) {
        String json = "";
        ObjectMapper objectMapper = new ObjectMapper();
        try {
             json = objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            log.error("对对碰格式化对局json数据错误");
        }
        return json;
    }


    @Override
    @RateLimiter(isLoop = true)
    public Map buyCollision(MaCollisionPayReq request, MemberCache memberCache, SSOMaAppCache ssoMaApp, Long chainId) {
        MaCollisionDetailVo collisionDetailVo = this.queryCollisionDetail(request.getCollisionId());

        if(Objects.isNull(collisionDetailVo)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_MEMBER_IS_NOT_FIND);
        }

        Integer num = request.getNum();
        BigDecimal totalPrice = request.getTotalPrice();
        //判断支付金额是否正确
        BigDecimal priceFee = null;
        if(request.getIsCustom()){
            if(request.getNum().compareTo(collisionDetailVo.getMinNum()) < 0){
                throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_NOT_BUY_NUM_ERROR);
            }
            priceFee = collisionDetailVo.getPriceFee().multiply(new BigDecimal(request.getNum())) ;
        }else{
            String rechargeJson = collisionDetailVo.getRechargeJson();
            if(StringUtils.isBlank(rechargeJson)){
                throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_IS_NOT_FIND_RECHARGE);
            }
            JSONArray recharge = JSON.parseObject(rechargeJson, JSONArray.class);
            for (int i = 0; i < recharge.size(); i++) {
                JSONObject jsonObject = recharge.getJSONObject(i);
                Integer rechargeNum = jsonObject.getInteger("num");
                BigDecimal price = jsonObject.getBigDecimal("price");
                if(num.compareTo(rechargeNum) == 0 && totalPrice.compareTo(price) == 0){
                     priceFee = price;
                     break;
                }
            }
        }
        if(Objects.isNull(priceFee) || totalPrice.compareTo(priceFee) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_BUY_MONEY_ERROR);
        }


        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberCache.getId());
        order.setOpenId(memberCache.getOpenId());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(totalPrice);
        order.setPaymentMoney(totalPrice);

        order.setOrderTitle(collisionDetailVo.getName());
        order.setOrderType(OrderEnum.S_OOT_COLLISION.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberCache.getId());
        order.setPlatform(ssoMaApp.getPlatform());
        order.setOrderTradeType(OrderTradeTypeEnum.S_OTT_TRADE.value());
        Map<String,Object> map = new HashMap<>();
        map.put("collisionId",request.getCollisionId());
        map.put("collisionName",collisionDetailVo.getName());
        map.put("collisionType",collisionDetailVo.getType());
        map.put("collisionTypeName",collisionDetailVo.getTypeName());
        map.put("num",request.getNum());
        map.put("image",collisionDetailVo.getImage());
        order.setExtJson(JSONObject.toJSONString(map));
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())){
            order.setOrderBalance(request.getPriceBalance());
        }
        dao.save(order);
        HashMap<String, Object> vo = new HashMap<>();
        vo.put("orderId",order.getId());
        if (OrderUtil.isCashPay(request.getPaymentMethod())) {
            Map<String, Object> extMap = new HashMap<>();
            extMap.put("memberBankId", request.getMemberBankId());
            extMap.put("orderId", order.getId());
            //调取微信支付
            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), memberCache.getOpenId(), chainId, ssoMaApp.getAppId(),request.getPaymentMethod(),
                    callBackUrl, null, request.getPayMethod(), "default",memberCache.getMobile(),extMap);

            vo.put("payUrl",payUrl);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(order.getPaymentMoney());
            // 买家
            balanceRequest.setMemberId(memberCache.getId());
            balanceRequest.setPlId(order.getId());
            // 查询卖家
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40176"));
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_COLLISION.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterPay(order);
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }


        return vo;
    }



    @Override
    @RateLimiter(isLoop = true)
    public Map buyCollisionContinued(MaCollisionPayContinuedReq request, MemberCache memberCache, SSOMaAppCache ssoMaApp, Long chainId) {
        MaCollisionDetailVo collisionDetailVo = this.queryCollisionDetail(request.getCollisionId());

        if(Objects.isNull(collisionDetailVo)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_MEMBER_IS_NOT_FIND);
        }
        Integer num = request.getNum();
        BigDecimal totalPrice = request.getTotalPrice();

        MaMemberCollisionInfoVo memberCollisionVo = this.queryMemberCollisionInfo(request.getOrderId(), memberCache.getId());
        if(Objects.isNull(memberCollisionVo)){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_NOT_FOUND);
        }
        //取消掉续包限制
//        if(memberCollisionVo.getIsAlreadyContinued()){
//            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_STAGE_CONTINUED_IS_ERROR);
//        }
//        Integer leftNum = memberCollisionVo.getLeftNum();
//        if(leftNum != 0){
//            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_STAGE_CONTINUED_BUY_IS_ERROR);
//        }

        List<MaCollisionStageContinuedVo> stageContinuedVos = this.queryCollisionStageContinued(request.getCollisionId(), memberCollisionVo.getStage());

        //判断支付金额是否正确
        BigDecimal priceFee = null;
        if(request.getIsCustom()){
            if(request.getNum().compareTo(collisionDetailVo.getMinNum()) < 0){
                throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_NOT_BUY_NUM_ERROR);
            }
            priceFee = collisionDetailVo.getPriceFee().multiply(new BigDecimal(request.getNum())) ;
        }else{
            for (MaCollisionStageContinuedVo stageContinuedVo : stageContinuedVos) {
                Integer rechargeNum = stageContinuedVo.getNum();
                BigDecimal price = stageContinuedVo.getPriceFee();
                if(num.compareTo(rechargeNum) == 0 && totalPrice.compareTo(price) == 0){
                    priceFee =price;
                    break;
                }

            }
        }
        if(Objects.isNull(priceFee) || totalPrice.compareTo(priceFee) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.COLLISION_BUY_MONEY_ERROR);
        }

        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberCache.getId());
        order.setOpenId(memberCache.getOpenId());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(totalPrice);
        order.setPaymentMoney(totalPrice);

        order.setOrderTitle(collisionDetailVo.getName()+"(续包)");
        order.setOrderType(OrderEnum.S_OOT_COLLISION.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberCache.getId());
        order.setPlatform(ssoMaApp.getPlatform());
        order.setOrderTradeType(OrderTradeTypeEnum.S_OTT_TRADE.value());
        Map<String,Object> map = new HashMap<>();
        map.put("mainOrderId",request.getOrderId());
        map.put("mainOrderIdStr",String.valueOf(request.getOrderId()));
        map.put("mainOrderSn",memberCollisionVo.getOrderSn());
        map.put("collisionId",request.getCollisionId());
        map.put("collisionName",collisionDetailVo.getName()+"(续包)");
        map.put("collisionType",memberCollisionVo.getCollisionType());
        map.put("collisionTypeName",memberCollisionVo.getCollisionTypeName());
        map.put("num",request.getNum());
        map.put("image",collisionDetailVo.getImage());
        map.put("isFinish", BooleanUtils.isTrue(request.getIsFinish()));
        order.setExtJson(JSONObject.toJSONString(map));
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())){
            order.setOrderBalance(request.getPriceBalance());
        }
        dao.save(order);
        HashMap<String, Object> vo = new HashMap<>();
        vo.put("orderId",order.getId());
        if (OrderUtil.isCashPay(request.getPaymentMethod())) {
            Map<String, Object> extMap = new HashMap<>();
            extMap.put("memberBankId", request.getMemberBankId());
            extMap.put("orderId", order.getId());
            //调取微信支付
            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), memberCache.getOpenId(), chainId, ssoMaApp.getAppId(),request.getPaymentMethod(),
                    callConBackUrl, null, request.getPayMethod(), "default",memberCache.getMobile(),extMap);

            vo.put("payUrl",payUrl);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(order.getPaymentMoney());
            // 买家
            balanceRequest.setMemberId(memberCache.getId());
            balanceRequest.setPlId(order.getId());
            // 查询卖家
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40176"));
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_COLLISION.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterContinuedPay(order);
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }


        return vo;
    }
    private void afterContinuedPay(Order order) {
        taskExecutor.execute(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderCode", order.getOrderSn());
            collisionConCallbackProcessor.onPayTradeSuccess(order, jsonObject);
        });
    }

    private void afterPay(Order order) {
        taskExecutor.execute(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderCode", order.getOrderSn());
            collisionCallbackProcessor.onPayTradeSuccess(order, jsonObject);
        });
    }


    private void saveMemberCollisionPrize(List<MaCollisionPrizeVo> memberPrize,Long chainId,Long collisionId,Long orderId) {
        String traceId = MDCTraceUtils.getTraceId();
        taskExecutor.execute(() -> {
            XcrmThreadContext.setChainId(chainId);
            XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
            MDCTraceUtils.putTraceId(traceId);
            Date now = DateFormatUtils.getNow();
            for (MaCollisionPrizeVo maCollisionPrizeVo : memberPrize) {
                try{
                    MemberCollisionPrize memberCollisionPrize = new MemberCollisionPrize();
                    memberCollisionPrize.setChainId(chainId);
                    memberCollisionPrize.setMemberCollisionId(collisionId);
                    memberCollisionPrize.setOrderId(orderId);
                    memberCollisionPrize.setSpuId(maCollisionPrizeVo.getSpuId());
                    memberCollisionPrize.setSkuId(maCollisionPrizeVo.getSkuId());
                    memberCollisionPrize.setProductName(maCollisionPrizeVo.getName());
                    memberCollisionPrize.setProductMainImage(maCollisionPrizeVo.getMainImage());
                    memberCollisionPrize.setPrimeCostFee(maCollisionPrizeVo.getPrimeCostFee());
                    memberCollisionPrize.setCreated(now);
                    memberCollisionPrize.setDataStatus(Boolean.TRUE);
                    dao.save(memberCollisionPrize);
                }catch (Exception e){
                    log.error("保存会员碰撞奖品失败",e);
                }
            }
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            MDCTraceUtils.removeTraceId();
        });
    }


    private static  List<String> bulidLXRule() {
        List<String> LXRule = new ArrayList<>();
        LXRule.add("1,2,3");
        LXRule.add("1,4,7");
        LXRule.add("1,5,9");
        LXRule.add("2,5,8");
        LXRule.add("3,5,7");
        LXRule.add("3,6,9");
        LXRule.add("4,5,6");
        LXRule.add("7,8,9");
        return LXRule;
    }

    public static boolean checkDuplicateValues(Map<String, MaCollisionPrizeVo> map) {
        // 校验全场不为空
        if (map.values().stream().anyMatch(Objects::isNull)) {
            return false;
        }
        // 使用Set来检查重复的skuId
        Set<Long> skuIds = new HashSet<>();
        for (MaCollisionPrizeVo prizeVo : map.values()) {
            if (!skuIds.add(prizeVo.getSkuId())) {
                return false; // 如果添加失败，说明有重复的skuId
            }
        }
        return true; // 没有重复的skuId
    }

    //检验是否全是空
    public static boolean checkAllNull(Map<String, MaCollisionPrizeVo> map) {
        return map.values().stream().allMatch(Objects::isNull);
    }

    private static Map<String, MaCollisionPrizeVo> bulidGameBoard() {
        Map<String, MaCollisionPrizeVo> map = new HashMap<>();
        map.put("1",null);
        map.put("2",null);
        map.put("3",null);
        map.put("4",null);
        map.put("5",null);
        map.put("6",null);
        map.put("7",null);
        map.put("8",null);
        map.put("9",null);
        return map;
    }
}
