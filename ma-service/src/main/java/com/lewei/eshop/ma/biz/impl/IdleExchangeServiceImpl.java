package com.lewei.eshop.ma.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.request.market.IdleExchangeMakeADealRequest;
import com.lewei.eshop.common.request.market.IdleExchangePayRequest;
import com.lewei.eshop.common.request.market.IdleExchangeReleaseRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.order.DeliverChangeRequest;
import com.lewei.eshop.common.vo.market.IdleExchangeAuctionVO;
import com.lewei.eshop.common.vo.market.IdleExchangeDetailVO;
import com.lewei.eshop.entity.market.ChangeCollect;
import com.lewei.eshop.entity.market.IdleExchange;
import com.lewei.eshop.entity.market.IdleExchangeAuction;
import com.lewei.eshop.entity.market.IdleExchangeBill;
import com.lewei.eshop.entity.market.types.BlackMarketStatusEnum;
import com.lewei.eshop.entity.market.types.ChangeOrderStatusEnum;
import com.lewei.eshop.entity.market.types.IdleExchangeTypeEnum;
import com.lewei.eshop.entity.market.types.MarketAuctionStatusEnum;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.types.BillTypeEnum;
import com.lewei.eshop.entity.member.types.MemberAuthorityEnum;
import com.lewei.eshop.entity.member.types.MemberBillTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IChainConfigService;
import com.lewei.eshop.ma.biz.IIdleExchangeService;
import com.lewei.eshop.ma.biz.IMaMemberBillService;
import com.lewei.eshop.ma.biz.IMaMemberService;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.pay.IdleExchangeCallbackProcessor;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 闲置换换表(IdleExchange)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-14 09:28:16
 */
@Service
@Transactional
@Slf4j
public class IdleExchangeServiceImpl implements IIdleExchangeService {

    @Autowired
    private BaseDaoSupport dao;

    @Autowired
    private IOrderPayService orderPayService;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IdleExchangeCallbackProcessor idleExchangeCallbackProcessor;

    @Autowired
    private IMaMemberService memberService;

    @Autowired
    private IMaMemberBillService memberBillService;

    @Autowired
    private IChainConfigService chainConfigService;

    /**
     * 微信支付回调
     */
    private final String FIX_CALL_BACK_URL = "/api/ma/idle/exchange/callback";

    /**
     * 发布
     *
     * @param request  请求实体
     * @param memberId 会员id
     * @return Long
     */
    @Override
    public Long release(IdleExchangeReleaseRequest request, Long memberId) {
        // 不可发布交易验证
        boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_RELEASE_DEAL, memberId);
        if (isAuthority) {
            throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_RELEASE_DEAL);
        }
        // 竞拍校验
        if (IdleExchangeTypeEnum.S_IET_A.value().equals(request.getType())) {
            if (null == request.getFixedPrice()) {
                throw new BizCoreRuntimeException(MaErrorConstants.AUCTION_MINIMUM_BID_CANNOT_BE_EMPTY);
            }
            if (null == request.getExpiredTime()) {
                throw new BizCoreRuntimeException(MaErrorConstants.AUCTION_END_TIME_CANNOT_BE_EMPTY);
            }
        }
        if (null != request.getExpiredTime() && request.getExpiredTime().getTime() - DateFormatUtils.getNow().getTime() <= 60 * 60 * 1000) {
            throw new BizCoreRuntimeException(MaErrorConstants.THE_SELECTED_END_TIME_MUST_BE_ONE_HOUR_AFTER_THE_CURRENT_TIME);
        }
        IdleExchange entity = new IdleExchange();
        BeanUtils.copyProperties(request, entity);
        String content = request.getContent();
        entity.setTitle(StringUtils.substring(content, 0, Math.min(content.length(), 20)));
        String flowSn = CodecUtil.createOrderId();
        entity.setFlowSn(flowSn);
        entity.setMemberId(memberId);
        entity.setStatus(BlackMarketStatusEnum.S_BMS_ON_SHELF.value());
        entity.setCreateBy(memberId);
        entity.setCreated(DateFormatUtils.getNow());
        dao.save(entity);
        return entity.getId();
    }

    /**
     * 查询闲置列表
     *
     * @param status       状态
     * @param type         type 查询类型
     *                     idle:惊喜闲置
     *                     myPosted:我的发布
     *                     myTransaction:我的交易
     *                     myCollect:我的收藏
     * @param queryKey     关键字查询
     * @param sellMemberId 卖家id
     * @param buyMemberId  买家id
     * @param pageNo       页码
     * @param pageSize     条数
     * @param memberId     会员id
     * @return 分页数据
     */
    @Override
    public Pagination pageList(String status, String type, String queryKey, Long sellMemberId, Long buyMemberId, Boolean isCollect, Integer pageNo,
                               Integer pageSize, Long memberId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.pageList")
                .setParam("status", status)
                .setParam("type", type)
                .setParam("queryKey", queryKey)
                .setParam("sellMemberId", sellMemberId)
                .setParam("buyMemberId", buyMemberId)
                .setParam("isCollect", isCollect)
                .setParam("memberId", memberId)
                .setParam("pageNo", pageNo)
                .setParam("pageSize", pageSize);
        ssqb.setIncludeTotalCount(true);
        return dao.findForPage(ssqb);
    }

    /**
     * 下单
     *
     * @param request  请求实体
     * @param memberId 用户id
     * @param appId    appId
     * @return String 微信支付url(如果余额支付不返回)
     */
    @Override
    public String pay(IdleExchangePayRequest request, Long memberId, String appId) {
        // 商品状态校验
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.selectOneById")
                .setParam("id", request.getIdleExchangeId());
        IdleExchange idleExchange = dao.findForObj(ssqb, IdleExchange.class);
        if (null == idleExchange) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_NO_SUCH_SKU);
        }
        if (BlackMarketStatusEnum.S_BMS_OFF_SHELF.value().equals(idleExchange.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_BE_TAKEN_OFF);
        } else if (BlackMarketStatusEnum.S_BMS_SOLD.value().equals(idleExchange.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_OUT_OF_STOCK);
        }
        // 本人无法对自己发布的下单
        if (memberId.equals(idleExchange.getMemberId())) {
            throw new BizCoreRuntimeException(MaErrorConstants.UNABLE_TO_PURCHASE_SELF_PUBLISHED_ITEMS);
        }

        if (IdleExchangeTypeEnum.S_IET_FP.value().equals(request.getType()) && request.getPrice().compareTo(idleExchange.getFixedPrice()) != 0) {
            // 一口价
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_PRICE_CHANGED);
        } else if (IdleExchangeTypeEnum.S_IET_A.value().equals(request.getType()) && request.getPrice().compareTo(idleExchange.getFixedPrice()) < 0) {
            // 竞拍
            throw new BizCoreRuntimeException(MaErrorConstants.CAN_NOT_LOWER_THAN_AUCTION_PRICE);
        }

        ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.selectListByIdleExchangeIdForPay")
                .setParam("idleExchangeId", request.getIdleExchangeId());
        ssqb.setParam("memberId", memberId);
        IdleExchangeAuctionVO vo = dao.findForObj(ssqb, IdleExchangeAuctionVO.class);
        if (null != vo) {
            throw new BizCoreRuntimeException(MaErrorConstants.YOU_HAVE_ALREADY_BID_FOR_THIS_ITEM_DO_NOT_REPEAT_PARTICIPATION);
        }
        String title = idleExchange.getTitle();

        // 从表
        IdleExchangeAuction idleExchangeAuction = new IdleExchangeAuction();
        idleExchangeAuction.setIdleExchangeId(request.getIdleExchangeId());
        idleExchangeAuction.setMemberId(memberId);
        // 支付状态 回调后改变
        idleExchangeAuction.setPayStatus(false);
        idleExchangeAuction.setPaymentMethod(request.getPaymentMethod());
        // 支付金额
        BigDecimal paymentMoney = request.getPrice().add(request.getFreight());
        idleExchangeAuction.setAuctionPrice(request.getPrice());
        idleExchangeAuction.setAddress(request.getAddress());
        idleExchangeAuction.setStatus(MarketAuctionStatusEnum.S_MAS_JPZ.value());
        idleExchangeAuction.setCreated(DateFormatUtils.getNow());
        idleExchangeAuction.setPayPrice(paymentMoney);

        // 主从表 币换算
        BigDecimal moneyRatio = chainConfigService.queryChainConfig().getMoneyRatio();
        BigDecimal coin = paymentMoney.multiply(moneyRatio);

        idleExchangeAuction.setCoin(coin);
        idleExchangeAuction.setCurrency(request.getPrice().multiply(moneyRatio));
        dao.save(idleExchangeAuction);


        // 支付 (扣款失败回调退款)
        // 获取下单人信息
        SaasQueryBuilder queryMember = SaasQueryBuilder.where(Restrictions.eq("id", memberId));
        Member member = dao.query(queryMember, Member.class);

        if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(request.getPaymentMethod())) {
            // 微信支付
            return orderPayService.callCashPayApi(title, paymentMoney,
                    idleExchangeAuction.getId().toString(),
                    member.getBmOpenId(), XcrmThreadContext.getChainId(), appId, request.getPaymentMethod(), FIX_CALL_BACK_URL);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(paymentMoney);
            balanceRequest.setMemberId(member.getId());
            balanceRequest.setPlId(idleExchangeAuction.getId());
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40129") + idleExchange.getTitle());
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterPay(idleExchangeAuction.getId());
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }
        return null;
    }

    /**
     * 余额支付后
     *
     * @param idleExchangeAuctionId 从表id
     */
    private void afterPay(Long idleExchangeAuctionId) {

        // 重新查询
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.selectOneById")
                .setParam("id", idleExchangeAuctionId);
        IdleExchangeAuction idleExchangeAuction = dao.findForObj(ssqb, IdleExchangeAuction.class);

        ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.selectOneById")
                .setParam("id", idleExchangeAuction.getIdleExchangeId());
        IdleExchange idleExchange = dao.findForObj(ssqb, IdleExchange.class);
        taskExecutor.execute(() -> idleExchangeCallbackProcessor.onPayTradeSuccess(idleExchange, idleExchangeAuction, null));
    }

    @Override
    public Map<String, Boolean> clickChangeCollect(Long changeId, Long memberId, Long tenantId) {
        boolean flag = true;
        SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("memberId", memberId))
                .and(Restrictions.eq("changeId", changeId));
        ChangeCollect changeCollect = dao.query(queryBuilder, ChangeCollect.class);
        if (changeCollect != null) {
            flag = !changeCollect.getDataStatus();
        }
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.clickChangeCollect")
                .setParam("tenantId", tenantId)
                .setParam("changeId", changeId)
                .setParam("memberId", memberId)
                .setParam("dataStatus", flag);
        dao.updateByMybatis(ssqb);
        Map<String, Boolean> map = new HashMap<>(1);
        map.put("flag", flag);
        return map;
    }

    @Override
    public void offShelfChange(Long memberId, Long changeId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                .and(Restrictions.eq("memberId", memberId))
                .and(Restrictions.eq("id", changeId));
        IdleExchange exchange = dao.query(query, IdleExchange.class);
        if (exchange == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40054"));
        }
        if (!Objects.equals(exchange.getStatus(), BlackMarketStatusEnum.S_BMS_ON_SHELF.value())) {
            throw new BizCoreRuntimeException(MaErrorConstants.CHANGE_STATUS_ERROR);
        }
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.updateById")
                .setParam("id", exchange.getId())
                .setParam("status", BlackMarketStatusEnum.S_BMS_OFF_SHELF.value());
        int i = dao.updateByMybatis(ssqb);
        if (i != 1) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
        }
        // 拍卖退款
        refundToBalance(exchange);
    }

    /**
     * 查询闲置详情
     *
     * @param id       闲置主表id
     * @param memberId memberId
     * @return Response
     */
    @Override
    public IdleExchangeDetailVO detail(String id, Long memberId) {
        // 增加浏览量
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.addPageViews")
                .setParam("id", id);
        dao.updateByMybatis(ssqb);

        ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.detail")
                .setParam("id", id)
                .setParam("memberId", memberId);
        IdleExchangeDetailVO result = dao.findForObj(ssqb, IdleExchangeDetailVO.class);
        if (null == result) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_NO_SUCH_SKU);
        }
        ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.selectListByIdleExchangeId")
                .setParam("idleExchangeId", id);
        if (!result.getMemberId().equals(memberId)) {
            ssqb.setParam("memberId", memberId);
        }
        List<IdleExchangeAuctionVO> idleExchangeAuctionVOList = dao.findForList(ssqb, IdleExchangeAuctionVO.class);
        result.setIdleExchangeAuctionVOList(idleExchangeAuctionVOList);
        return result;
    }

    /**
     * 成交
     *
     * @param request  入参
     * @param memberId memberId
     */
    @Override
    public void makeADeal(IdleExchangeMakeADealRequest request, Long memberId) {
        this.makeADeal(request, false, memberId);
    }

    /**
     * 竞拍商品出售成功 后处理
     *
     * @param request  入参
     * @param flag     true 定时任务调用  false 接口调用
     * @param memberId memberId
     */
    private void makeADeal(IdleExchangeMakeADealRequest request, Boolean flag, Long memberId) {
        // 校验商品状态
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.idle.exchange.selectOneById")
                .setParam("id", request.getIdleExchangeId());
        IdleExchange idleExchange = dao.findForObj(query, IdleExchange.class);
        if (!BlackMarketStatusEnum.S_BMS_ON_SHELF.value().equals(idleExchange.getStatus())) {
            // 已售出的商品不能进行成交操作
            throw new BizCoreRuntimeException(MaErrorConstants.THE_SOLD_PRODUCT_CANNOT_BE_TRADED);
        }
        query = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.selectListByIdleExchangeId")
                .setParam("idleExchangeId", request.getIdleExchangeId());
        List<IdleExchangeAuctionVO> idleExchangeAuctionVOList = dao.findForList(query, IdleExchangeAuctionVO.class);
        if (flag) {
            // 定时任务情况
            if (CollectionUtil.isNotEmpty(idleExchangeAuctionVOList)) {
                // 有人出价
                idleExchangeAuctionVOList.sort(Comparator.comparing(IdleExchangeAuctionVO::getAuctionPrice).reversed()
                        .thenComparing(IdleExchangeAuctionVO::getCreated));
                request.setIdleExchangeAuctionId(idleExchangeAuctionVOList.get(0).getId());
            } else {
                // 无人出价
                query = Ssqb.create("com.lewei.eshop.ma.idle.exchange.updateById")
                        .setParam("id", idleExchange.getId())
                        .setParam("status", BlackMarketStatusEnum.S_BMS_OFF_SHELF.value());
                int i = dao.updateByMybatis(query);
                if (i != 1) {
                    throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
                }
                return;
            }
        } else {
            // 调接口情况
            // 是否存在
            boolean isExists = idleExchangeAuctionVOList.stream().anyMatch(e -> request.getIdleExchangeAuctionId().equals(e.getId()));
            if (!isExists) {
                // 不存在 数据异常
                throw new BizCoreRuntimeException(MaErrorConstants.THE_BID_RECORD_DOES_NOT_EXIST);
            }
        }
        // 退款集合
        List<IdleExchangeAuction> refundList = idleExchangeAuctionVOList.stream()
                .filter(e -> !e.getId().equals(request.getIdleExchangeAuctionId()))
                .map(e -> {
                    IdleExchangeAuction idleExchangeAuction = new IdleExchangeAuction();
                    idleExchangeAuction.setId(e.getId());
                    idleExchangeAuction.setAuctionPrice(e.getAuctionPrice().add(idleExchange.getFreight()));
                    idleExchangeAuction.setStatus(MarketAuctionStatusEnum.S_MAS_YTK.value());
                    idleExchangeAuction.setUpdated(DateFormatUtils.getNow());
                    // 退款

                    MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                    balanceRequest.setGift(BigDecimal.ZERO);
                    balanceRequest.setBalance(e.getAuctionPrice().add(idleExchange.getFreight()));
                    balanceRequest.setMemberId(e.getMemberId());
                    balanceRequest.setPlId(e.getId());
                    balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40032") + idleExchange.getTitle());
                    balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                    memberFeignClient.memberBalanceHandle(balanceRequest);
                    // 币记录
                    IdleExchangeBill idleExchangeBill = new IdleExchangeBill();
                    idleExchangeBill.setFlowSn(idleExchange.getFlowSn());
                    idleExchangeBill.setMemberId(e.getMemberId());
                    idleExchangeBill.setTitle(idleExchange.getTitle());
                    idleExchangeBill.setMoney(e.getAuctionPrice().add(idleExchange.getFreight()));
                    idleExchangeBill.setCoin(e.getCoin());
                    idleExchangeBill.setType(MemberBillTypeEnum.refund.value());
                    idleExchangeBill.setPaymentMethod(e.getPaymentMethod());
                    idleExchangeBill.setCreated(DateFormatUtils.getNow());
                    dao.save(idleExchangeBill);
                    //保存账单
                    memberBillService.saveMemberBill(e.getMemberId(),
                            e.getAuctionPrice().add(idleExchange.getFreight()), BillTypeEnum.general, MemberBillTypeEnum.refund,
                            e.getPaymentMethod(), idleExchange.getFlowSn(), null);
                    return idleExchangeAuction;
                }).collect(Collectors.toList());

        IdleExchangeAuctionVO auctionVO = idleExchangeAuctionVOList.stream()
                .filter(e -> e.getId().equals(request.getIdleExchangeAuctionId())).findAny().get();
        // 更新状态
        query = Ssqb.create("com.lewei.eshop.ma.idle.exchange.updateById")
                .setParam("id", idleExchange.getId())
                .setParam("status", BlackMarketStatusEnum.S_BMS_SOLD.value())
                .setParam("auctionPrice", auctionVO.getAuctionPrice())
                .setParam("coin", auctionVO.getCoin())
                .setParam("updateBy", memberId);
        int i = dao.updateByMybatis(query);
        if (i != 1) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
        }

        // 竞拍失败者
        if (CollectionUtil.isNotEmpty(refundList)) {
            query = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.updateById")
                    .setParam("idList", refundList.stream().map(IdleExchangeAuction::getId).collect(Collectors.toList()))
                    .setParam("status", MarketAuctionStatusEnum.S_MAS_YTK.value());
            i = dao.updateByMybatis(query);
            if (i != refundList.size()) {
                throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
            }
        }

        // 竞拍成功者
        query = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.updateById")
                .setParam("id", request.getIdleExchangeAuctionId())
                .setParam("status", MarketAuctionStatusEnum.S_MAS_JPCG.value())
                .setParam("orderStatus", ChangeOrderStatusEnum.S_COS_DCL.value());
        i = dao.updateByMybatis(query);
        if (i != 1) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
        }
    }

    /**
     * 刷新商品状态
     */
    @Override
    public void refreshOrderStatus() {
        QueryBuilder ssqb = QueryBuilder.create("com.lewei.eshop.ma.idle.exchange.selectListByStatusAndType");
        List<IdleExchange> idleExchangeList = dao.findForList(ssqb, IdleExchange.class);
        idleExchangeList.stream()
                // 过滤出过期的竞拍商品
                .filter(e -> e.getExpiredTime().compareTo(DateFormatUtils.getNow()) < 0).collect(Collectors.toList())
                .stream().collect(Collectors.groupingBy(IdleExchange::getChainId)).forEach((k, v) -> {
                    try {
                        XcrmThreadContext.setChainId(k);
                        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                        v.forEach(e -> {
                            IdleExchangeMakeADealRequest request = new IdleExchangeMakeADealRequest();
                            request.setIdleExchangeId(e.getId());
                            this.makeADeal(request, true, null);
                        });
                    } finally {
                        XcrmThreadContext.removeAccessType();
                        XcrmThreadContext.removeChainId();
                    }
                });
    }

    @Override
    public void deliverChange(Long changeId, DeliverChangeRequest request) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", changeId))
                .and(Restrictions.eq("dataStatus", 1));
        IdleExchange exchange = dao.query(query, IdleExchange.class);
        if (exchange == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_NON_EXISTENT);
        }
        SaasQueryBuilder query1 = SaasQueryBuilder.where(Restrictions.eq("idleExchangeId", exchange.getId()))
                .and(Restrictions.eq("orderStatus", ChangeOrderStatusEnum.S_COS_DCL.value()))
                .and(Restrictions.eq("dataStatus", 1));
        IdleExchangeAuction exchangeAuction = dao.query(query1, IdleExchangeAuction.class);
        if (Objects.nonNull(exchangeAuction)) {
            exchange.setExtJson(JSON.toJSONString(request.getExtJson()));
            exchange.setAutoReceiptTime(DateUtils.addDays(DateFormatUtils.getNow(), 20));
            exchange.setUpdated(DateFormatUtils.getNow());
            dao.update(exchange);

            exchangeAuction.setOrderStatus(ChangeOrderStatusEnum.S_COS_DCJ.value());
            exchangeAuction.setUpdated(DateFormatUtils.getNow());
            dao.update(exchangeAuction);
        }
    }

    @Override
    public void receiptChange(Long changeId, Long chainId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", changeId))
                .and(Restrictions.eq("dataStatus", 1));
        IdleExchange exchange = dao.query(query, IdleExchange.class);
        if (exchange == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_NON_EXISTENT);
        }
        SaasQueryBuilder query1 = SaasQueryBuilder.where(Restrictions.eq("idleExchangeId", exchange.getId()))
                .and(Restrictions.eq("orderStatus", ChangeOrderStatusEnum.S_COS_DCJ.value()))
                .and(Restrictions.eq("dataStatus", 1));
        IdleExchangeAuction exchangeAuction = dao.query(query1, IdleExchangeAuction.class);
        if (Objects.nonNull(exchangeAuction)) {
            // 余额充值到发布者钱包
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(exchange.getAuctionPrice().add(exchange.getFreight()));
            balanceRequest.setMemberId(exchange.getMemberId());
            balanceRequest.setPlId(exchange.getId());
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40031") + exchange.getTitle());
            balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 币记录
            IdleExchangeBill idleExchangeBill = new IdleExchangeBill();
            idleExchangeBill.setFlowSn(exchange.getFlowSn());
            idleExchangeBill.setMemberId(exchange.getMemberId());
            idleExchangeBill.setTitle(exchange.getTitle());
            idleExchangeBill.setMoney(exchange.getAuctionPrice().add(exchange.getFreight()));
            idleExchangeBill.setCoin(exchange.getCoin());
            idleExchangeBill.setType(MemberBillTypeEnum.income.value());
            idleExchangeBill.setPaymentMethod(exchangeAuction.getPaymentMethod());
            idleExchangeBill.setCreated(DateFormatUtils.getNow());
            dao.save(idleExchangeBill);
            // 保存会员账单
            memberBillService.saveMemberBill(exchange.getMemberId(), exchange.getAuctionPrice().add(exchange.getFreight()), BillTypeEnum.general, MemberBillTypeEnum.income, null, exchange.getFlowSn(), null);

            exchange.setStatus(BlackMarketStatusEnum.S_BMS_SOLD.value());
            exchange.setUpdated(DateFormatUtils.getNow());
            dao.update(exchange);

            exchangeAuction.setOrderStatus(ChangeOrderStatusEnum.S_COS_JYCG.value());
            exchangeAuction.setUpdated(DateFormatUtils.getNow());
            dao.update(exchangeAuction);
        }
    }

    /**
     * 是否首次发布商品
     *
     * @param memberId memberId
     * @return boolean
     */
    @Override
    public Boolean isFirst(Long memberId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.isFirst")
                .setParam("memberId", memberId);
        return dao.findForObj(ssqb, Integer.class) == 0;
    }

    private void refundToBalance(IdleExchange exchange) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                .and(Restrictions.eq("payStatus", 1))
                .and(Restrictions.eq("status", MarketAuctionStatusEnum.S_MAS_JPZ.value()))
                .and(Restrictions.eq("idleExchangeId", exchange.getId()));
        List<IdleExchangeAuction> exchangeAuctions = dao.queryList(query, IdleExchangeAuction.class);

        if (ListUtil.isNotEmpty(exchangeAuctions)) {
            for (IdleExchangeAuction auction : exchangeAuctions) {
                MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                balanceRequest.setGift(BigDecimal.ZERO);
                balanceRequest.setBalance(auction.getPayPrice());
                balanceRequest.setMemberId(auction.getMemberId());
                balanceRequest.setPlId(auction.getId());
                balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40032") + exchange.getTitle());
                balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                memberFeignClient.memberBalanceHandle(balanceRequest);

                Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.idle.exchange.auction.updateById")
                        .setParam("id", auction.getId())
                        .setParam("status", MarketAuctionStatusEnum.S_MAS_YTK.value());
                int i = dao.updateByMybatis(ssqb);
                if (i != 1) {
                    throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
                }

                // 币记录
                IdleExchangeBill idleExchangeBill = new IdleExchangeBill();
                idleExchangeBill.setFlowSn(exchange.getFlowSn());
                idleExchangeBill.setMemberId(auction.getMemberId());
                idleExchangeBill.setTitle(exchange.getTitle());
                idleExchangeBill.setMoney(auction.getAuctionPrice());
                idleExchangeBill.setCoin(auction.getCoin());
                idleExchangeBill.setType(MemberBillTypeEnum.refund.value());
                idleExchangeBill.setPaymentMethod(auction.getPaymentMethod());
                idleExchangeBill.setCreated(DateFormatUtils.getNow());
                dao.save(idleExchangeBill);
                //保存账单
                memberBillService.saveMemberBill(auction.getMemberId(), auction.getAuctionPrice(), BillTypeEnum.general, MemberBillTypeEnum.refund,
                        auction.getPaymentMethod(), exchange.getFlowSn(), null);
            }
        }
    }
}