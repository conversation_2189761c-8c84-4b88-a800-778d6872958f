package com.lewei.eshop.ma.message.request;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 分销关系临时表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
public class MaDistRefRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "openId is required")
    private String openId;
    /**
     * 会员id
     */
    @NotEmpty(message = "inviteCode is required")
    private String inviteCode;





}
