package com.lewei.eshop.ma;

/**
 * 错误定义
 *
 * <AUTHOR>
 * @date 2019/12/18
 **/

public class MaErrorConstants {

    /**
     * 微信错误：{0}
     */
    public static final String WX_COMMON_ERROR = "wx10000";
    /**
     * {0}
     */
    public static final String MA_COMMON_ERROR = "wx19999";

    /**
     * 访问微信服务错误
     */
    public static final String WX_ACCESS_ERROR = "wx10001";

    /**
     * 这个商品不存在
     */
    public static final String WX_NO_SUCH_SKU = "wx10002";

    /**
     * 该商品已售罄
     */
    public static final String WX_OUT_OF_STOCK = "wx10003";

    /**
     * 购买商品数量不能大于库存
     */
    public static final String WX_GREATER_QUANTITY = "wx10004";

    /**
     * 购买的商品已被下架
     */
    public static final String WX_BE_TAKEN_OFF = "wx10005";

    /**
     * 购买商品信息已过期
     */
    public static final String WX_INFO_OUT_OF_DATE = "wx10006";

    /**
     * 订单不存在
     */
    public static final String WX_ORDER_NON_EXISTENT = "wx10007";

    /**
     * 非待支付状态的订单不可发起支付
     */
    public static final String WX_ORDER_STATUS_NOT_UNPAID = "wx10008";

    /**
     * 后台接口错误：{0}
     */
    public static final String WX_BACK_END_ERROR = "wx10009";
    /**
     * 服务和商品不能同时下单
     */
    public static final String WX_ORDER_PRODUCT_NTYPE_IS_SAME = "wx10010";
    /**
     * 购物车已失效
     */
    public static final String WX_ORDER_CART_IS_INVALID = "wx10011";
    /**
     * 订单状态异常
     */
    public static final String WX_ORDER_STATUS_ERROR = "wx10012";
    /**
     * 产品详细接口参数错误
     */
    public static final String PRODUCT_DETAIL_PARAMETER_ERROR = "wx10013";
    /**
     * 提取时间不合法
     */
    public static final String ORDER_PICK_UP_TIME_ERROR = "wx10014";
    /**
     * 存在已失效的运费模板，请重新下单
     */
    public static final String FREIGHT_TEMPLATE_IS_NOT_EXIST = "wx10015";
    /**
     * {0}产品不支持当前地区邮寄
     */
    public static final String PRODUCT_IS_NOT_SUPPORT_SHIPPING = "wx10016";
    /**
     * {0}不支持当前提取方式
     */
    public static final String PRODUCT_IS_NOT_SUPPORT_LOGISTICSMODE = "wx10017";

    /**
     * {0}库存不足
     */
    public static final String PRODUCT_QUANTITY_IS_NOT_ENOUGH = "wx10018";
    /**
     * 不支持的地区：{0}
     */
    public static final String AREA_IS_NOT_SUPPORT = "wx10019";
    /**
     * 提取方式不能为空
     */
    public static final String LOGISTICSMODE_IS_EMPTY = "wx10020";
    /**
     * 商品/服务：{0}已下架或删除
     */
    public static final String PRODUCT_HAS_BEEN_DELETED_OR_OFF_SHELF = "wx10021";
    /**
     * 优惠券不存在
     */
    public static final String COUPON_NOT_FOUND = "wx10022";
    /**
     * 优惠券不符合适用条件
     */
    public static final String COUPON_IS_NOT_AVAILABLE = "wx10023";
    /**
     * 只能删除本人评论
     */
    public static final String CAN_ONLY_DELETE_OWN_COMMENTS = "wx10024";

    /**
     * 已成为代理，请勿重复提交
     */
    public static final String MEMBER_PROXY_STATUS_PASSED = "wx10025";
    /**
     * 正在审核中，请勿重复提交
     */
    public static final String MEMBER_PROXY_STATUS_REVIEWING = "wx10026";
    /**
     * 积分商城未配置
     */
    public static final String SCORE_PRODUCT_CONFIG_NOT_FOUNT = "wx10027";
    /**
     * 积分商品已过期
     */
    public static final String SCORE_PRODUCT_EXPIRED = "wx10028";

    /**
     * 积分商品兑换次数不足，限购{0}个
     */
    public static final String SCORE_PRODUCT_LIMIT_NUM_NOT_ENOUGH = "wx10029";
    /**
     * 积分商品库存不足
     */
    public static final String SCORE_PRODUCT_QUANTITY_NOT_ENOUGH = "wx10030";
    /**
     * 会员怒气不足
     */
    public static final String MEMBER_SCORE_NOT_ENOUGH = "wx10031";
    /**
     * 已选卡内服务不存在，请刷新重试
     */
    public static final String CARD_ITEM_ERROR = "wx10032";
    /**
     * 请选择{0}个服务
     */
    public static final String CARD_ITEM_SELECT_ERROR = "wx10033";
    /**
     * 鼎美卡不支持购物车支付
     */
    public static final String DM_CARD_CAN_ONLY_BUY_ONE = "wx10034";

    /**
     * 活动报名人数已满
     */
    public static final String ACTIVITY_LEFT_TIMES_NOT_ENOUGH = "wx10035";
    /**
     * 活动未开始或已结束
     */
    public static final String ACTIVITY_STATUS_ERROR = "wx10036";
    /**
     * 签到活动未开启
     */
    public static final String SIGN_ACTIVITY_IS_NOT_OPEN = "wx10037";
    /**
     * 今日已签到，请勿重复签到~
     */
    public static final String TODAY_HAS_SINGED = "wx10038";
    /**
     * 积分配置未开启
     */
    public static final String SCORE_DEDUCTION_CONFIG_NOT_OPEN = "wx10039";
    /**
     * 请输入{0}的整数倍积分
     */
    public static final String PAY_SCORE_ERROR = "wx10040";
    /**
     * 积分最多可抵扣应付金额的{0}%
     */
    public static final String PAY_SCORE_PROPORTION_ERROR = "wx10041";
    /**
     * 秒杀活动未开始或已结束
     */
    public static final String SEC_KILL_ACTIVITY_NOT_BG_OR_END = "wx10042";
    /**
     * 秒杀活动不能与其他优惠共享
     */
    public static final String SEC_KILL_ACTIVITY_CANNOT_COEXIST_WITH_OTHER_ACTIVITIES = "wx10043";
    /**
     * 秒杀库存不足
     */
    public static final String SEC_KILL_ACTIVITY_QUANTITY_NOT_ENOUGH = "wx10044";
    /**
     * 秒杀商品每人限购{0}个
     */
    public static final String SEC_KILL_PRODUCT_LIMIT_BUY = "wx10045";

    /**
     * 套盒剩余奖品数不足
     */
    public static final String BOX_ITEM_LEFT_REWARD_NOT_ENOUGH = "wx10046";
    /**
     * 套盒一次只能买一个
     */
    public static final String BOX_ITEM_CAN_ONLY_BUY_ONE_ONE_TIME = "wx10047";
    /**
     * 奖品小于等于{0}个，可以全部购买
     */
    public static final String BOX_ITEM_NOT_APPLY_ALL_BUY_CONDITIONS = "wx10048";

    /**
     * 只有未申请赏品可以移入保险柜
     */
    public static final String MEMBER_REWARD_UNAPPLY_TO_LOCK = "wx10049";
    /**
     * 只有保险柜中的赏品可以移除保险柜
     */
    public static final String MEMBER_REWARD_LOCK_TO_UNAPPLY = "wx10050";
    /**
     * 赏袋状态错误
     */
    public static final String MEMBER_REWARD_STATUS_ERROR = "wx10051";
    /**
     * 只有未申请赏品可以申请发货
     */
    public static final String MEMBER_REWARD_UNAPPLY_TO_UNDELIVERED = "wx10052";

    /**
     * 订单已失效
     */
    public static final String ORDER_HAS_EXPIRED = "wx10053";
    /**
     * 赏品已失效
     */
    public static final String MEMBER_REWARD_HAS_EXPIRED = "wx10054";
    /**
     * 不支持的支付方式
     */
    public static final String PAY_TYPE_NOT_SUPPORT = "wx10055";
    /**
     * 怒气值只支持盲盒抽奖
     */
    public static final String SCORE_CAN_ONLY_PAY_BOX = "wx10056";
    /**
     * 赏品【{0}】状态不是未申请，不能上架到黑市
     */
    public static final String MEMBER_REWARD_STATUS_NOT_UNAPPLY = "wx10057";
    /**
     * 赏品信息已过期，请刷新重试
     */
    public static final String MEMBER_REWARD_EXPIRED = "wx10058";
    /**
     * 黑市状态错误
     */
    public static final String BLACK_MARKET_STATUS_ERROR = "wx10059";
    /**
     * 只有下架的黑市可以更新
     */
    public static final String ONLY_OFF_SHELF_MARKET_CAN_UPDATE = "wx10060";
    /**
     * 不能低于竞拍底价
     */
    public static final String CAN_NOT_LOWER_THAN_AUCTION_PRICE = "wx10061";
    /**
     * 已参与竞拍，请勿重复参与
     */
    public static final String ALREADY_IN_THE_AUCTION = "wx10062";
    /**
     * 竞拍状态错误
     */
    public static final String AUCTION_STATUS_ERROR = "wx10063";

    /**
     * 不能撤回他人竞拍
     */
    public static final String CAN_NOT_REVOKE_OTHER_AUCTION = "wx10064";

    /**
     * 当前合适不支持一口价
     */
    public static final String MARKET_NOT_SUPPORT_FIXED_PRICE = "wx10065";

    /**
     * 黑市信息已过期
     */
    public static final String BLACK_MARKET_EXPIRED = "wx10066";
    /**
     * 已被此次交易拉黑，不能出价
     */
    public static final String BE_MARKET_BLACK = "wx10067";
    /**
     * 已被此会员拉黑，不能参与此人发布的交易
     */
    public static final String BE_MEMBER_BLACK = "wx10068";
    /**
     * 怒气值抽奖未配置
     */
    public static final String SCORE_PAY_NOT_CONFIG = "wx10069";
    /**
     * 黑市上架失败，请勿重复操作
     */
    public static final String BLACK_MARKET_ON_SHELF_FAIL = "wx10070";
    /**
     * 昵称已被占用
     */
    public static final String MEMBER_NAME_REPEAT_CHECK = "wx10071";
    /**
     * 竞拍已退款，请勿重复操作
     */
    public static final String AUCTION_ALREADY_REFUND = "wx10072";
    /**
     * 本月修改昵称次数已达上限，请下月再试
     */
    public static final String UPDATE_NAME_TIME_CHECK = "wx10073";
    /**
     * 账号状态异常
     */
    public static final String ACCOUNT_STATUS_IS_ABNORMAL = "wx10074";
    /**
     * 存在敏感词汇，请重试
     */
    public static final String SENSITIVE_WORD_CHECK = "wx10075";

    /**
     * 微信已经授权，请勿重复操作
     */
    public static final String OPENID_IS_EXSIT = "wx10076";
    /**
     * 手机号已经授权，请勿使用其他微信授权
     */
    public static final String APPID_IS_EXSIT = "wx10077";

    /**
     * 赏品【{0}】状态不是未申请，不能回收
     */
    public static final String MEMBER_REWARD_STATUS_NOT_UNAPPLY_NO_RECYCLE = "wx10078";

    /**
     * 赏品回收价信息已变更，请刷新重试
     */
    public static final String MEMBER_REWARD_RECYCLE_FEE_ERROR = "wx10079";

    /**
     * 会员赏品回收失败，请刷新重试
     */
    public static final String MEMBER_REWARD_RECYCLE_FAIL = "wx10080";

    /**
     * 积分抽奖未配置
     */
    public static final String FRACTION_PAY_NOT_CONFIG = "wx10081";

    /**
     * 积分只支持盲盒抽奖
     */
    public static final String FRACTION_CAN_ONLY_PAY_BOX = "wx10082";

    /**
     * 已售出/核销赏品不能生成验证码
     */
    public static final String MEMBER_REWARD_SOLD_STATUS_VERIFY_CODE = "wx10083";
    /**
     * 赏品已生成验证码，请勿重复生成
     */
    public static final String MEMBER_REWARD_HAD_VERIFY_CODE = "wx10084";

    /**
     * 只有线上类型赏品可以申请发货
     */
    public static final String MEMBER_REWARD_ONLINE_TO_UNDELIVERED = "wx10085";
    /**
     * 优惠券剩余数量不足
     */
    public static final String PROXY_COUPON_NOT_ENOUGH = "wx10086";
    /**
     * 系统已开启用户保护模式，套盒前{0}数量内，{1}，无法购买{2}
     */
    public static final String BOX_IS_PROTECTED = "wx10087";
    /**
     * 该商品仅限每人购买{0}次
     */
    public static final String LIMIT_NUM_ERROR = "wx10088";
    /**
     * 预售商品不支持发货
     */
    public static final String IS_PRE_SALE_ERROR = "wx10089";
    /**
     * 使用优惠券只能抽一次
     */
    public static final String COUPON_CAN_ONLY_BUY_ONE = "wx10090";
    /**
     * 生成分享码超时，请重试
     */
    public static final String CREATE_SHARE_CODE_TIME_OUT = "wx10091";

    /**
     * 该盲盒不允许全收
     */
    public static final String BOX_ITEM_CANT_BUY_ALL = "wx10092";

    /**
     * 验证码已发送，请稍后再试
     */
    public static final String MOBILE_VERIFY_HAS_SEND = "wx10093";
    /**
     * 验证码错误或已失效
     */
    public static final String MOBILE_CODE_ERROR = "wx10094";
    /**
     * 该会员已是代理，不可以绑定邀请人
     */
    public static final String USER_IS_PROXY_NOT_INVITE = "wx10095";
    /**
     * 该会员已有邀请人
     */
    public static final String USER_HAS_INVITER = "wx10096";
    /**
     * 该邀请码不存在，请确认后再次输入
     */
    public static final String INVITER_CODE_NOT_EXIST = "wx10097";

    /**
     * 购买商品价格已变更，请重新刷新
     */
    public static final String WX_INFO_PRICE_CHANGED = "wx10098";
    /**
     * 最多只能上传六张图片
     */
    public static final String MAX_THIRTY_PICTURES = "wx10099";
    /**
     * 图片或视频不能为空
     */
    public static final String VIDEO_OR_PICTURES_NOT_EMPTY = "wx10100";
    /**
     * 尊敬的UP主，您暂时不可发布作品呢
     */
    public static final String USER_MUST_NOT_ISSUE = "wx10101";
    /**
     * 抱歉，暂不支持评论
     */
    public static final String USER_MUST_NOT_COMMENT = "wx10102";
    /**
     * 抱歉，暂不支持回复帖子
     */
    public static final String USER_MUST_NOT_REPLY = "wx10103";
    /**
     * 该用户不可以评论、回复
     */
    public static final String USER_MUST_NOT_REPLY_COMMENT = "wx10104";
    /**
     * 用户不可以发布该分类
     */
    public static final String USER_MUST_NOT_PUBLISH_CATEGORY = "wx10105";
    /**
     * 用户不可以评论该分类
     */
    public static final String USER_MUST_NOT_COMMENT_CATEGORY = "wx10106";
    /**
     * 暂无此分类发布权限
     */
    public static final String USER_NO_HAVE_CATEGORY_AUTHORITY = "wx10107";
    /**
     * 所需藏宝图已调整，请刷新后重新合成
     */
    public static final String TREASURE_COMBINE_ERROR = "wx10108";
    /**
     * 一键合成藏宝图参数异常
     */
    public static final String TREASURE_COMBINE_PARAM_ERROR = "wx10109";
    /**
     * 未找到需要合成的藏宝图
     */
    public static final String TREASURE_COMBINE_NOT_CHANGE = "wx10110";

    /**
     * 该商品已下架，无法兑换
     */
    public static final String TREASURE_PRODUCT_NOT_ENABLE = "wx10111";
    /**
     * 商品限兑换{0}个
     */
    public static final String TREASURE_PRODUCT_LIMIT = "wx10112";
    /**
     * 商品库存不足
     */
    public static final String TREASURE_PRODUCT_NOT_ENOUGH = "wx10113";
    /**
     * 商品信息已过期，请刷新后重试
     */
    public static final String TREASURE_PRODUCT_EXPIRED = "wx10114";

    /**
     * 优惠券已结束发放
     */
    public static final String COUPON_IS_OVER = "wx10115";
    /**
     * 该商品/优惠券，已被领取
     */
    public static final String HAVE_BEEN_RECEIVING = "wx10116";
    /**
     * 商品即将到期，无法赠送
     */
    public static final String PRODUCT_ABOUT_TO_EXPIRE = "wx10117";
    /**
     * 不可以领取自己赠送的商品/优惠券
     */
    public static final String DO_NOT_GET_YOURSELF = "wx10118";
    /**
     * 优惠券即将到期，无法赠送
     */
    public static final String COUPON_ABOUT_TO_EXPIRE = "wx10119";
    /**
     * 非未申请商品不可赠送
     */
    public static final String PRODUCT_ABOUT_TO_WSQ = "wx10120";
    /**
     * 非未使用优惠券不可赠送
     */
    public static final String COUPON_ABOUT_TO_WSY = "wx10121";
    /**
     * 非赠送中商品不可领取
     */
    public static final String PRODUCT_ABOUT_TO_GIVING = "wx10122";
    /**
     * 非赠送中优惠券不可领取
     */
    public static final String COUPON_ABOUT_TO_GIVING = "wx10123";
    /**
     * 非赠送中商品不可取消
     */
    public static final String PRODUCT_ABOUT_TO_QX = "wx10124";
    /**
     * 非赠送中优惠券不可取消
     */
    public static final String COUPON_ABOUT_TO_QX = "wx10125";
    /**
     * 赠送失败，功能已下架
     */
    public static final String GIFT_FUNCTION_FOR_REMOVE = "wx10126";
    /**
     * 非赠送中状态，不能领取
     */
    public static final String THIS_THING_NOT_GET = "wx10127";
    /**
     * 该赠送已不存在
     */
    public static final String THIS_THING_NOT_EXIST = "wx10128";
    /**
     * 非赠送者本人，不可取消
     */
    public static final String DO_NOT_CANCEL_YOURSELF = "wx10129";
    /**
     * 该赠品为已被领取或已退回状态，不能取消
     */
    public static final String THIS_THING_NOT_CANCEL = "wx10130";
    /**
     * 领取失败，功能已下架
     */
    public static final String GET_FUNCTION_FOR_REMOVE = "wx10131";
    /**
     * 非自己的商品，不能赠送
     */
    public static final String NOT_YOURSELF_PRODUCT = "wx10132";
    /**
     * 非自己的优惠券，不能赠送
     */
    public static final String NOT_YOURSELF_COUPON = "wx10133";
    /**
     * 该用户已实名认证
     */
    public static final String MEMBER_HAVE_VERIFY = "wx10134";
    /**
     * 请重新输入信息
     */
    public static final String MEMBER_INFO_INPUT_ERROR = "wx10135";
    /**
     * 支付平台未配置
     */
    public static final String ALIPAY_NOT_CONFIG = "wx10136";

    /**
     * 支付平台未配置
     */
    public static final String STRIPEPAY_NOT_CONFIG = "wx10136";

    /**
     * 认证异常
     */
    public static final String VERIFY_ERROR = "wx10137";

    /**
     * app登录并发异常
     */
    public static final String APP_LOGIN_CONCURRENCY_ERROR = "wx10138";
    /**
     * 赏品状态异常，请刷新重试
     */
    public static final String UNIVERSE_REWARD_STATUS_ERROR = "wx10139";

    /**
     * 所选赏品状态已变更，请刷新重试
     */
    public static final String UNIVERSE_REWARD_CHANGED = "wx10140";
    /**
     * 商家未配置换换宇宙，请联系客服
     */
    public static final String UNIVERSE_NOT_CONFIG = "wx10141";
    /**
     * 不支持的赏品来源类型
     */
    public static final String UNIVERSE_REWERAD_TYPE_ERROR = "wx10142";
    /**
     * 所选赏品正在交换中，请勿重复操作
     */
    public static final String REWARD_IS_CHANGED = "wx10143";
    /**
     * 此圈子已被删除
     */
    public static final String MEDIA_IS_DELETE = "wx10144";

    /**
     * 订单状态错误或订单已删除
     */
    public static final String ORDER_STATUS_ERROR_OR_ORDER_DELETE = "wx10145";
    /**
     * 暂无赠送权限，请联系客服处理
     */
    public static final String USER_MUST_NOT_MEMBER_GIFT = "wx10146";
    /**
     * 抱歉，您暂时无法参与换换宇宙哦
     */
    public static final String USER_MUST_NOT_CHANGE_UNIVERSE = "wx10147";
    /**
     * 该作品已被删除
     */
    public static final String MEDIA_HAS_BEEN_DELETE = "wx10148";
    /**
     * 发布内容存在敏感信息
     */
    public static final String MEDIA_CONTENT_HAVE_RISKY_CONTENT = "wx10149";
    /**
     * 支付处理中，请稍后重试
     */
    public static final String BUY_BOX_IS_PROCESSING = "wx10150";
    /**
     * 该评论已不存在
     */
    public static final String MEDIA_COMMENT_HAS_BEEN_DELETE = "wx10151";
    /**
     * 因未满足锁箱条件，您无法锁箱
     */
    public static final String LOCK_BOX_CONDITION_NOT_ENOUGH = "wx10152";
    /**
     * 当前套盒处于锁定状态，请耐心等待或前往其它套盒进行游玩
     */
    public static final String BOX_IS_LOCKING = "wx10153";
    /**
     * 当天已达锁定次数上限
     */
    public static final String LOCK_NUM_NOT_ENOUGH = "wx10154";
    /**
     * 本箱当前处于锁箱状态，请等待3-5分钟 也可切换到其他箱购买
     */
    public static final String BOX_IS_LOCKING_BY_OTHER = "wx10155";
    /**
     * 当前分类不支持锁箱
     */
    public static final String CATEGORY_NOT_SUPPORT_LOCK = "wx10156";
    /**
     * 该订单暂时无法在{0}上支付，请回到{1}支付
     */
    public static final String ORDER_PAY_PLATFORM_ERROR = "wx10157";
    /**
     * 信息错误，请重新评论
     */
    public static final String INFO_ERROR_TRY_AGAIN = "wx10158";
    /**
     * 抱歉，您暂时无法发布交易哦
     */
    public static final String DO_NOT_RELEASE_DEAL = "wx10159";
    /**
     * 状态错误,不能下架
     */
    public static final String CHANGE_STATUS_ERROR = "wx10160";
    /**
     * 竞拍最低出价不能为空
     */
    public static final String AUCTION_MINIMUM_BID_CANNOT_BE_EMPTY = "wx10161";
    /**
     * 竞拍结束时间不能为空
     */
    public static final String AUCTION_END_TIME_CANNOT_BE_EMPTY = "wx10162";
    /**
     * 该出价记录不存在
     */
    public static final String THE_BID_RECORD_DOES_NOT_EXIST = "wx10163";
    /**
     * 无法购买自己发布的商品
     */
    public static final String UNABLE_TO_PURCHASE_SELF_PUBLISHED_ITEMS = "wx10164";
    /**
     * 闲置商品类型错误
     */
    public static final String INCORRECT_IDLE_ITEM_TYPE = "wx10165";
    /**
     * 已售出的商品不能进行成交操作
     */
    public static final String THE_SOLD_PRODUCT_CANNOT_BE_TRADED = "wx10166";
    /**
     * 所选的结束时间需晚于当下时间一小时后
     */
    public static final String THE_SELECTED_END_TIME_MUST_BE_ONE_HOUR_AFTER_THE_CURRENT_TIME = "wx10167";
    /**
     * 您已参与过此商品的竞拍, 请勿重复参与
     */
    public static final String YOU_HAVE_ALREADY_BID_FOR_THIS_ITEM_DO_NOT_REPEAT_PARTICIPATION = "wx10168";
    /**
     * 该客户端暂不支持退出登出
     */
    public static final String CLIENT_NOT_SUPPORT_SIGN_OUT = "wx10169";
    /**
     * 该商品无详情
     */
    public static final String THIS_PRODUCT_IS_NOT_DETAIL = "wx10170";
    /**
     * 兑换失败，不符合兑换条件
     */
    public static final String MEMBER_TAG_NOT_MATCH = "wx10171";
    /**
     * 兑换失败，已超过兑换次数
     */
    public static final String GT_WELFARE_EXCHANGE_TIME = "wx10172";
    /**
     * 商家未配置玩家互赠，请联系客服
     */
    public static final String MEMBER_GIFT_NOT_CONFIG = "wx10173";
    /**
     * 不支持的商品来源类型
     */
    public static final String GIFT_PRODUCT_TYPE_ERROR = "wx10174";
    /**
     * 不支持的优惠券来源类型
     */
    public static final String GIFT_COUPON_TYPE_ERROR = "wx10175";
    /**
     * 申请失败，已超过当日申请退款限制次数，请明天再试
     */
    public static final String EXCEEDED_THE_NUMBER_OF_TIMES_TO_APPLY_FOR_A_REFUND_ON_THE_DAY = "wx10176";
    /**
     * 申请失败，已超过当日申请退款限制金额，请明天再试
     */
    public static final String EXCEEDED_THE_REFUND_LIMIT_AMOUNT_APPLIED_FOR_THE_DAY = "wx10177";
    /**
     * 请勿重复提交，请稍后再试
     */
    public static final String REPEATED_SUBMIT_ERROR = "wx10178";
    /**
     * 抱歉，您暂时无法访问哦
     */
    public static final String USER_MUST_NOT_ACCESS = "wx10179";
    /**
     * 亲，您暂时不能进行交易哦
     */
    public static final String USER_MUST_NOT_TRADE = "wx10180";
    /**
     * 亲，您暂时不能进行消费哦
     */
    public static final String USER_MUST_NOT_REWARD = "wx10181";
    /**
     * 亲，您暂时不能进行此操作哦
     */
    public static final String USER_MUST_NOT_WITHDRAW = "wx10182";
    /**
     * 该板块进群交流未配置
     */
    public static final String WE_CHAT_GROUP_NOT_CONFIG = "wx10183";
    /**
     * 未进行实名认证
     */
    public static final String REAL_NAME_AUTHENTICATION_IS_NOT_PERFORMED = "wx10184";
    /**
     * 发货数量不能超过100
     */
    public static final String SHIP_COUNT_ERROR = "wx10185";
    /**
     * 发货数量不能超过5000
     */
    public static final String RECYCLE_COUNT_ERROR = "wx10186";
    /**
     * 回收处理中请稍后再试
     */
    public static final String RECYCLE_IS_DOING = "wx10187";
    /**
     * 抱歉，您暂时无法发布自建赏
     */
    public static final String DO_NOT_RELEASE_MEMBER_BOX = "wx10188";

    /**
     * 序号数量与购买数量不匹配
     */
    public static final String SERIAL_NUMBER_NOT_MATCH = "wx10189";

    /**
     * 序号不能重复
     */
    public static final String SERIAL_NUMBER_CAN_NOT_REPEAT = "wx10190";

    /**
     * {0}号位置已经被人占了，请重新选择
     */
    public static final String SERIAL_NUMBER_HAVE_BEEN_OCCUPY = "wx10191";
    /**
     * 序号范围错误
     */
    public static final String SERIAL_NUMBER_RANGE_ERROR = "wx10192";
    /**
     * 请选择序号
     */
    public static final String SERIAL_NUMBER_NOT_FOUND = "wx10193";
    /**
     * 呀，手慢未抢上，刷新重新选购吧
     */
    public static final String AH_SLOW_HAND_DID_NOT_GRAB_REFRESH_TO_BUY_AGAIN = "wx10194";
    /**
     * 暂无领取权限，请联系客服处理
     */
    public static final String USER_MUST_NOT_MEMBER_GET = "wx10195";
    /**
     * 每日只可赠送x次，当日以达上限，请明日再来
     */
    public static final String GIFT_LINK_GIVE_NUM_CHECK = "wx10196";
    /**
     * “{0}”类型商品当前不支持发布
     */
    public static final String CHANGE_TRADE_PRODUCT_TYPE_ERROR = "wx10197";
    /**
     * {0}商品当前不支持发布
     */
    public static final String CHOICE_PRODUCT_TYPE_ERROR = "wx10198";
    /**
     * 商家未配置发布功能，请联系客服
     */
    public static final String CHANGE_TRADE_IS_NOT_CONFIG = "wx10199";
    /**
     * “{0}”类型商品当前不支持赠送
     */
    public static final String GIVE_TRADE_PRODUCT_TYPE_ERROR = "wx10200";
    /**
     * “{0}”类型优惠券当前不支持赠送
     */
    public static final String GIVE_TRADE_COUPON_TYPE_ERROR = "wx10201";
    /**
     * 无法对自己膜拜，换个人膜拜吧
     */
    public static final String CAN_NOT_WORSHIP_OWNER = "wx10202";
    /**
     * 潮人榜未配置，请联系客服
     */
    public static final String RANKING_LIST_NOT_OPEN = "wx10203";
    /**
     * 今日膜拜次数已达上限，请明日再来
     */
    public static final String WORSHIP_TIMES_ERROR = "wx10204";
    /**
     * 该玩家今日被膜拜次数已达上限，请明日再来
     */
    public static final String ARE_WORSHIP_TIMES_ERROR = "wx10205";
    /**
     * 闯关盲盒阶段id不能不为空
     */
    public static final String CONCATENATE_BOX_STAGEID_IS_NULL = "wx10206";
    /**
     * 您无法与此人进行交易
     */
    public static final String CANNOT_TRADE_WITH_THIS_PLAYER = "wx10214";
    /**
     * 亲爱的用户，今日现金抽赏次数已达上限！您可以用币进行支付，若币不足，请立即去充值。
     */
    public static final String CASH_TIMES_EXCEED_TODAY = "wx10215";

    /**
     * 该盲盒礼品只能领取一次
     */
    public static final String NEWCOMER_ACTIVITY_GIFT_ERROR = "wx10216";

    /**
     * 对战参数缺失
     */
    public static final String FIGHT_REQUEST_IS_NULL = "wx10217";

    /**
     * 对战序号不能为空
     */
    public static final String FIGHT_SERIAL_NUM_IS_NULL = "wx10218";
    /**
     * 对战序号已被占用
     */
    public static final String FIGHT_SERIAL_IS_OCCUPATION = "wx10219";
    /**
     * 对战只能购买一次
     */
    public static final String FIGHT_ONLY_BUY_ONE = "wx10220";

    /**
     * 只有4人支持组团模式
     */
    public static final String FIGHT_ONLY_FOUR_SUPPORT_GROUP = "wx10221";

    /**
     * 序号不能大于人数
     */
    public static final String SERIAL_NUM_LT_PERSON_NUM = "wx10222";
    /**
     * 抱歉，您暂时无法发布roll房哦
     */
    public static final String DO_NOT_ISSUE_ROLL = "wx10223";
    /**
     * 抱歉，您暂时无法参与roll房哦
     */
    public static final String DO_NOT_PART_ROLL = "wx10224";

    /**
     * roll房不存在或已删除
     */
    public static final String ROLL_NOT_EXIST = "wx10225";

    /**
     * roll房状态错误
     */
    public static final String ROLL_STATUS_ERROR = "wx10226";

    /**
     * 您输入的口令有误，请重新输入
     */
    public static final String ROLL_PASSWORD_ERROR = "wx10227";

    /**
     * {0}
     */
    public static final String ROLL_RULE_ERROR = "wx10228";

    /**
     * 您已参加，请勿重复参与
     */
    public static final String ROLL_JOIN_REPEAT = "wx10229";

    /**
     * 参与人数已满，请等待开奖
     */
    public static final String ROLL_JOIN_FULL = "wx10230";

    /**
     * 存在不支持回收赏品
     */
    public static final String MEMBER_REWARD_UN_SUPPORT_RECYCLING = "wx10231";

    /**
     * 邮箱已注册
     */
    public static final String MAIL_HAS_SIGN_UP = "wx10232";

    /**
     * 账号或密码错误
     */
    public static final String MAIL_ACCOUNT_ERROR = "wx10233";

    /**
     * 邮箱不存在
     */
    public static final String MAIL_ACCOUNT_IS_NOT_EXIST = "wx10234";

    /**
     * 消耗赏品回收价不能小于合成商品售价
     */
    public static final String REWARD_RECOVERY_FEE_NOT_AMPLE = "wx10235";
    /**
     * 对战功能未开启
     */
    public static final String FIGHT_NOT_ENABLE = "wx10236";
    /**
     * 对战只支持无限盲盒
     */
    public static final String FIGHT_ONLY_SUPPORT_UN_LIMIT= "wx10237";

    /**
     * 验证码不能为空
     */
    public static final String MOBILE_CODE_IS_NULL = "wx10238";

    /**
     * openId 不能为空
     */
    public static final String OPEN_ID_IS_NULL = "wx10239";
    /**
     * 账户已授权，请勿重复登入
     */
    public static final String OPEN_ID_IS_EXIST = "wx10240";

    /**
     * 对战盲盒中未设置当前盲盒
     */
    public static final String FIGHT_PART_EXIST = "wx10241";

    /**
     * 全收时不支持使用此优惠券
     */
    public static final String THIS_COUPON_NOT_USED_FOR_ALL_BUY = "wx10242";

    /**
     * 抽一发时不支持使用此优惠券
     */
    public static final String THIS_COUPON_NOT_USED_FOR_ONE = "wx10243";

    /**
     * 抽三发时不支持使用此优惠券
     */
    public static final String THIS_COUPON_NOT_USED_FOR_THREE = "wx10244";

    /**
     * 抽十发时不支持使用此优惠券
     */
    public static final String THIS_COUPON_NOT_USED_FOR_TEN = "wx10245";

    /**
     * 抱歉，您暂时无法发起对战哦
     */
    public static final String DO_NOT_ISSUE_FIGHT = "wx10246";

    /**
     * 抱歉，您暂时无法参与对战哦
     */
    public static final String DO_NOT_PART_FIGHT = "wx10247";

    /**
     * DIY盲盒未开启
     */
    public static final String DIY_BOX_IS_NOT_OPEN = "wx10248";
    /**
     * 保底奖励不能为空
     */
    public static final String SMALL_PRIZE_IS_NULL = "wx10249";

    /**
     * 一次最多支持100个宝箱同时开启
     */
    public static final String OPEN_TREASURE_NUM_ERROR = "wx10250";

    /**
     * 抱歉，您暂时无法参与该玩法哦
     */
    public static final String DO_NOT_PART_DIY_BOX = "wx10251";

    /**
     * 心仪大奖概率不能超过80%
     */
    public static final String BIG_PRIZE_ODDS_ERROR = "wx10252";
    /**
     * 奖品概率总和不等于1
     */
    public static final String DIY_BOX_PRIZE_ODDS_ERROR = "wx10253";
    /**
     * 单个红包不可低于0.01元
     */
    public static final String RED_PACKAGE_MONEY_NUM_ERROR = "wx10254";

    /**
     * 红包不存在
     */
    public static final String RED_PACKAGE_NOT_FOUND = "wx10255";
    /**
     * 红包已过期，无法领取
     */
    public static final String RED_PACKAGE_IS_EXPIRED = "wx10256";
    /**
     * 来晚了，红包已被抢光
     */
    public static final String RED_PACKAGE_IS_DONE = "wx10257";
    /**
     * 每个红包只能领取一次
     */
    public static final String RED_PACKAGE_GET_ONE = "wx10258";

    /**
     * 心仪奖品价格不得小于等于保底奖
     */
    public static final String BIG_PRIZE_FEE_LESS_THAN_SMALL_PRIZE_FEE = "wx10259";

    /**
     * 抱歉，您暂时无法发红包哦
     */
    public static final String DO_NOT_PROVIDE_RED_PACKAGE = "wx10260";

    /**
     * 抱歉，您暂时无法领红包哦
     */
    public static final String DO_NOT_RECEIVE_RED_PACKAGE = "wx10261";
    /**
     * 红包功能未配置
     */
    public static final String RED_PACKAGE_NOT_CONFIG = "wx10262";
    /**
     * 每日发红包总额不能超过{0}
     */
    public static final String DAY_RED_PACKAGE_MONEY_ERROR = "wx10263";

    /**
     * 不持支当前购买数量
     */
    public static final String DIY_BOX_NUM_ERROR = "wx10264";

    /**
     * 对战最多支持50个盲盒
     */
    public static final String FIGHT_BOX_MAX_NUM_ERROR= "wx10265";
    /**
     * 会员信息未找到
     */
    public static final String MEMBER_NOT_FIND_ERROR = "wx10266";

    /**
     * 每日仅可购买{0}件，明日再来吧
     */
    public static final String COMMODITY_LIMITATION_ERROR = "wx10267";

    /**
     * 此功能已下架
     */
    public static final String FEATURE_HAS_BEEN_REMOVED = "wx10268";

    /**
     * 缺少指定合成商品
     */
    public static final String MISSING_SPECIFIED_COMPOSITE_PRODUCT = "wx10269";
    /**
     * 奖品{0}数量不满足合成条件
     */
    public static final String  NUMBER_PRIZES_DOES_NOT_CONDITIONS = "wx10270";
    /**
     * 当前会员等级不满足参与条件
     */
    public static final String  MEMBER_LEVEL_INSUFFICIENT = "wx10271";

    /**
     * Ta未参与抽奖，无法助力
     */
    public static final String  ROLL_INVITE_ERROR_ONE = "wx10272";

    /**
     * 您已参与，无法为Ta助力
     */
    public static final String  ROLL_INVITE_ERROR_TWO = "wx10273";
    /**
     * 今日助力已达上限
     */
    public static final String  ROLL_INVITE_ERROR_THREE = "wx10274";
    /**
     * 仅新人可为Ta助力
     */
    public static final String  ROLL_INVITE_ERROR_FOUR = "wx10275";
    /**
     * 当前合成赏品活动不存在
     */
    public static final String  SYNTHESIS_NOT_EXIST = "wx10276";
    /**
     * 当前活动已下架无法参与
     */
    public static final String  SYNTHESIS_NOT_JOIN = "wx10277";
    /**
     * 用户信息未创建，请联系管理员
     */
    public static final String MEMBER_NOT_FOUND= "wx10278";
    /**
     * 非平台会员，无法充值
     */
    public static final String DONT_MEMBER_NOT_RECHARGE= "wx10279";
    /**
     * openId不允许为空
     */
    public static final String OPENID_IS_NOT_NULL = "wx10280";

    /**
     * 抽五发时不支持使用此优惠券
     */
    public static final String THIS_COUPON_NOT_USED_FOR_FIVE = "wx10281";
    /**
     * 心仪大奖概率不能小于{0}%
     */
    public static final String BIG_PRIZE_MIN_ODDS_ERROR = "wx10282";
    /**
     * 今日可赠送商品价值最高共x元，已超上限，请明日在来
     */
    public static final String GIFT_LINK_GIVE_MONEY_CHECK = "wx10283";
    /**
     * 数据规则发生变化,请重新进入
     */
    public static final String GET_CASH_BACK_AMOUNT_ERROR = "wx10284";
    /**
     * 资源不存在或已删除
     */
    public static final String  RESOURCE_NOT_EXIST_OR_DELETED = "wx10285";

    /**
     * 众筹中不可预约或该众筹已不存在
     */
    public static final String CROWD_HAS_BEEN_DELETE = "wx10286";
    /**
     * 该众筹不存在
     */
    public static final String CROWD_FUNDING_HAS_BEEN_DELETE = "wx10287";

    /**
     * 不是众筹订单
     */
    public static final String NOT_A_CROWDFUNDING_ORDER = "wx10288";


    /**
     * 众筹距离结束大于1小时时可申请退款
     */
    public static final String REFUND_CAN_BE_APPLIED_WHEN_THE_DISTANCE_FROM_THE_END_OF_CROWDFUNDING_IS_MORE_THAN_1_HOUR = "wx10289";


    /**
     * IP分润权只能购买一个
     */
    public static final String SERVICE_CAN_ONLY_ONE = "wx10290";
    /**
     * IP分润权只能购买一次
     */
    public static final String SERVICE_ALREAY_BUY = "wx10291";

    public static final String RED_PACKAGE_MEMBER_ERROR = "wx10292";


    /**
     * 众筹商品不存在
     */
    public static final String CROWDFUNDING_PRODUCT_NOT_EXIST = "wx10293";

    /**
     * {0}品剩余库存不足
     * */
    public static final String PRODUCT_NUM_INSUFFICIENT = "wx10294";

    /**
     * 当前众筹状态不可购买商品
     */
    public static final String CROWDFUNDING_STATUS_ERROR = "wx10295";
    /**
     * 当前众筹状态不支持支付
     */
    public static final String CROWDFUNDING_STATUS_FAIL = "wx10296";
    /**
     * 每人仅可购买一次此众筹
     */
    public static final String CROWDFUNDING_ORDER_EXIST = "wx10297";
    /**
     * 该商品退款时间以过,无法申请退款
     */
    public static final String ORDER_REFUND_DATE_EXPIRE_ERROR = "wx10298";
    /**
     * 会员昵称不可以含有特殊字符
     */
    public static final String MEMBER_NAME_CANNOT_HAS_SPECIAL_SYMBOLS = "wx10299";
    /**
     * 您当前不满足参与签到规则，无法进行签到
     */
    public static final String CURRENTLY_NOT_MEET_THE_ATTENDANCE_RULES = "wx10300";
    /**
     * 您当前消费流水还差{0}元,即可完成签到
     */
    public static final String CURRENTLY_MONEY_IS_INSUFFICIENT_AMOUNT = "wx10301";




    /**
     * 暂不符合赠送条件，请联系客服
     */
    public static final String CURRENTLY_NOT_ELIGIBLE_FOR_GIFT = "wx10302";

    /**
     * 累计充值金额不足{0}元，无法赠送
     */
    public static final String TCUMULATIVE_RECHARGE_AMOUNT_IS_LESS_CANNOT_BE_GIVEN_GIFT = "wx10303";

    /**
     * 领取失败，暂不符合领取条件
     */
    public static final String CLAIM_FAILED_CURRENTLY_NOT_MEETING_THE_CLAIM_CRITERIA = "wx10304";


    /**
     * 今日回赠总次数已达上限
     */
    public static final String REBATE_LINK_GIVE_NUM_CHECK = "wx10305";
    /**
     * 今日回赠总金额已达上限
     */
    public static final String REBATE_LINK_GIVE_MONEY_CHECK = "wx10306";
    /**
     * 回赠成功
     */
    public static final String REBATE_SUCCESS = "wx10307";
    /**
     * 商家未配置玩家回赠，请联系客服
     */
    public static final String MEMBER_REBATE_NOT_CONFIG = "wx10308";
    /**
     * 商家未配置玩家回赠，请联系客服
     */
    public static final String MEMBER_NOT_CONFIG = "wx10309";
    /**
     * 不可以回赠自己
     */
    public static final String CAN_NOT_REBATE_SELF = "wx10310";

    /**
     * 当前宝箱活动不存在
     */
    public static final String TREASURE_IS_NOT_EXIST = "wx10311";
    /**
     * 活动暂未开始，请耐心等待～
     */
    public static final String TREASURE_ACTIVITY_IS_NO_OPEN = "wx10312";
    /**
     * 活动已结束，期待下次活动吧～
     */
    public static final String TREASURE_ACTIVITY_IS_OVER= "wx10313";
    /**
     * 此功能暂未开启
     */
    public static final String TREASURE_ACTIVITY_IS_NOT_OPEN= "wx10314";
    /**
     * 自己不可以给自己助力哦~
     */
    public static final String SHARE_CAN_NOT_SHARE_YOURSELF= "wx10315";

    /**
     * 身份不符,无法助力
     */
    public static final String NEW_PERSON_CAN_NOT_SHARE_OLD_USERS= "wx10316";

    /**
     * 累计消费金额不足{0}元，无法赠送
     */
    public static final String CUMULATIVE_TRADE_AMOUNT_IS_LESS_CANNOT_BE_GIVEN_GIFT = "wx10317";

    /**
     * 请输入正确的ID
     */
    public static final String PLEASE_INPUT_MEMBER_CODE = "wx10318";

    /**
     * 不支持此类型的互赠，请联系客服咨询
     */
    public static final String CAN_NOT_GIFT_BY_METHOD = "wx10319";

    /**
     * 不可以赠送自己的商品/优惠券
     */
    public static final String DO_NOT_GIVE_YOURSELF = "wx10320";
    /**
     * 无法识别卡牌信息
     */
    public static final String CARD_ITEMS_IDENTIFY_ERROR = "wx10321";
    /**
     * 该卡牌已被绑定
     */
    public static final String CARD_HAS_BEEN_BOUND = "wx10322";
    /**
     * 卡牌信息不存在
     */
    public static final String CARD_ITEMS_IS_NOT_FIND = "wx10323";
    /**
     * 一件合成最多支持2000个赏品
     */
    public static final String MA_SYNTHESIS_NUM_ERROR = "wx10324";
    /**
     * 亲，您暂时不能进行消费哦
     */
    public static final String DO_NOT_BUY_PRODUCT = "wx10325";

    /**
     * 等级不足无法购买
     */
    public static final String PRODUCT_MEMBER_LEVEL_INSUFFICIENT = "wx10326";

    /**
     * 输入金额不可超过可划转总收益
     */
    public static final String INPUT_PRICE_CANT_GANT_BALANCE = "wx10327";

    /**
     * 今日可消费已达上限，休息一会吧
     */
    public static final String CONSUMER_REMINDER_IS_REACHING_THE_UPPER_LIMIT = "wx10328";

    /**
     * 今日可消费已达上限，休息一会吧
     */
    public static final String MEMBER_BALANCE_NOT_ENOUGH = "wx10329";

    /**
     * 该手机号无法注册，请更换正确的手机号
     */
    public static final String MEMBER_MOBILE_CAN_NOT_LOGIN = "wx10330";

    /**
     * 当前商品不支持赠送
     */
    public static final String THE_PRODUCT_DONT_GIFT = "wx10331";
    /**
     * 冒险盲盒关卡数不匹配
     */
    public static final String ADVENTURE_BOX_STAGE_NOT_MATCH = "wx10332";

    /**
     * 当前商品合成次数已达上限，请明日再试
     */
    public static final String SYNTHESIS_TIMES_LIMIT = "wx10333";

    /**
     * 标签已存在
     */
    public static final String LABEL_ALREADY_EXISTS = "wx10334";


    /**
     * 评论内容不能为空
     */
    public static final String COMMENT_IS_NULL = "wx10335";

    /**
     * 累计消费金额不足{0}元，无法领取
     */
    public static final String CUMULATIVE_TRADE_AMOUNT_IS_LESS_CANNOT_BE_REC_GIFT = "wx10336";

    /**
     * 卡册卡牌不足
     */
    public static final String MEMBER_BOOK_CARD_NOT_ENOUGH = "wx10337";

    /**
     * 卡牌状态错误，请刷新重试
     */
    public static final String MEMBER_CARD_STATUS_ERROR = "wx10338";
    /**
     * 卡册未配置奖品
     */
    public static final String CARD_BOOK_NOT_CONFIG_PRIZE = "wx10339";
    /**
     * 屏蔽用户已达上限
     */
    public static final String MADIA_BLOCK_USER_ERR = "wx10340";

    /**
     * 商品{0}，每日限购{1}个
     */
    public static final String PRODUCT_DAY_LIMIT_ERROR = "wx10341";

    /**
     * 未满足锁定条件，差{0}抽可锁定
     */
    public static final String LOCK_BOX_CONDITION_NOT_ENOUGH_V2 = "wx10342";

    /**
     * 锁箱功能未配置或已关闭
     */
    public static final String LOCK_CONFIG_NOT_OPEN = "wx10343";
    /**
     * 该盲盒图鉴已经兑换过,无法再次兑换
     */
    public static final String PRODUCT_BOX_BOOK_MEMBER_IS_EXCHANGE = "wx10344";
    /**
     * 未达到兑换条件,无法兑换
     */
    public static final String PRODUCT_BOX_BOOK_MEMBER_IS_NOT_CONVERTIBLE = "wx10345";
    /**
     * 未找到盲盒图鉴信息
     */
    public static final String PRODUCT_BOX_BOOK_MEMBER_IS_NOT_FIND = "wx10346";

    /**
     * 该转盘已下架，请刷新
     */
    public static final String TURNTABLE_DOWN = "wx10347";

    /**
     * 您的抽奖余额不够，无法抽奖
     */
    public static final String TURNTABLE_MONEY_LIMIT = "wx10348";

    /**
     * 当前转盘游玩次数已用完
     */
    public static final String TURNTABLE_LIMIT = "wx10349";

    /**
     * 银行卡号已存在
     */
    public static final String MEMBER_BANK_ACCOUNT_EXIST = "wx10350";

    /**
     * 流水转盘活动未开启
     */
    public static final String TURNTABLE_CLOSED = "wx10351";

    /**
     * 积分余额不足,无法兑换
     */
    public static final String OQ_PRODUCT_POINT_NOT_ERROR = "wx10352";
    /**
     * 未找到对对碰游戏信息
     */
    public static final String COLLISION_NOT_FOUND = "wx10353";
    /**
     * 对对碰游戏状态有误,无法结束游戏
     */
    public static final String COLLISION_STATUS_IS_ERROR_NOT_FINISH = "wx10354";
    /**
     * 目前还有剩余包数,无法结束游戏
     */
    public static final String COLLISION_LEFTNUM_IS_ERROR_NOT_FINISH = "wx10355";
    /**
     * 幸运奖品不在奖品列表内,请重新选择
     */
    public static final String COLLISION_LUCKY_SKU_IS_ERROR = "wx10356";
    /**
     * 对对碰游戏状态有误,无法进行游戏
     */
    public static final String COLLISION_STATUS_IS_ERROR_NOT_GAME = "wx10357";
    /**
     * 请先确定幸运奖品
     */
    public static final String COLLISION_LUCKY_SKU_IS_NOT_FIND = "wx10358";
    /**
     * 购买自定义包数是不能低于最小值
     */
    public static final String COLLISION_NOT_BUY_NUM_ERROR = "wx10359";
    /**
     * 碰撞游戏未配置包数价格
     */
    public static final String COLLISION_IS_NOT_FIND_RECHARGE = "wx10360";
    /**
     * 支付金额不正确
     */
    public static final String COLLISION_BUY_MONEY_ERROR  = "wx10361";
    /**
     * 当前阶段已经续包过,无法继续续包
     */
    public static final String COLLISION_STAGE_CONTINUED_IS_ERROR = "wx10362";
    /**
     * 当前不在续包阶段无法续包,无法购买续包
     */
    public static final String COLLISION_STAGE_CONTINUED_BUY_IS_ERROR = "wx10363";
    /**
     * 当前对局游戏信息错误,无法进行游戏
     */
    public static final String COLLISION_GAME_JSON_IS_ERROR = "wx10364";
    /**
     * 此套已删除，请换场游玩
     */
    public static final String COLLISION_MEMBER_IS_NOT_FIND = "wx10365";
    /**
     * 未找到投票活动
     */
    public static final String VOTE_ACTIVITY_IS_NOT_FIND = "wx10367";
    /**
     * 投票失败
     */
    public static final String VOTE_ACTIVITY_IS_ERROR = "wx10368";
    /**
     * 投票次数已用完
     */
    public static final String VOTE_ACTIVITY_LIMIT_NOT_ERROR = "wx10369";
    /**
     * 亲，您暂时不能进行发货哦
     */
    public static final String DO_NOT_DELIVERY = "wx10370";
    /**
     * 用户闯关数据不正确，请稍后再试
     */
    public static final String UP_BOX_STAGE_DATA_ERROR = "wx10371";
    /**
     * 用户关卡不正确
     */
    public static final String UP_BOX_STAGE_ERROR = "wx10372";

    /**
     * 第一关需要选择商品
     */
    public static final String UP_BOX_STAGE_ONE_ERROR = "wx10373";

    /**
     * 第三关需要选择序号和套盒
     */
    public static final String UP_BOX_STAGE_THREE_ERROR = "wx10374";
    /**
     * 抽奖次数已变更，请重新选择
     */
    public static final String UP_BOX_OPEN_NUM_ERROR = "wx10375";
    /**
     * 盲盒商品已变更，请重新选择
     */
    public static final String UP_BOX_SELECT_ERROR = "wx10376";
    /**
     * 购买失败，套盒不支持付款购买
     */
    public static final String BUY_PAY_TYPE_COUPON_ERROR = "wx10377";
    /**
     * 分享有礼超时,请重试
     */
    public static final String HANDLE_GIFT_SHARE_CONCURRENT_ERROR = "wx10378";
    /**
     * 异空间不存在
     */
    public static final String DIFFERENT_SPACE_IS_NOT_FIND = "wx10379";
    /**
     * 异空间状态未开启,请刷新后再试
     */
    public static final String DIFFERENT_SPACE_STATUS_ERROR = "wx10380";
    /**
     * 玩家入场券不足,请先去抽取盲盒
     */
    public static final String DIFFERENT_SPACE_MEMBER_QUANTITY_ERROR = "wx10381";
}