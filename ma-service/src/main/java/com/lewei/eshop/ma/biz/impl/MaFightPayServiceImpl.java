package com.lewei.eshop.ma.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.auth.ma.SSOMaAppCache;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.data.FightConfigEnum;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.member.MemberBalanceTradeTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.fight.FightBoxRequest;
import com.lewei.eshop.common.request.fight.FightPayReq;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.vo.config.FightConfigVo;
import com.lewei.eshop.common.vo.fight.FightBoxVo;
import com.lewei.eshop.entity.config.FightConfig;
import com.lewei.eshop.entity.fight.Fight;
import com.lewei.eshop.entity.fight.FightBox;
import com.lewei.eshop.entity.fight.FightMember;
import com.lewei.eshop.entity.member.types.MemberAuthorityEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.OrderProduct;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.product.box.ProductBox;
import com.lewei.eshop.entity.product.box.types.ProductBoxTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.biz.IChainConfigService;
import com.lewei.eshop.ma.biz.IMaFightPayService;
import com.lewei.eshop.ma.biz.IMaMemberService;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.message.vo.SaveOrderVO;
import com.lewei.eshop.ma.pay.FightCallbackProcessor;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.sso.MemberCache;
import com.lewei.log.trace.MDCTraceUtils;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/8
 */
@Transactional
@Service
public class MaFightPayServiceImpl implements IMaFightPayService {

    private final String callBackUrl = "/api/ma/pay/fight/callback";
    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IChainConfigService chainConfigService;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private FightCallbackProcessor fightCallbackProcessor;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private RedisCacheProvider redisCacheProvider;
    @Autowired
    private IMaMemberService memberService;

    @Override
    public SaveOrderVO fightPay(FightPayReq payReq, Long memberId, MemberCache memberCache, SSOMaAppCache ssoMaAppCache) {
        Long chainId = memberCache.getChainId();
        String openId = memberCache.getOpenId();

        Date now = DateFormatUtils.getNow();

        Map<String,Object> extJosnMap = new HashMap<>();
        //验证开关
        SaasQueryBuilder queryFightConfig = SaasQueryBuilder.create();
        FightConfig fightConfig = dao.query(queryFightConfig,FightConfig.class);

        if (fightConfig == null || BooleanUtils.isNotTrue(fightConfig.getIsEnable())) {
            throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_NOT_ENABLE);
        }

        if (payReq.getFightRequest() != null){
            // 不可发起对战验证
            boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_ISSUE_FIGHT, memberId);
            if (isAuthority) {
                throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_ISSUE_FIGHT);
            }
            //验证盲盒
            List<Long> spuIds = payReq.getFightRequest().getFightBoxList().stream().map(FightBoxRequest::getSpuId).collect(Collectors.toList());
            SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.in("spuId",spuIds))
                    .and(Restrictions.ne("boxType", ProductBoxTypeEnum.S_BT_UN_LIMIT.value()));
            List<ProductBox> productBoxes = dao.queryList(query,ProductBox.class);
            if (ListUtil.isNotEmpty(productBoxes)){
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_ONLY_SUPPORT_UN_LIMIT);
            }
            if (payReq.getFightRequest().getFightBoxList().size() > 20){
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_BOX_MAX_NUM_ERROR);
            }

            //验证当前盲盒是否在选定范围内
            if (fightConfig.getChoseType().equals(FightConfigEnum.PART_EXIST.value())){
                if (StringUtils.isNotBlank(fightConfig.getSpuIds())) {
                    //盲盒spuIdS
                    if (CollectionUtil.isNotEmpty(spuIds)){
                        spuIds.forEach(a->{
                            if (!fightConfig.getSpuIds().contains(a.toString())){
                                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_PART_EXIST);
                            }
                        });
                    }
                }
            }if (fightConfig.getChoseType().equals(FightConfigEnum.PART_NOT_EXIST.value())){
                if (StringUtils.isNotBlank(fightConfig.getSpuIds())) {
                    //盲盒spuIdS
                    if (CollectionUtil.isNotEmpty(spuIds)){
                        spuIds.forEach(a->{
                            if (fightConfig.getSpuIds().contains(a.toString())){
                                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_PART_EXIST);
                            }
                        });
                    }
                }
            }
        }




        List<Long> skuIds;
        if (payReq.getFightId() == null){
            if (payReq.getFightRequest() == null){
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_REQUEST_IS_NULL);
            }
            if (payReq.getFightRequest().getFightPersonNum() != 4 && payReq.getFightRequest().getIsGroup() ){
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_ONLY_FOUR_SUPPORT_GROUP);
            }


            skuIds = payReq.getFightRequest().getFightBoxList().stream().map(FightBoxRequest::getSkuId).collect(Collectors.toList());
            extJosnMap.put("fightRequest",payReq.getFightRequest());
        }else {
            // 不可发起对战验证
            boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_PART_FIGHT, memberId);
            if (isAuthority) {
                throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_PART_FIGHT);
            }
            if (payReq.getSerialNum() == null){
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_SERIAL_NUM_IS_NULL);
            }
            SaasQueryBuilder queryFightBoxs = SaasQueryBuilder.where(Restrictions.eq("fightId",payReq.getFightId()));
            List<FightBox> fightBoxes = dao.queryList(queryFightBoxs, FightBox.class);
            skuIds = fightBoxes.stream().map(FightBox::getSkuId).collect(Collectors.toList());

            SaasQueryBuilder queryFightMember = SaasQueryBuilder.where(Restrictions.eq("fightId",payReq.getFightId()))
                    .and(Restrictions.eq("serialNum",payReq.getSerialNum()));
            FightMember fightMember = dao.query(queryFightMember,FightMember.class);
            if (fightMember != null) {
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_SERIAL_IS_OCCUPATION);
            }

            queryFightMember = SaasQueryBuilder.where(Restrictions.eq("fightId",payReq.getFightId()))
                    .and(Restrictions.eq("memberId",memberId));
            fightMember = dao.query(queryFightMember,FightMember.class);
            if (fightMember != null) {
                throw new BizCoreRuntimeException(MaErrorConstants.FIGHT_ONLY_BUY_ONE);
            }

            Fight fight = dao.queryById(payReq.getFightId(),Fight.class);
            if (payReq.getSerialNum() > fight.getFightPersonNum()){
                throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUM_LT_PERSON_NUM);
            }
            extJosnMap.put("fightId",payReq.getFightId().toString());
            extJosnMap.put("serialNum",payReq.getSerialNum());

        }

        extJosnMap.put("traceId", MDCTraceUtils.getTraceId());
        extJosnMap.put("lang", XcrmThreadContext.getLocale());


        Ssqb queryFightBoxs = Ssqb.create("com.lewei.eshop.ma.fight.queryFightBoxs")
                .setParam("skuIds",skuIds);
        List<FightBoxVo> fightBoxVos = dao.findForList(queryFightBoxs, FightBoxVo.class);
        Map<Long,Integer> skuMap = new HashMap<>();
        for (Long skuId : skuIds) {
            skuMap.merge(skuId,1,Integer::sum);
        }
        BigDecimal orderMoney = BigDecimal.ZERO;
        for (FightBoxVo fightBoxVo : fightBoxVos) {
            orderMoney = orderMoney.add(fightBoxVo.getPriceFee().multiply(BigDecimal.valueOf(skuMap.get(fightBoxVo.getSkuId()))));
            fightBoxVo.setNum(skuMap.get(fightBoxVo.getSkuId()));
        }



        if (orderMoney.compareTo(payReq.getOrderMoney()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }

        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setMemberId(memberId);
        order.setPaymentMethod(payReq.getPaymentMethod());
        order.setOrderMoney(orderMoney);
        order.setPaymentMoney(orderMoney);
        order.setOrderTitle(BizMessageSource.getInstance().getMessage("cem40020"));
        order.setOrderType(OrderEnum.S_OOT_FIGHT.value());
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setExtJson(JSONObject.toJSONString(extJosnMap));
        order.setCreated(now);
        order.setCreateBy(memberId);
        order.setPlatform(ssoMaAppCache.getPlatform());
        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_ESHOP.value());
        if (payReq.getPaymentMethod().equals(OrderPaymentTypeEnum.S_OPM_BALANCE.value())) {
            BigDecimal moneyRatio = chainConfigService.queryChainConfig().getMoneyRatio();
            BigDecimal coin = order.getOrderMoney().multiply(moneyRatio);
            order.setOrderBalance(coin);
        }

        dao.save(order);

        //订单ID
        Long orderId = order.getId();

        for (FightBoxVo fightBoxVo : fightBoxVos) {
            //生成订单明细
            OrderProduct orderProduct = new OrderProduct();
            orderProduct.setOrderId(orderId);
            orderProduct.setSpuId(fightBoxVo.getSpuId());
            orderProduct.setSkuId(fightBoxVo.getSkuId());
            orderProduct.setProductName(BizMessageSource.getInstance().getMessage("cem40020"));
            orderProduct.setMainImage(fightBoxVo.getMainImage());
            orderProduct.setCurrentUnitPrice(fightBoxVo.getPriceFee());
            orderProduct.setQuantity(fightBoxVo.getNum());
            orderProduct.setTotalPrice(fightBoxVo.getPriceFee());
            orderProduct.setCreated(now);
            orderProduct.setCreateBy(memberId);
            orderProduct.setPrimeCostFee(BigDecimal.ZERO);
            dao.save(orderProduct);
        }

        SaveOrderVO returnVo = new SaveOrderVO();

        if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(payReq.getPaymentMethod())) {
            returnVo.setOrderId(orderId);
            //调取微信支付
            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), openId, chainId, ssoMaAppCache.getAppId(),payReq.getPaymentMethod(),
                    callBackUrl);
            returnVo.setPayUrl(payUrl);
            this.saveOrderPayQueue(order.getId());
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payReq.getPaymentMethod())) {
            // 余额支付
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(orderMoney);
            // 买家
            balanceRequest.setMemberId(memberId);
            balanceRequest.setPlId(payReq.getFightId());
            // 查询卖家
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40021"));
            balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
            balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_FIGHT.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);

            // 扣款后续
            this.afterPay(order);
        } else {
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }
        return returnVo;
    }


    private void afterPay(Order order) {
        taskExecutor.execute(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderCode", order.getOrderSn());
            fightCallbackProcessor.onPayTradeSuccess(order, jsonObject);
        });
    }

    /**
     * 订单支付队列 延迟10分钟
     * @param orderId
     */
    private void saveOrderPayQueue(Long orderId){
        redisCacheProvider.getRedisTemplate().opsForZSet().add(MaCacheKeys.ORDER_PAY_QUEUE_KEY,orderId,System.currentTimeMillis() + 1000*60*10);
    }

}


