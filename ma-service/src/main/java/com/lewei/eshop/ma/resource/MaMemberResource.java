package com.lewei.eshop.ma.resource;

import cn.hutool.core.map.MapUtil;
import com.lewei.eshop.auth.annotation.ApiPolicy;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.member.MemberInfoVerifiedRequest;
import com.lewei.eshop.common.request.member.MemberTitleShowRequest;
import com.lewei.eshop.common.request.member.UpdateMemberInvitationRequest;
import com.lewei.eshop.common.request.member.bill.QueryMemberBillRequest;
import com.lewei.eshop.common.request.member.share.MaMemberShareBindiRequest;
import com.lewei.eshop.common.request.member.share.MemberShareBindiRequest;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.ma.SysConfig;
import com.lewei.eshop.ma.biz.IMaMemberBillService;
import com.lewei.eshop.ma.biz.IMaMemberService;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.paas.DistFeignClient;
import com.lewei.eshop.ma.message.request.*;
import com.lewei.eshop.ma.message.vo.MaSaveMemberVO;
import com.lewei.eshop.ma.utils.FileUploadUtil;
import com.xcrm.common.page.Pagination;
import com.xcrm.core.jersey.common.XcrmMediaType;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/1/9
 */
@Path("/members")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class MaMemberResource extends BaseAuthedResource {

    @Autowired
    private IMaMemberService maMemberService;
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private IMaMemberBillService memberBillService;
    @Autowired
    private DistFeignClient distFeignClient;

    /**
     * 通过openId获取会员信息
     *
     * @param openId
     * @return
     */
    @GET
    @Path("/{openId}")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberByOpenId(@NotBlank(message = "openId不能为空")
                                        @PathParam("openId") String openId) {
        return Response.ok(maMemberService.queryMemberByOpenId(openId)).build();
    }

    /**
     * 生成会员记录
     *
     * @param request
     * @return
     */
    @POST
    @ApiPolicy(isNoAuth = true)
    public Response saveMemberWithOnlyOpenId(@Valid SaveMemberRequest request) {
        Long memberId = maMemberService.saveMemberWithOnlyOpenId(request.getOpenId(),
                super.getChainId(), request.getTenantId(),super.getSSOMaApp().getAppId(),request.getInviterId(),null);
        return Response.status(Response.Status.CREATED).entity(memberId).build();
    }

    /**
     * 更新会员
     *
     * @param memberId
     * @param request
     * @return
     */
    @PUT
    @Path("/{memberId}")
    @ApiPolicy(isNoAuth = true)
    public Response updateMember(@PathParam("memberId") Long memberId, @Valid MaMemberUpdateRequest request) {
        if(StringUtils.isNotBlank(request.getHeadImage()) && request.getHeadImage().contains("https://wx.qlogo.cn")) {
            //微信自身头像，上传oss处理
            String newHeadImage = FileUploadUtil.uploadWxHeadImage(request.getHeadImage()
                    ,sysConfig.getEshopService() + "/api/ma-public/v1/file", super.getSSOMaApp().getAppId());
            request.setHeadImage(newHeadImage);
        }
        Member member = maMemberService.updateMember(memberId, request);
        if (member == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40146"));
        }
        return Response.status(Response.Status.CREATED).build();
    }




    /**
     * 更新会员(taobao)
     *
     * @param memberId
     * @param request
     * @return
     */
    @POST
    @Path("/update/{memberId}")
    @ApiPolicy(isNoAuth = true)
    public Response updateMemberPost(@PathParam("memberId") Long memberId, @Valid MaMemberUpdateRequest request) {
        if(StringUtils.isNotBlank(request.getHeadImage()) && request.getHeadImage().contains("https://wx.qlogo.cn")) {
            //微信自身头像，上传oss处理
            String newHeadImage = FileUploadUtil.uploadWxHeadImage(request.getHeadImage()
                    ,sysConfig.getEshopService() + "/api/ma-public/v1/file", super.getSSOMaApp().getAppId());
            request.setHeadImage(newHeadImage);
        }
        Member member = maMemberService.updateMember(memberId, request);
        if (member == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40146"));
        }
        return Response.status(Response.Status.CREATED).build();
    }
    /**
     * 更新会员关系冗余表
     * <AUTHOR>
     * @param request 请求实体
     * @return Response
     */
    @PUT
    @Path("/attr")
    @ApiPolicy(isNoAuth = true)
    public Response updateMember(@Valid UpdateTenantMemberAttrRequest request) {
      maMemberService.updateTenantMemberAttr(request.getTenantId(),request.getMemberId());
      return Response.status(Response.Status.CREATED).build();
    }
    /**
     * 查询会员等级列表
     * <AUTHOR>
     * @since 2020-4-20
     * @return Response
     */
    @GET
    @Path("/levels")
    public Response queryMemberLevels() {
        log.debug("MaMemberResource.queryMemberLevels(chainId = {})",super.getChainId());
        return Response.ok(maMemberService.queryMemberLevels()).build();
    }
    /**
     * 查询成长任务列表
     * <AUTHOR>
     * @since 2020-4-20
     * @return Response
     */
    @GET
    @Path("/grow/tasks")
    public Response queryGrowTaskList(@QueryParam("type") String type) {
        log.debug("MaMemberResource.queryGrowTaskList(chainId = {})",super.getChainId());
        return Response.ok(maMemberService.queryGrowTaskList(type)).build();
    }
    /**
     * 生成代理关系
     * @param relRequest relRequest
     */
    @PUT
    @Path("/proxy/rel")
    public Response generateProxyRel(@Valid GenerateProxyRelRequest relRequest) {
        maMemberService.generateProxyRel(relRequest.getOpenId(),relRequest.getParentCode(),super.getMemberId());
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 查询代理信息
     * @param openId  openId
     * @return MaMemberDetailVo
     */
    @GET
    @Path("/{openId}/proxy")
    @ApiPolicy(isNoAuth = true)
    public Response queryProxyByOpenId(@NotBlank(message = "openId不能为空")
                                        @PathParam("openId") String openId) {
        return Response.ok(maMemberService.queryProxyByOpenId(openId)).build();
    }

    /**
     * 查询一级粉丝列表
     * @param searchKey searchKey
     * @param request pageRequest
     * @return Pagination
     */
    @GET
    @Path("/descendant")
    public Response queryMembers(@QueryParam("searchKey") String searchKey,
                                 @QueryParam("level") Integer level,
                                 @QueryParam("userType") String userType,
                                 @QueryParam("order") String order,
                                 @QueryParam("orderType") String orderType,
                                 @QueryParam("st") Long st,
                                 @QueryParam("et") Long et,
                                 @Valid @BeanParam PageRequest request) {
        log.debug("MaMemberResource.queryMembers(searchKey = {},request = {})",searchKey,request);
        return Response.ok(maMemberService.queryMembers(super.getUserCode(),level,searchKey,userType,order,orderType, st, et,request)).build();
    }

    /**
     * 查询客户信息
     * <AUTHOR>
     * @since 2020-06-23
     * @param userCode userCode
     */
    @GET
    @Path("/simple/by/userCode")
    public Response queryMemberByUserCode(@NotEmpty(message = "userCode is required") @QueryParam("userCode") String userCode) {
        log.debug("MaMemberResource.queryMemberByUserCode(userCode = {})",userCode);
        return Response.ok(maMemberService.queryMemberByUserCode(userCode)).build();
    }

    /**
     * 查询客户信息
     * <AUTHOR>
     * @since 2020-06-23
     * @param memberId memberId
     */
    @GET
    @Path("/simple")
    public Response queryMemberByMemberId(@NotNull(message = "memberId is required") @QueryParam("memberId") Long memberId) {
        log.debug("MaMemberResource.queryMemberByUserCode(memberId = {})",memberId);
        return Response.ok(maMemberService.queryMemberSimpleVo(memberId)).build();
    }

    /**
     * 查询会抽奖金额
     * @param st
     * @param et
     * @return
     */
    @GET
    @Path("/buy/box/money")
    public Response queryMemberBuyBoxMoneyVO(@NotNull(message = "memberId is required") @QueryParam("memberId") Long memberId,
                                             @QueryParam("st") Long st,
                                             @QueryParam("et") Long et) {
        log.debug("MaMemberResource.queryMemberBuyBoxMoneyVO(memberId = {})",memberId);
        return Response.ok(maMemberService.queryMemberBuyBoxMoneyVO(memberId,st,et)).build();
    }

    /**
     * 更新活跃时间
     * @param memberId
     */
    @PUT
    @Path("/{memberId}/active")
    public Response updateActiveTime(@NotNull  @PathParam("memberId") Long memberId) {
        maMemberService.updateActiveTime(memberId);
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 粉丝活跃度排行
     * @param level 代理关系 1 直属 2 非直属 null = 1U2
     * @param order 排序类型
     * @param request 分页实体
     * @return Pagination
     */
    @GET
    @Path("/active/rank")
    public Response queryFansActiveRank(@QueryParam("level") Integer level,
                                     @NotEmpty @QueryParam("order") String order,
                                     @QueryParam("st") Long st,
                                     @QueryParam("et") Long et,
                                     @Valid @BeanParam PageRequest request) {

        return Response.ok(maMemberService.queryFansActiveRank(super.getUserCode(),level,order,st,et,request)).build();
    }

    /**
     * 添加邀请人数
     * @param userCode 邀请人code
     */
    @PUT
    @Path("/invite/num")
    public Response updateActiveTime(@NotEmpty(message = "userCode is required") @QueryParam("userCode") String userCode) {
        maMemberService.saveInviteNewLog(userCode);
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 粉丝邀请人数排行
     * @param level 代理关系 1 直属 2 非直属 null = 1U2
     * @param order 排序类型
     * @param st 开始时间
     * @param et 结束时间
     * @param request 分页实体
     * @return Pagination
     */
    @GET
    @Path("/invite/rank")
    public Response updateActiveTime(@QueryParam("level") Integer level,
                                     @QueryParam("order") String order,
                                     @QueryParam("st") Long st,
                                     @QueryParam("et") Long et,
                                     @Valid @BeanParam PageRequest request) {
        return Response.ok(maMemberService.queryFansInviteRank(super.getUserCode(),level,order,st,et,request)).build();
    }

    /**
     * 收益排行
     * <AUTHOR>
     */
    @GET
    @Path("/profit/rank")
    public Response queryProfitRank(@QueryParam("level") Integer level,
                                     @QueryParam("order") String order,
                                     @QueryParam("st") Long st,
                                     @QueryParam("et") Long et,
                                     @Valid @BeanParam PageRequest request) {
        return Response.ok(maMemberService.queryProfitRank(super.getUserCode(),level,order,st,et,request)).build();
    }



    /**
     * 提交代理申请
     */
    @PUT
    @Path("/proxy/status")
    public Response updateMemberProxyStatus() {
        maMemberService.updateMemberProxyStatus(super.getMemberId(),super.getOpenId());
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 绑定邀请人
     * @param inviterCode
     */
    @PUT
    @Path("/binding")
    public Response updateMemberProxyStatus(@NotEmpty @QueryParam("inviterCode") String inviterCode) {
        maMemberService.bindingMemberInviter(super.getMemberId(),inviterCode,false);
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 查询客户绑定配置
     */
    @GET
    @Path("/binding")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberBindingConfig() {
        log.debug("MemberResource.queryMemberBindingConfig()");
        return Response.ok(maMemberService.queryMemberBindingConfig()).build();
    }


    /**
     * 查询入会配置
     * @return 入会配置
     */
    @GET
    @Path("/ship/config")
    @ApiPolicy(isNoAuth = true)
    public Response queryLevelConfig() {
        log.debug("MemberLevelResource.queryLevelConfig()");
        return Response.ok( memberFeignClient.queryLevelConfig()).build();
    }
    /**
     * 领取入会赠送
     * @param tenantId tenantId
     */
    @POST
    @Path("/receive/ship")
    public Response receiveMemberShipGiven(@NotNull(message = "tenantId is required")@QueryParam("tenantId") Long tenantId) {
        log.debug("MemberResource.receiveMemberShipGiven()");
        maMemberService.receiveMemberShipGiven(super.getMemberId(),tenantId,super.getChainId());
        return Response.status(Response.Status.CREATED).build();
    }


    /**
     * 查询会员积分记录
     * @param request 分页实体
     * @return Pagination
     */
    @GET
    @Path("/score/log")
    public Response queryMemberScoreLog(@Valid @BeanParam PageRequest request,
                                        @QueryParam("task") String task) {
        log.debug("MemberResource.queryMemberScoreLog()");
        Pagination pagination = maMemberService.queryMemberScoreLog(super.getMemberId(),task,request);
        return Response.ok(pagination).build();
    }

    /**
     * 通过黑市openId查询对应会员
     * @param bmOpenId 黑市openId
     * @return
     */
    @GET
    @Path("/black/market/open")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberByBmOpenId(@NotNull(message = "bmOpenId is required")@QueryParam("bmOpenId") String bmOpenId){
        log.debug("MemberResource.queryMemberByBmOpenId(bmOpenId={})",bmOpenId);
        return Response.ok(maMemberService.queryMemberByBmOpenId(bmOpenId)).build();
    }

    /**
     * 查询会员账单
     * @param request 查询求情实体
     * @return Pagination
     */
    @GET
    @Path("/bill")
    public Response queryMemberBillPage(@Valid @BeanParam QueryMemberBillRequest request){
        log.debug("MemberResource.queryMemberBillPage(request={})",request);
        return Response.ok(memberBillService.queryMemberBillPage(request,super.getMemberId())).build();
    }

    /**
     * 查询会员积分
     * @return BigDecimal 积分
     */
    @GET
    @Path("/score")
    public Response queryMemberScore(){
        log.debug("MemberResource.queryMemberScore()");
        return Response.ok(memberBillService.queryMemberScore(super.getMemberId())).build();
    }
    /**
     * 查询会员积分2
     * @return BigDecimal 积分
     */
    @GET
    @Path("/fraction")
    public Response queryMemberFraction(){
        log.debug("MemberResource.queryMemberFraction()");
        return Response.ok(memberBillService.queryMemberFraction(super.getMemberId())).build();
    }


    /**
     * 通过openId获取会员信息
     *
     * @param openId
     * @return
     */
    @GET
    @Path("/v2/{openId}")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberByOpenIdV2(@NotBlank(message = "openId不能为空")
                                        @PathParam("openId") String openId) {
        return Response.ok(maMemberService.queryMemberByOpenIdV2(openId)).build();
    }

    /**
     * 通过openId获取会员信息
     *
     * @param memberId
     * @return
     */
    @GET
    @Path("/v2/media/{memberId}")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberForMedia(@NotBlank(message = "memberId不能为空")
                                          @PathParam("memberId") String memberId) {
        String appId = this.getSSOMaApp().getAppId();
        return Response.ok(maMemberService.queryMemberForMedia(memberId, appId)).build();
    }

    /**
     * 通过openId获取会员信息
     *
     * @param memberCode
     * @return
     */
    @GET
    @Path("/memberCode/{memberCode}")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberByMemberCode(@NotBlank(message = "memberCode不能为空")
                                          @PathParam("memberCode") String memberCode) {
        return Response.ok(maMemberService.queryMemberByMemberCode(memberCode)).build();
    }


    /**
     * 通过openId获取封禁状态
     *
     * @param openId openId
     * @return Response
     */
    @GET
    @Path("/authority/{openId}")
    @ApiPolicy(isNoAuth = true)
    public Response queryAuthorityByOpenId(@NotBlank(message = "openId不能为空")
                                          @PathParam("openId") String openId) {
        return Response.ok(maMemberService.queryAuthorityByOpenId(openId)).build();
    }

    /**
     * 生成会员记录
     *
     * @param request
     * @return
     */
    @POST
    @Path("/v2")
    @ApiPolicy(isNoAuth = true)
    public Response saveMemberWithOnlyOpenIdV2(@Valid SaveMemberV2Request request) {
        MaSaveMemberVO  member = maMemberService.saveMemberV2(request,super.getSSOMaApp().getAppId(),super.getSSOMaApp().getPlatform());
        return Response.status(Response.Status.CREATED).entity(member).build();
    }


    /**
     * 微信登出
     */

    @DELETE
    @Path("/wechat/sign/out")
    public Response weChatSignOut() {
        maMemberService.weChatSignOut(super.getSSOMaApp().getAppId(),super.getMemberId(), super.getOpenId());
        return Response.noContent().build();
    }

    /**
     * 微信登出
     */
    @POST
    @Path("/delete/wechat/sign/out")
    public Response deleteWeChatSignOut() {
        maMemberService.weChatSignOut(super.getSSOMaApp().getAppId(),super.getMemberId(), super.getOpenId());
        return Response.noContent().build();
    }


    /**
     * 查询会员积分记录
     * @param request 分页实体
     * @return Pagination
     */
    @GET
    @Path("/fraction/log")
    @SuppressWarnings("unchecked")
    public Response queryMemberFractionLog(@Valid @BeanParam PageRequest request) {
        log.debug("MemberResource.queryMemberScoreLog()");
        Pagination pagination = memberFeignClient.queryMemberFractionLog(super.getMemberId(),request.getPageNo(),request.getPageSize());
        if(pagination != null) {
            List<Map<String, Object>> items = (List<Map<String, Object>>)pagination.getList();
            for(Map<String, Object> item : items) {
                if(item.get("created") != null) {
                    item.put("created", new Timestamp(MapUtil.getLong(item, "created")));
                }
            }
        }
        return Response.ok(pagination).build();
    }


    /**
     * 生成会员记录
     *
     * @param request
     * @return
     */
    @POST
    @Path("/dist/ref")
    @ApiPolicy(isNoAuth = true)
    public Response saveMaDistRef(@Valid MaDistRefRequest request) {
        log.debug("MemberResource.saveMaDistRef(request = {})",request);
        maMemberService.saveMaDistRef(request);
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 绑定邀请人
     *
     * @param request
     * @return
     */
    @PUT
    @Path("/update/invitation")
//    @ApiPolicy(isNoAuth = true)
    public Response updateInvitation(@Valid UpdateMemberInvitationRequest request) {
        log.debug("MaMemberResource.updateInvitation(request = {})",request);
        maMemberService.updateInvitation(super.getMemberId(),request);
        Map<String,String> map = new HashMap<>(1);
        map.put("result", "success");
        return Response.ok().entity(map).build();
    }

    /**
     * 查询会员头衔列表
     * <AUTHOR>
     */
    @GET
    @Path("/title/list")
    public Response queryMemberTitleList() {
        return Response.ok(maMemberService.queryMemberTitleList(super.getMemberId())).build();
    }

    /**
     * 查询会员头衔详情
     *
     * <AUTHOR>
     */
    @GET
    @Path("/title/detail/{titleId}")
    public Response queryMemberTitleDetail(@NotNull(message = "titleId不能为空") @PathParam("titleId") Long titleId) {
        return Response.ok(maMemberService.queryMemberTitleDetail(titleId, super.getMemberId())).build();
    }

    /**
     * 查询会员头衔列表(已获得)
     * <AUTHOR>
     */
    @GET
    @Path("/title/have/list")
    public Response queryMemberTitleHaveList() {
        return Response.ok(maMemberService.queryMemberTitleHaveList(super.getMemberId())).build();
    }


    /**
     * 更新会员显示头衔
     */
    @PUT
    @Path("/update/title")
    public Response updateMemberShowTitle(@Valid MemberTitleShowRequest request) {
        maMemberService.updateMemberShowTitle(request,super.getMemberId());
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 会员真实身份认证
     */
    @PUT
    @Path("/identity/verified")
    public Response memberInfoVerified(@Valid MemberInfoVerifiedRequest request) {
        maMemberService.memberInfoVerified(request,super.getMemberId());
        return Response.status(Response.Status.CREATED).build();
    }


    /**
     * 会员绑定
     */
    @POST
    @Path("/share/binding")
    public Response memberShareBinding(@Valid MaMemberShareBindiRequest request) {
        log.debug("MemberResource.memberShareBinding(request = {})",request);
        maMemberService.memberShareBinding(request,super.getMemberId());
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 判断是否是新会员
     */
    @GET
    @Path("/tags/new/member/{memberId}")
    public Response queryIsNewMember(@NotNull(message = "memberId is required") @PathParam("memberId") Long memberId) {
        log.debug("MemberResource.queryIsNewMember(memberId = {})", memberId);
        Boolean isNewMember = memberFeignClient.queryIsNewMember(memberId);
        return Response.ok(isNewMember).build();
    }

    /**
     * 查询钱包汇总金额
     * <AUTHOR>
     * @param userCode          用户编码
     * @param st                开始时间
     * @param et                结束时间
     * @return                  累计金额
     */
    @GET
    @Path(value = "/wallet/summary-data")
    public Response queryWalletSummaryData(@NotNull(message = "userCode is required")@QueryParam("userCode") String userCode,
                                           @QueryParam("st") Long st,
                                           @QueryParam("et") Long et){
        log.debug("MemberResource.queryWalletSummaryData(userCode = {}, st = {}, et = {})", userCode, st, et);
        return Response.ok(distFeignClient.queryWalletSummaryData(userCode, st,et)).build();
    }

    /**
     * 查询记录总结
     *
     * @param st 开始时间
     * @param et 结束时间
     * @return 累计金额
     * <AUTHOR>
     */
    @GET
    @Path(value = "/record/summary-data")
    public Response queryRecordSummaryData(@QueryParam("userCode") String userCode,
                                           @QueryParam("memberId") Long memberId,
                                           @QueryParam("st") Long st,
                                           @QueryParam("et") Long et) {
        log.debug("MemberResource.queryRecordSummaryData(memberId = {}, userCode = {}, st = {}, et = {})", userCode, memberId, st, et);
        return Response.ok(maMemberService.queryRecordSummaryData(userCode, memberId, st, et)).build();
    }

    /**
     * 通过手机号查询会员
     * @param mobile 手机号
     * @return Response
     */
    @GET
    @Path("/search/member/{mobile}")
    @ApiPolicy(isNoAuth = true)
    public Response queryMemberByMobile(@NotEmpty(message = "会员手机号不能为空") @PathParam("mobile") String  mobile)  {
        return Response.ok(maMemberService.queryMemberByMobileH5(mobile)).build();
    }


    @PUT
    @Path("/last/login/time")
    public Response updateMemberLastLoginTime()  {
        Long memberId = super.getMemberId();
        log.info("MemberResource.updateMemberLastLoginTime(memberId = {}, )",memberId);
        maMemberService.updateMemberLastLoginTime(memberId);
        return Response.status(Response.Status.CREATED).build();
    }
}
