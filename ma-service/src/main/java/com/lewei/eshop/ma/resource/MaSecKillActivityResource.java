package com.lewei.eshop.ma.resource;

import com.lewei.eshop.ma.biz.IMaSecKillActivityService;
import com.lewei.eshop.ma.message.request.QueryMaSecKillActivityRequest;
import com.lewei.eshop.ma.message.vo.SecKillActivityVo;
import com.xcrm.common.page.Pagination;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
* 满减活动资源
* <AUTHOR>
* @date 2020/12/22
**/
@Path("/sec/kill/activity")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class MaSecKillActivityResource extends BaseAuthedResource {

    @Autowired
    private IMaSecKillActivityService secKillActivityService;

    /**
     * 查询秒杀活动列表
     * @param request 请求实体
     * @return Pagination
     */
    @GET
    public Response querySecKillActivityPage(@Valid @BeanParam QueryMaSecKillActivityRequest request) {
        log.debug("MaSecKillActivityResource.querySecKillActivityPage(request = {})", request);
        Pagination pagination= secKillActivityService.querySecKillActivityPage(request);
        return Response.ok(pagination).build();
    }

    /**
     * 查询秒杀活动
     * @param activityId
     * @return
     */
    @GET
    @Path("/{activityId}")
    public Response querySecKillActivity(@NotNull @PathParam("activityId") Long activityId) {
        log.debug("MaSecKillActivityResource.querySecKillActivity(activityId = {})", activityId);
        SecKillActivityVo activityVo= secKillActivityService.querySecKillActivity(activityId);
        return Response.ok(activityVo).build();
    }


    /**
     * 查询秒杀活动
     * @param tenantId 店铺id
     * @return   List<Date>
     */
    @GET
    @Path("/times")
    public Response queryTodaySecKillTime(@NotNull @QueryParam("tenantId") Long tenantId) {
        log.debug("MaSecKillActivityResource.queryTodaySecKillTime(tenantId = {})", tenantId);
        List<Date> dates= secKillActivityService.queryTodaySecKillTime(tenantId);
        return Response.ok(Collections.singletonMap("dates",dates)).build();
    }
}