package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.OrderUtil;
import com.lewei.eshop.common.data.BoxConfigEnum;
import com.lewei.eshop.common.data.TimeTypeEnum;
import com.lewei.eshop.common.data.coupon.ActivityStatusEnum;
import com.lewei.eshop.common.data.coupon.LotteryNumTypeEnum;
import com.lewei.eshop.common.data.coupon.MemberCouponStatusEnum;
import com.lewei.eshop.common.data.member.MemberTypeEnum;
import com.lewei.eshop.common.data.member.ScoreTaskEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.data.product.ProductEnum;
import com.lewei.eshop.common.request.member.MemberBalancePayRequest;
import com.lewei.eshop.common.request.taoBao.MemberTbTicketRequest;
import com.lewei.eshop.common.request.treasure.MemberBoxTicketReq;
import com.lewei.eshop.common.vo.activity.seckill.SecKillActivityDetailVo;
import com.lewei.eshop.common.vo.commodityLimitation.CommodityLimitationConfigVo;
import com.lewei.eshop.common.vo.crowd.CrowdFundingVO;
import com.lewei.eshop.common.vo.ma.MaMemberDetailVo;
import com.lewei.eshop.common.vo.ma.SelfPickPointVo;
import com.lewei.eshop.common.vo.ma.reduction.CalculateReductionFeeVo;
import com.lewei.eshop.common.vo.member.ConsumeFractionRequest;
import com.lewei.eshop.common.vo.member.ConsumeScoreRequest;
import com.lewei.eshop.common.vo.member.MemberBoxTicketVo;
import com.lewei.eshop.common.vo.member.OrderShippedVo;
import com.lewei.eshop.entity.app.ApplicationConfig;
import com.lewei.eshop.entity.app.coupon.MemberCoupon;
import com.lewei.eshop.entity.app.score.FractionPayConfig;
import com.lewei.eshop.entity.app.score.ScorePayConfig;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.crowd.CrowdFunding;
import com.lewei.eshop.entity.crowd.CrowdOrderRef;
import com.lewei.eshop.entity.crowd.types.CrowdFundingStatusEnum;
import com.lewei.eshop.entity.member.MemberBoxStage;
import com.lewei.eshop.entity.member.MemberLevel;
import com.lewei.eshop.entity.member.MemberRecord;
import com.lewei.eshop.entity.member.MemberService;
import com.lewei.eshop.entity.member.types.*;
import com.lewei.eshop.entity.newcomer.NewcomerActivityBuyRecord;
import com.lewei.eshop.entity.newcomer.NewcomerActivityConfigRule;
import com.lewei.eshop.entity.order.*;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.product.*;
import com.lewei.eshop.entity.product.box.types.BoxSequenceTypeEnum;
import com.lewei.eshop.entity.product.box.types.ProductBoxPrizeModeTypeEnum;
import com.lewei.eshop.entity.product.box.types.ProductBoxTypeEnum;
import com.lewei.eshop.entity.product.config.BoxRewardConfig;
import com.lewei.eshop.entity.product.types.BoxItemSaleStatusEnum;
import com.lewei.eshop.entity.reduction.ReductionGift;
import com.lewei.eshop.entity.sso.types.MaPlatformEnum;
import com.lewei.eshop.ma.Constants;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.SysConfig;
import com.lewei.eshop.ma.biz.*;
import com.lewei.eshop.ma.client.AppFeignClient;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.client.paas.DistFeignClient;
import com.lewei.eshop.ma.client.paas.request.CalcProfitAmountRequest;
import com.lewei.eshop.ma.client.paas.response.CalcProfitAmountResponse;
import com.lewei.eshop.ma.client.paas.response.UserResponse;
import com.lewei.eshop.ma.event.entity.BuyBoxEvent;
import com.lewei.eshop.ma.message.request.*;
import com.lewei.eshop.ma.message.vo.*;
import com.lewei.eshop.ma.pay.*;
import com.lewei.eshop.ma.sso.MemberCache;
import com.lewei.global.IGlobalHandler;
import com.lewei.log.trace.MDCTraceUtils;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.DateZonetimeUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.StringUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/1/11
 */
@Slf4j
@Service
@Transactional
public class MaOrderServiceImpl implements IMaOrderService {
    private final Map<String, String> orderTypeMap = new HashMap<>();

    {
        orderTypeMap.put("S_ST_PRODUCT", "S_OOT_PRODUCT");
        orderTypeMap.put("S_ST_SERVICE", "S_OOT_SERVICE");
        orderTypeMap.put("S_ST_CARD", "S_OOT_CARD");
        orderTypeMap.put("S_ST_BOX", "S_OOT_BOX");
    }

    private final String callBackUrl = "/api/ma/pay/wx/callback";

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IMaProductService maProductService;

    @Autowired
    private IMaMemberService memberService;
    @Autowired
    private FreightFeeProcessor freightFeeProcessor;
    @Autowired
    private SysConfig sysConfig;

    @Autowired
    private IMaOrderRefundService maOrderRefundService;

    @Autowired
    private MemberCouponProcessor memberCouponProcessor;
    @Autowired
    private IMaMemberCouponService maMemberCouponService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private DistFeignClient feignClient;
    @Autowired
    private ReductionProcessor reductionProcessor;
    @Autowired
    private ScoreDeductionProcessor scoreDeductionProcessor;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private PayCallbackProcessorV2 payCallbackProcessorV2;
    @Autowired
    private AppFeignClient appFeignClient;
    @Autowired
    private IMaSecKillActivityService secKillActivityService;
    @Autowired
    private IMaProductBoxService productBoxService;
    @Autowired
    private IMaShopService shopService;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private RedisCacheProvider redisCacheProvider;
    @Autowired
    private IMaShoppingCartService shoppingCartService;
    @Autowired
    private IMaPayDeductService maPayDeductService;
    @Autowired
    private IGlobalHandler globalHandler;
    @Autowired
    private IConsumerReminderService iConsumerReminderService;

    @Override
    public Pagination queryOrders(QueryOrdersRequest request, Long memberId) {

        List<String> statusList = new ArrayList<>();
        if (StringUtils.isNotEmpty(request.getMultiStatus())){
            statusList = Arrays.asList(request.getMultiStatus().split(","));
        }

        List<String> orderTypeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(request.getOrderTypes())){
            orderTypeList = Arrays.asList(request.getOrderTypes().split(","));
        }

        List<String> orderTradeTypeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(request.getOrderTradeTypes())){
            orderTradeTypeList = Arrays.asList(request.getOrderTradeTypes().split(","));
        }


        Ssqb query = Ssqb.create("com.lewei.eshop.ma.queryOrders")
                .setParam("status", request.getStatus())
                .setParam("statusList", statusList)
                .setParam("paymentMethod", request.getPaymentMethod())
                .setParam("orderTradeType", request.getOrderTradeType())
                .setParam("memberId", memberId)
                .setParam("tenantId", request.getTenantId())
                .setParam("orderType", request.getOrderType())
                .setParam("tradeSubType", request.getTradeSubType())
                .setParam("neTradeSubType", request.getNeTradeSubType())
                .setParam("orderTypeList", orderTypeList)
                .setParam("orderTradeTypeList", orderTradeTypeList)
                .setParam("pageNo", request.getPageNo()).setParam("pageSize", request.getPageSize());
        Pagination pagination =  dao.findForPage(query);
        List<OrderListVO> orderListVOS = (List<OrderListVO>) pagination.getList();
        if (ListUtil.isNotEmpty(orderListVOS)){
            for (OrderListVO  order : orderListVOS) {
                String getOrderProductStr = order.getOrderProductStr();
                if (StringUtils.isNotEmpty(getOrderProductStr)){
                    String[] orderProducts = getOrderProductStr.split("##");
                    for (String orderProduct : orderProducts) {
                        List<String> productInfo =   Arrays.asList(orderProduct.split("@@"));
                        OrderListVO.OrderProductVO  orderProductVO = new OrderListVO.OrderProductVO();
                        orderProductVO.setProductName(productInfo.get(0));
                        orderProductVO.setMainImage(productInfo.get(1));
                        orderProductVO.setQuantity(Integer.valueOf(productInfo.get(2)));
                        orderProductVO.setCurrentUnitPrice(new BigDecimal(productInfo.get(3)));
                        orderProductVO.setExtJson(productInfo.get(4));
                        order.getOrderProducts().add(orderProductVO);
                    }
                }
            }
        }

        return pagination;
    }

    @Override
    public OrderDetailVO queryOrderDetail(Long orderId) {
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.queryOrderDetail")
                .setParam("orderId", orderId);
        return dao.findForObj(query, OrderDetailVO.class);
    }

    @Override
    public OrderShippedVo queryOrderDetailShip(Long orderId) {
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.orderProductShipped")
                .setParam("orderId", orderId);
        return dao.findForObj(query, OrderShippedVo.class);
    }

    @Override
    public List<PrePayVO> prePay(String cartIds, Long memberId, Long chainId, Long tenantId, Long levelId, String userCode) {
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.prePay")
                .setParam("cartIds", cartIds)
                .setParam("memberId", memberId)
                .setParam("chainId", chainId)
                .setParam("tenantId", tenantId);
        List<PrePayVO> prePayVOList = dao.findForList(query, PrePayVO.class);
        if (ListUtil.isNotEmpty(prePayVOList) && levelId != null) {
            //代理不计算会员价，直接计算自购返佣价格
            if (StringUtils.isNotEmpty(userCode)) {
                UserResponse user = feignClient.queryUserByCode(userCode);
                if (user != null) {
                    if (MemberTypeEnum.S_MT_PROXY.value().equals(user.getUserType())) {
                        this.handleCalcProfitAmount(prePayVOList, userCode);
                        return prePayVOList;
                    }
                }
            }
        }
        return prePayVOList;
    }

    @Override
    public PrePayVOV2 prePayV2(String cartIds, Long memberId, Long chainId, Long tenantId, Long levelId, String userCode) {
        List<PrePayVO> prePayVOList = prePay(cartIds, memberId, chainId, tenantId, levelId, userCode);
        PrePayVOV2 prePayV2 = null;
        if (ListUtil.isNotEmpty(prePayVOList)) {
            prePayV2 = new PrePayVOV2();
            PrePayVO prePayVO = prePayVOList.stream().
                    max(Comparator.comparing(PrePayVO::getFreightFee, Comparator.nullsLast(BigDecimal::compareTo))).get();
            prePayV2.setSubTotalFreightFee(prePayVO.getFreightFee());
            prePayV2.setPrePayVOList(prePayVOList);
            MemberLevel memberLevel = memberService.queryMemberLevel(levelId);
            if (memberLevel != null) {
                prePayV2.setIsFreeFreightFee(memberLevel.getIsFreeFreightFee());
                if (memberLevel.getIsFreeFreightFee()) {
                    prePayV2.setSubTotalFreightFee(BigDecimal.ZERO);
                }
            }
        }
        return prePayV2;
    }

    @Override
    public PrePayVOV2 prePayV3(PrePayRequest request, Long memberId, Long chainId, Long levelId, String userCode) {
        // 不可进行消费
        boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_BUY_PRODUCT, memberId);
        if (isAuthority) {
            throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_BUY_PRODUCT);
        }
        List<PrePayVO> prePayVOList = prePay(request.getCartIds(), memberId, chainId, request.getTenantId(), levelId, userCode);
        PrePayVOV2 prePayV2 = null;
        if (ListUtil.isNotEmpty(prePayVOList)) {
            //购买次数限制
//            CommodityLimitationConfigVo limitationConfigVo = appFeignClient.queryCommodityLimitationConfig();
//            if (null != limitationConfigVo && BooleanUtils.isTrue(limitationConfigVo.getIsOpen())) {
//                //配置每人每天可以购买多少次
//                Integer configTotal = limitationConfigVo.getNumberPieces();
//                Integer memberTotal = 0;
//                List<Long> spuIds = new ArrayList<>();
//                //当前配置所有限制购买商品的集合
//                List<CommodityLimitationConfigVo.CommodityLimitationConfigSpuVo> spuVo = limitationConfigVo.getCommodityLimitationConfigSpuVos();
//                if (CollectionUtils.isNotEmpty(spuVo)){
//                    spuIds = spuVo.stream().map(p -> p.getSpuId()).collect(Collectors.toList());
//                    for (PrePayVO vo: prePayVOList){
//                        ProductSpu spu = dao.queryById(vo.getSpuId(), ProductSpu.class);
//                        if (CollectionUtils.isNotEmpty(spuIds) && null != spu.getParentId() && spuIds.contains(spu.getParentId())){
//                            log.info("当前spuId是" + spu.getParentId());
//                            for (CommodityLimitationConfigVo.CommodityLimitationConfigSpuVo a :spuVo){
//                                Object num = redisCacheProvider.getRedisTemplate().opsForHash().get(MaCacheKeys.MEMBER_PRODUCT_QUEUE_KEY,
//                                        memberId.toString() + a.getSpuId().toString());
//                                log.info("当前用户编号"+memberId+"spuId是"+a.getSpuId() +"当前商品购买次数是"+num);
//                                if (num != null){
//                                    Integer nums  = (Integer)num;
//                                    memberTotal = memberTotal + nums;
//                                    log.info("当前用户购买次数是"+memberTotal);
//                                }
//                            }
//                            memberTotal = memberTotal + vo.getNum();
//                            if (memberTotal > configTotal){
//                                throw new BizCoreRuntimeException(MaErrorConstants.COMMODITY_LIMITATION_ERROR,configTotal);
//                            }
//                        }
//                    }
//
//                }
//            }
            //购买次数限制
            Map<Long,Integer> spuMap = new ConcurrentHashMap<>();
            prePayVOList.forEach(p -> {
                spuMap.put(p.getParentSpuId(),p.getNum());
            });

            this.checkDayLimit(spuMap,memberId);

            prePayV2 = new PrePayVOV2();
            prePayV2.setPrePayVOList(prePayVOList);
            BigDecimal subTotalProductFee = BigDecimal.ZERO;
            BigDecimal subTotalOriginalPrice = BigDecimal.ZERO;
            for (PrePayVO prePayVO : prePayVOList) {
                subTotalProductFee = subTotalProductFee.add(prePayVO.getPriceFee().multiply(BigDecimal.valueOf(prePayVO.getNum())));
                subTotalOriginalPrice = subTotalOriginalPrice.add(prePayVO.getOriginalPrice().multiply(BigDecimal.valueOf(prePayVO.getNum())));
            }
            prePayV2.setSubTotalProductFee(subTotalProductFee);
            prePayV2.setSubTotalOriginalPrice(subTotalOriginalPrice);
            //计算优惠券优惠金额
            if (request.getMemberCouponId() != null) {
                memberCouponProcessor.calculatePreferentialFee(request.getMemberCouponId(), prePayV2, request.getTenantId());
            }
            //计算满减优惠
           // reductionProcessor.calculateReductionFee(prePayV2, request.getTenantId());
            //提取方式为物流需要计算运费
//            if (Objects.equals(ProductEnum.S_LM_LOGISTICS.value(), request.getLogisticsMode())) {
//                freightFeeProcessor.calculateFreightFee(prePayV2, request.getLogisticsMode(), request.getAddress(), levelId);
//            }
            //积分抵扣
         //   scoreDeductionProcessor.calculateDeductionScore(prePayV2, request.getPayScore());

            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            prePayV2.setSubTotalBalance(prePayV2.getSubTotalFee().multiply(chainConfig.getMoneyRatio()));
        }


        return prePayV2;
    }

    @Override
    public PrePayBuyNowVO prePayBuyNowV3(PrePayBuyNowRequest request, Long levelId, String userCode, Long memberId ) {
        if (request.getBoxItemId() == null) {
            // 不可进行消费
            boolean isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_BUY_PRODUCT, memberId);
            if (isAuthority) {
                throw new BizCoreRuntimeException(MaErrorConstants.DO_NOT_BUY_PRODUCT);
            }
        }else {
            //封禁校验
            boolean flag =  memberService.checkIsAuthority(MemberAuthorityEnum.NO_REWARD,memberId);
            if (flag){
                throw new BizCoreRuntimeException(MaErrorConstants.USER_MUST_NOT_REWARD);
            }
        }
        PrePayBuyNowVO prePayBuyNowVO = prePayBuyNow(request , levelId , userCode ,request.getTenantId(),memberId);


        if (prePayBuyNowVO != null) {
            //商品价格小计
            prePayBuyNowVO.setNum(request.getNum());
            BigDecimal totalProductFee = prePayBuyNowVO.getPriceFee().multiply(BigDecimal.valueOf(request.getNum()));
            BigDecimal subTotalOriginalPrice = prePayBuyNowVO.getOriginalPrice().multiply(BigDecimal.valueOf(request.getNum()));
            prePayBuyNowVO.setSubTotalProductFee(totalProductFee);
            prePayBuyNowVO.setSubTotalOriginalPrice(subTotalOriginalPrice);


            PrePayVOV2 prePayVOV2 = new PrePayVOV2();
            PrePayVO prePayVO = new PrePayVO();
            prePayVOV2.setSubTotalProductFee(prePayBuyNowVO.getSubTotalProductFee());
            BeanUtils.copyProperties(prePayBuyNowVO, prePayVO);
            prePayVOV2.getPrePayVOList().add(prePayVO);

            //邮费处理
//            freightFeeProcessor.calculateFreightFee(prePayVOV2, request.getLogisticsMode(), request.getAddress(), levelId);
//            prePayBuyNowVO.setFreightFee(prePayVOV2.getSubTotalFreightFee());
//            prePayBuyNowVO.setIsFreeFreightFee(prePayVOV2.getIsFreeFreightFee());
            //秒杀不参与其他优惠
            if (request.getSecKillId() != null) {
                return prePayBuyNowVO;
            }
            //邮费处理
            if(null != request.getPlId() && request.getPlId() > 0 && null != request.getAddress()){
                freightFeeProcessor.calculateFreightFee(prePayVOV2, request.getLogisticsMode(), request.getAddress(), levelId);
                prePayBuyNowVO.setFreightFee(prePayVOV2.getSubTotalFreightFee());
                prePayBuyNowVO.setIsFreeFreightFee(prePayVOV2.getIsFreeFreightFee());
                prePayBuyNowVO.setPlId(request.getPlId());
            }
            //计算优惠券优惠金额
            if (request.getMemberCouponId() != null) {
                //使用优惠券只能购买一个盲盒
                if (request.getBoxItemId() != null){
                    memberCouponProcessor.calculatePreferentialFee(request.getMemberCouponId(), prePayVOV2, prePayBuyNowVO.getTenantId());
                    prePayBuyNowVO.setCouponPreferentialFee(prePayVOV2.getCouponPreferentialFee());
                }else {
                    request.setMemberCouponId(null);
                }
            }
            //计算满减优惠
            //reductionProcessor.calculateReductionFee(prePayVOV2, prePayBuyNowVO.getTenantId());
            prePayBuyNowVO.setReductionFee(prePayVOV2.getReductionFee());
            prePayBuyNowVO.setCalculateReductionFeeVo(prePayVOV2.getCalculateReductionFeeVo());

            //积分抵扣
           // scoreDeductionProcessor.calculateDeductionScore(prePayVOV2, request.getPayScore());
            //prePayBuyNowVO.setScoreDeductionFee(prePayVOV2.getScoreDeductionFee());

            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            prePayBuyNowVO.setSubTotalBalance(prePayBuyNowVO.getSubTotalFee().multiply(chainConfig.getMoneyRatio()));
            if (BooleanUtils.isTrue(request.getIsBuyDirectly())){
                prePayBuyNowVO.setPayScore(prePayBuyNowVO.getSubTotalFee().multiply(chainConfig.getScoreRatio()));

            }

        }
        return prePayBuyNowVO;
    }

    @Override
    public PrePayBuyNowVO prePayBuyNow(PrePayBuyNowRequest request,Long levelId,String userCode ,Long tenantId, Long memberId) {
        Long skuId  = request.getSkuId();
        Long boxItemId  = request.getBoxItemId();
        Long secKillId  = request.getSecKillId();
        Long newcomerRuleId  = request.getNewcomerRuleId();

        Ssqb query = Ssqb.create("com.lewei.eshop.ma.prePayBuyNow")
                .setParam("skuId", skuId);
        PrePayBuyNowVO prePayBuyNowVO = dao.findForObj(query, PrePayBuyNowVO.class);
        //购买次数限制
//        CommodityLimitationConfigVo limitationConfigVo = appFeignClient.queryCommodityLimitationConfig();
//        if (null != limitationConfigVo && BooleanUtils.isTrue(limitationConfigVo.getIsOpen())) {
//            //配置每人每天可以购买多少次
//            Integer configTotal = limitationConfigVo.getNumberPieces();
//            Integer memberTotal = 0;
//            List<Long> spuIds = new ArrayList<>();
//            //当前配置所有限制购买商品的集合
//            List<CommodityLimitationConfigVo.CommodityLimitationConfigSpuVo> spuVo = limitationConfigVo.getCommodityLimitationConfigSpuVos();
//            if (CollectionUtils.isNotEmpty(spuVo)){
//                spuIds = spuVo.stream().map(p -> p.getSpuId()).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(spuIds) && spuIds.contains(prePayBuyNowVO.getParentSpuId())){
//                    log.info("当前spuId是" + prePayBuyNowVO.getParentSpuId());
//                    for (CommodityLimitationConfigVo.CommodityLimitationConfigSpuVo a :spuVo){
//                            Object num = redisCacheProvider.getRedisTemplate().opsForHash().get(MaCacheKeys.MEMBER_PRODUCT_QUEUE_KEY,
//                                    memberId.toString() + a.getSpuId().toString());
//                            log.info("当前用户编号"+memberId+"spuId是"+a.getSpuId() +"当前商品购买次数是"+num);
//                            if (num != null){
//                                Integer nums  = (Integer)num;
//                                memberTotal = memberTotal + nums;
//                                log.info("当前用户购买次数是"+memberTotal);
//                            }
//                    }
//                    memberTotal = memberTotal + request.getNum();
//                    if (memberTotal > configTotal){
//                        throw new BizCoreRuntimeException(MaErrorConstants.COMMODITY_LIMITATION_ERROR,configTotal);
//                    }
//                }
//
//            }
//
//        }
        if (ProductEnum.S_ST_PRODUCT.value().equals(prePayBuyNowVO.getSpuType())){
            Map<Long,Integer> spuMap = new ConcurrentHashMap<>();
            spuMap.put(prePayBuyNowVO.getParentSpuId(),request.getNum());
            this.checkDayLimit(spuMap,memberId);
        }

        if (boxItemId != null) {
            MaBoxItemSimplelVo boxItem = productBoxService.queryBoxItemSimpleVo(boxItemId);
            if (boxItem == null) {
                throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40034"));
            }
            if(ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType()) || ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType())){
                //控奖验证
                validateBuyBox(request, boxItem, memberId);

            }

            if(ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType()) || ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType())
                    || ProductBoxTypeEnum.S_BT_UN_LIMIT.value().equals(boxItem.getBoxType())){
                //锁箱验证
                validateLockBox(boxItemId,memberId);

            }
//            if (ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType())){
//                //购买次数验证
//                SaasQueryBuilder queryBuyCount = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItemId))
//                        .and(Restrictions.eq("memberId",memberId))
//                        .and(Restrictions.eq("isGift",0));
//                Integer buyCount = dao.queryForInt(queryBuyCount,ProductBoxItemRewardTmpl.class);
//                if (boxItem.getLimitNum() != null && boxItem.getLimitNum() > 0){
//                    if (buyCount >= boxItem.getLimitNum()){
//                        throw new BizCoreRuntimeException(MaErrorConstants.LIMIT_NUM_ERROR,boxItem.getLimitNum());
//                    }
//                }
//                //控奖验证
//                validateBuyBox(request, boxItem, memberId);
//                //锁箱验证
//                validateLockBox(boxItemId,memberId);
//            }
            prePayBuyNowVO.setPriceFee(boxItem.getPriceFee());
            prePayBuyNowVO.setOriginalPrice(boxItem.getPriceFee());

            if (BooleanUtils.isTrue(request.getIsAllBuy())){
                request.setNum(boxItem.getLeftRewardNum());
            }
            if (BooleanUtils.isTrue(request.getIsAllBuyCard())){
                request.setNum(boxItem.getAllBuyNum());
            }
            ScorePayConfig scorePayConfig = this.queryScorePayConfig(prePayBuyNowVO.getSpuId());
            if (scorePayConfig != null) {
                prePayBuyNowVO.setPayScore(scorePayConfig.getScore().multiply(BigDecimal.valueOf(request.getNum())));
            }
            FractionPayConfig fractionPayConfig = this.queryFractionPayConfig(prePayBuyNowVO.getSpuId());
            if (fractionPayConfig != null){
                prePayBuyNowVO.setPayFraction(fractionPayConfig.getFraction().multiply(BigDecimal.valueOf(request.getNum())));
            }
        }

        if (prePayBuyNowVO != null) {
            //秒杀价格计算
            if (secKillId != null && secKillId != 0L) {
                SecKillActivityDetailVo secKillActivityDetailVo = appFeignClient.querySecKillActivityDetailVo(secKillId);
                if (secKillActivityDetailVo != null && Objects.equals(ActivityStatusEnum.S_MAT_JXZ.value(), secKillActivityDetailVo.getActivityStatus())) {
                    //mapping >> <sku,SecKillActivityDetailVo.SecKillSKuVo>
                    Map<Long, SecKillActivityDetailVo.SecKillSkuVo> mapping = secKillActivityDetailVo.getSecKillSkuVos().stream().collect(Collectors.toMap(SecKillActivityDetailVo.SecKillSkuVo::getParentSkuId, c -> c));
                    SecKillActivityDetailVo.SecKillSkuVo secKillSKuVo = mapping.get(prePayBuyNowVO.getParentSkuId());
                    if (secKillSKuVo != null) {
                        prePayBuyNowVO.setPriceFee(secKillSKuVo.getPrice());
                        prePayBuyNowVO.setIsSecKill(true);
                    }
                    return prePayBuyNowVO;
                }
            }

            if (newcomerRuleId != null && newcomerRuleId != 0L) {
                SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("id",newcomerRuleId))
                        .and(Restrictions.eq("spuId",prePayBuyNowVO.getSpuId()));
                NewcomerActivityConfigRule newcomerActivityConfigRule = dao.query(queryBuilder,NewcomerActivityConfigRule.class);
                if (newcomerActivityConfigRule != null) {
                    //查询是否已购买此盒子 购买过不在享有新区价格
                    SaasQueryBuilder queryBuyRecord = SaasQueryBuilder.where(Restrictions.eq("memberId",memberId))
                            .and(Restrictions.eq("spuId",prePayBuyNowVO.getSpuId()));
                    if ( dao.query(queryBuyRecord, NewcomerActivityBuyRecord.class) == null){
                        prePayBuyNowVO.setPriceFee(newcomerActivityConfigRule.getPrice());
                    }
                }
                return prePayBuyNowVO;
            }

            //代理不计算会员价，直接计算自购返佣价格
            if (shopService.isDisShop(tenantId) && StringUtils.isNotEmpty(userCode)) {
                UserResponse user = feignClient.queryUserByCode(userCode);
                if (user != null) {
                    if (MemberTypeEnum.S_MT_PROXY.value().equals(user.getUserType())) {
                        CalcProfitAmountRequest amountRequest = new CalcProfitAmountRequest();
                        amountRequest.setUserCode(userCode);
                        CalcProfitAmountRequest.Goods goods = new CalcProfitAmountRequest.Goods();
                        goods.setSkuId(prePayBuyNowVO.getSkuId());
                        goods.setGoodsId(prePayBuyNowVO.getParentSpuId() != null ? prePayBuyNowVO.getParentSpuId() : prePayBuyNowVO.getSpuId()  );
                        goods.setOriginalPrice(prePayBuyNowVO.getOriginalPrice());
                        goods.setSpuType(prePayBuyNowVO.getSpuType());
                        amountRequest.setGoods(Collections.singletonList(goods));
                        List<CalcProfitAmountResponse> calcProfitAmountResponses = feignClient.calcProfitAmountByGoodsId(amountRequest);
                        if (ListUtil.isNotEmpty(calcProfitAmountResponses)) {
                            BigDecimal selfBuyProfitAmount = calcProfitAmountResponses.get(0).getSelfBuyProfitAmount() == null ? BigDecimal.ZERO : calcProfitAmountResponses.get(0).getSelfBuyProfitAmount();
                            prePayBuyNowVO.setSelfBuyProfitAmount(selfBuyProfitAmount);
                            prePayBuyNowVO.setPriceFee(prePayBuyNowVO.getOriginalPrice().subtract(selfBuyProfitAmount));
                        }
                        return prePayBuyNowVO;
                    }
                }
            }
//            if (levelId != null) {
//                MemberLevel memberLevel = memberService.queryMemberLevel(levelId);
//                if (memberLevel != null) {
//                    prePayBuyNowVO.setIsFreeFreightFee(memberLevel.getIsFreeFreightFee());
//                    if (memberLevel.getIsDiscount()) {
//                        Map<Long, BigDecimal> map = maProductService.calculateProductPrice(prePayBuyNowVO.getSpuId(), levelId, Collections.singletonList(skuId));
//                        prePayBuyNowVO.setPriceFee(map.get(skuId));
//                    }
//                    if (memberLevel.getIsFreeFreightFee()) {
//                        prePayBuyNowVO.setFreightFee(BigDecimal.ZERO);
//                    }
//                }
//            }
            if(null == request.getPlId() || request.getPlId() == 0 || null == request.getAddress()) {
                prePayBuyNowVO.setFreightFee(BigDecimal.ZERO);
            }
        }
        return prePayBuyNowVO;
    }

    @Override
    public SaveOrderVO saveOrder(SaveOrderRequest request, MemberCache memberCache, Long tenantId, String appId,String platform) {
        Long chainId = memberCache.getChainId();
        Long levelId = memberCache.getLevelId();
        Long memberId = memberCache.getId();
        List<Long> cartIds = request.getCartIds();
        Ssqb querySaveOrderData = Ssqb.create("com.lewei.eshop.ma.querySaveOrderData")
                .setParam("cartIds", cartIds);
        List<SaveOrderDataVO> saveDataList = dao.findForList(querySaveOrderData, SaveOrderDataVO.class);

        if (ListUtil.isEmpty(saveDataList)) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_CART_IS_INVALID);
        }
        if (!(OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod()) || OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(request.getPaymentMethod()) || OrderPaymentTypeEnum.S_OPM_ALIPAY.value().equals(request.getPaymentMethod()) || OrderPaymentTypeEnum.S_OPM_BANK.value().equals(request.getPaymentMethod()))){
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }


        //订单金额 支付金额
        BigDecimal orderMoney = new BigDecimal("0");

        //检验商品类型
        List<SaveOrderDataVO> checkList = null;
        if (ListUtil.isNotEmpty(saveDataList)) {
            checkList = saveDataList.stream()
                    .filter(distinctByKey(SaveOrderDataVO::getSpuType)).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(checkList) && checkList.size() > 1) {
                throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_PRODUCT_NTYPE_IS_SAME);
            }
            //提取方式校验



        }
        //校验
        for (SaveOrderDataVO data : saveDataList) {
            //校验商品是否已下架
            if (ProductEnum.S_PS_NOT_ON.value().equals(data.getStatus())) {
                throw new BizCoreRuntimeException(MaErrorConstants.WX_BE_TAKEN_OFF);
            }

        }
        //计算运费
        String cartIdStr = StringUtils.join(cartIds, ",");
        PrePayVOV2 prePayVoV2 = null;
        PrePayRequest prePayRequest = new PrePayRequest();
        prePayRequest.setMemberCouponId(request.getMemberCouponId());
        //prePayRequest.setAddress(request.getPrePayAddressRequest());
        prePayRequest.setCartIds(cartIdStr);
        prePayRequest.setTenantId(tenantId);
        prePayRequest.setCartIds(cartIdStr);
        prePayVoV2 = prePayV3(prePayRequest, memberId, chainId, levelId, memberCache.getUserCode());

        if (prePayVoV2 == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }
        if (request.getSubTotalFee().compareTo(prePayVoV2.getSubTotalFee()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }

        BigDecimal freightFee = prePayVoV2.getSubTotalFreightFee();
        //计算 订单金额
        Map<Long, BigDecimal> productToFeeMap = new HashMap<>();
        for (PrePayVO prePayVO : prePayVoV2.getPrePayVOList()) {
            productToFeeMap.put(prePayVO.getSkuId(), prePayVO.getPriceFee());
        }

        orderMoney = prePayVoV2.getSubTotalFee();
//        if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(request.getPaymentMethod()) || OrderPaymentTypeEnum.S_OPM_ALIPAY.value().equals(request.getPaymentMethod())){
//            orderMoney = maPayDeductService.handelPayDeduct(orderMoney);
//        }


        //处理盲盒/购买商品消费提醒
        Boolean consumerReminder = iConsumerReminderService.handleConsumerReminder(request.getPaymentMethod(),memberId,chainId,orderMoney,saveDataList.get(0).getSpuType());

        //订单标题
        String orderTitle = saveDataList.get(0).getSpuName();
        if (saveDataList.size() > 1) {
            orderTitle = orderTitle + BizMessageSource.getInstance().getMessage("cem40035");
        }

        //下单
        Order order = new Order();
        order.setChainId(chainId);
        order.setTenantId(tenantId);
        order.setOrderSn(CodecUtil.createOrderId());
        order.setWechatAccount("");
        order.setMemberId(memberId);
        order.setOpenId(memberCache.getOpenId());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(orderMoney);
        order.setPaymentMoney(orderMoney);
        order.setShippingMoney(freightFee);
        order.setShippingUser(request.getShippingUser());
        order.setTelNumber(request.getTelNumber());
        order.setProvinceName(request.getProvinceName());
        order.setCityName(request.getCityName());
        order.setCountyName(request.getCountyName());
        order.setAddress(request.getAddress());
        order.setPostalCode(request.getPostalCode());
        order.setOrderTitle(orderTitle);
        order.setOrderType(orderTypeMap.get(saveDataList.get(0).getSpuType()));
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setMemberCouponId(request.getMemberCouponId());
        order.setCouponPreferentialFee(prePayVoV2.getCouponPreferentialFee());
        order.setReductionFee(prePayVoV2.getReductionFee());
        order.setRemark(request.getRemark());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(memberId);
        order.setInviterCode(request.getInviterCode());
        order.setPlatform(platform);
        handleOrderBizType(order, memberCache.getOpenId(), request.getInviterCode());
        //判断是否需要验证码
//        boolean needVerify = false;
//        if (Objects.equals(saveDataList.get(0).getSpuType(), ProductEnum.S_ST_SERVICE.value())) {
//            needVerify = saveDataList.stream().
//                    anyMatch(a -> Objects.equals(a.getVerifyType(), ProductEnum.S_VT_TO_STORE.value()));
//        }
//        if (needVerify) {
//            order.setVerifyCode(1 + RandomStringUtils.randomNumeric(7));
//        }
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(request.getPaymentMethod())){
            map.put("payMethod",request.getPayMethod());
        }
        if (OrderEnum.S_OOT_PRODUCT.value().equals(order.getOrderType())){
            if(request.getPrePayAddressRequest() != null){
                map.put("area",request.getPrePayAddressRequest().getArea());
                map.put("city",request.getPrePayAddressRequest().getCity());
                map.put("address",request.getPrePayAddressRequest().getAddress());
                map.put("province",request.getPrePayAddressRequest().getProvince());
            }
            order.setExtJson(JSON.toJSONString(map));
        }

        dao.save(order);

        //订单ID
        Long orderId = order.getId();

        //生成订单明细
        List<OrderProduct> orderProductList = new ArrayList<>();
        for (SaveOrderDataVO data : saveDataList) {
            //List<PrePayVO> prePayVOList = prePayVOV2.getPrePayVOList().stream().filter(a->Objects.equals(a.getSkuId(),data.getSkuId())).collect(Collectors.toList());
            BigDecimal priceFee = productToFeeMap.get(data.getSkuId());
            OrderProduct orderProduct = new OrderProduct();
            orderProduct.setChainId(chainId);
            orderProduct.setTenantId(tenantId);
            orderProduct.setOrderId(orderId);
            orderProduct.setParentSpuId(data.getParentSpuId());
            orderProduct.setParentSkuId(data.getParentSkuId());
            orderProduct.setSpuId(data.getSpuId());
            orderProduct.setSkuId(data.getSkuId());
            orderProduct.setSupplySkuId(data.getSupplySkuId());
            orderProduct.setProductType(data.getSpuType());
            orderProduct.setProductName(data.getSpuName());
            orderProduct.setMainImage(data.getMainImage());
            orderProduct.setPrimeCostFee(data.getPrimeCostFee());
            orderProduct.setAttrValues(data.getAttrValues());
            orderProduct.setCurrentUnitPrice(priceFee);
            orderProduct.setQuantity(data.getNum());
            orderProduct.setTotalPrice(priceFee.multiply(new BigDecimal(data.getNum())));
            orderProduct.setVerifyType(data.getVerifyType());
            orderProduct.setCreated(new Timestamp(System.currentTimeMillis()));
            orderProduct.setCreateBy(memberId);
            orderProduct.setPlType(data.getPlType());
            orderProduct.setPlId(data.getPlId());
            orderProduct.setSupplySkuId(data.getSupplySkuId());
            orderProductList.add(orderProduct);
      //      maProductService.updateSkuQuantity(data.getSkuId(), data.getNum(), data.getSpuName(), data.getSpuType());
        }
        dao.batchSave(orderProductList, OrderProduct.class);

        //更新优惠券状态
        if (request.getMemberCouponId() != null) {
            maMemberCouponService.updateMemberCouponStatus(MemberCouponStatusEnum.S_MCDS_YSY.value(), request.getMemberCouponId());
        }
        //抵扣积分
//        if (request.getPayScore() != null && request.getPayScore() > 0) {
//            memberFeignClient.consumeMemberScore(new ConsumeScoreRequest(memberId, ScoreTaskEnum.S_SST_SCORE_DEDUCTION.value(), BigDecimal.valueOf(request.getPayScore()), order.getOrderTitle()));
//        }


        //清除购物车
        shoppingCartService.deleteShoppingCarts(cartIds);

       // this.saveReductionGift(prePayVoV2.getCalculateReductionFeeVo(), order);

        SaveOrderVO returnVo = new SaveOrderVO();
        returnVo.setOrderId(orderId);
        returnVo.setConsumerReminder(consumerReminder);
        this.callPayApi(request.getPaymentMethod(), order, memberCache, chainId, tenantId, appId, returnVo,null,null,null,null,request.getPayMethod(),request.getMemberBankId(),request.getPrePayAddressRequest());

        return returnVo;
    }

    @Override
    public SaveOrderVO saveOrderBuyNow(BuyNowRequest request, MemberCache memberCache, Long tenantId, String appId,String platform) {
        Long memberId = memberCache.getId();
        Long chainId = memberCache.getChainId();
        String openId = memberCache.getOpenId();
        //校验购买的商品
        SaasQueryBuilder querySpu = SaasQueryBuilder.where(Restrictions.eq("id", request.getSpuId()))
                .and(Restrictions.eq("dataStatus", 1));
        ProductSpu spu = dao.query(querySpu, ProductSpu.class);
        SaasQueryBuilder querySku = SaasQueryBuilder.where(Restrictions.eq("id", request.getSkuId()));
        ProductSku sku = dao.query(querySku, ProductSku.class);
        if (spu == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_NO_SUCH_SKU);
        }
        //校验会员等级购买限制
        if( spu.getLevelBuyPriority().compareTo(memberCache.getPriority())  > 0){
            throw new BizCoreRuntimeException(MaErrorConstants.PRODUCT_MEMBER_LEVEL_INSUFFICIENT);
        }



        String attrValues = "";
//        SaasQueryBuilder querySkuAttrMap = SaasQueryBuilder.where(Restrictions.eq("skuId", sku.getId()));
//        List<ProductSpuSkuAttrMap> spuSkuAttrMaps = dao.queryList(querySkuAttrMap, ProductSpuSkuAttrMap.class);
//        if (ListUtil.isNotEmpty(spuSkuAttrMaps)) {
//            attrValues = spuSkuAttrMaps.stream().filter(a -> StringUtils.isNotEmpty(a.getAttrValueName())).map(ProductSpuSkuAttrMap::getAttrValueName).collect(Collectors.joining(","));
//        }
//        if (Objects.equals(spu.getSpuType(), ProductEnum.S_ST_PRODUCT.value()) && StringUtil.isEmpty(request.getLogisticsMode())) {
//            throw new BizCoreRuntimeException(MaErrorConstants.LOGISTICSMODE_IS_EMPTY);
//        }

        if (ProductEnum.S_PS_NOT_ON.value().equals(spu.getStatus()) && !ProductEnum.S_ST_TREASURE.value().equals(spu.getSpuType())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_BE_TAKEN_OFF);
        }
//        if (sku.getQuantity() <= 0) {
//            throw new BizCoreRuntimeException(MaErrorConstants.WX_OUT_OF_STOCK);
//        }
//        if (request.getNum() != null && request.getNum().compareTo(sku.getQuantity()) > 0) {
//            throw new BizCoreRuntimeException(MaErrorConstants.WX_GREATER_QUANTITY);
//        }
        //秒杀活动验证
        this.validateSecKill(request, memberId, sku.getParentSkuId());
        //购买盲盒验证
        this.validateBoxItem(request,memberId);
        this.validateService(request,spu,memberId);

        PrePayBuyNowVO prePayBuyNowVO = null;


        PrePayBuyNowRequest prePayBuyNowRequest = new PrePayBuyNowRequest();
        prePayBuyNowRequest.setMemberCouponId(request.getMemberCouponId());
        prePayBuyNowRequest.setAddress(request.getPrePayAddressRequest());
        prePayBuyNowRequest.setLogisticsMode(request.getLogisticsMode());
        prePayBuyNowRequest.setNum(request.getNum());
        prePayBuyNowRequest.setSkuId(request.getSkuId());
        prePayBuyNowRequest.setPayScore(request.getPayScore());
        prePayBuyNowRequest.setSecKillId(request.getSecKillId());
        prePayBuyNowRequest.setBoxItemId(request.getBoxItemId());
        prePayBuyNowRequest.setIsAllBuy(request.getIsAllBuy());
        prePayBuyNowRequest.setIsAllBuyCard(request.getIsAllBuyCard());
        prePayBuyNowRequest.setNewcomerRuleId(request.getNewcomerRuleId());
        prePayBuyNowRequest.setPlId(request.getPlId());
        prePayBuyNowRequest.setIsBuyDirectly(request.getIsBuyDirectly());

        prePayBuyNowVO = prePayBuyNowV3(prePayBuyNowRequest, memberCache.getLevelId(), memberCache.getUserCode(), memberId);
        //临时处理  优惠券 前端 bug 多张优惠券传入优惠券id
        request.setMemberCouponId(prePayBuyNowRequest.getMemberCouponId());
        if (request.getCurrentUnitPrice() != null && prePayBuyNowVO != null && request.getCurrentUnitPrice().compareTo(prePayBuyNowVO.getPriceFee()) != 0) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_PRICE_CHANGED);
        }
        checkPickUpTime(request.getPickupTime(), tenantId);
        //总计
        if (prePayBuyNowVO == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
        }
        BigDecimal orderMoney = prePayBuyNowVO.getSubTotalFee();

        //校验是否优惠卷支付
        String extJson = spu.getExtJson();
        if (StringUtils.isNotEmpty(extJson)) {
            JSONObject jsonObject = JSONObject.parseObject(extJson);
            //判断开关是否开启
            if(Objects.nonNull(jsonObject)&& jsonObject.containsKey("payment") && "coupon".equals(jsonObject.getString("payment"))){
                //判断是否适用优惠卷并且价格为0
                if(Objects.isNull(request.getMemberCouponId()) || orderMoney.compareTo(BigDecimal.ZERO) != 0){
                    throw new BizCoreRuntimeException(MaErrorConstants.BUY_PAY_TYPE_COUPON_ERROR);
                }
            }

        }

        //积分支付验证
        if (OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(request.getPaymentMethod())){
            if (prePayBuyNowVO.getPayScore() ==null){
                throw new BizCoreRuntimeException(MaErrorConstants.SCORE_PAY_NOT_CONFIG);
            }
            if (prePayBuyNowVO.getPayScore().compareTo(BigDecimal.ZERO) < 0){
                throw new BizCoreRuntimeException(MaErrorConstants.SCORE_PAY_NOT_CONFIG);
            }
            if (request.getPayScore() == null || prePayBuyNowVO.getPayScore().compareTo(request.getPayScore()) != 0){
                throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
            }
            if (!ProductEnum.S_ST_BOX.value().equals(prePayBuyNowVO.getSpuType())){
                throw new BizCoreRuntimeException(MaErrorConstants.SCORE_CAN_ONLY_PAY_BOX);
            }
            orderMoney = BigDecimal.ZERO;
        }
        if(null != request.getPlId() && request.getPlId() > 0 && null != request.getAddress()){
            orderMoney.add(prePayBuyNowVO.getFreightFee());
        }
        //积分2支付验证
        if (OrderPaymentTypeEnum.S_OPM_FRACTION.value().equals(request.getPaymentMethod())){
            if (prePayBuyNowVO.getPayFraction() ==null){
                throw new BizCoreRuntimeException(MaErrorConstants.FRACTION_PAY_NOT_CONFIG);
            }
            if (request.getPayFraction() == null || prePayBuyNowVO.getPayFraction().compareTo(request.getPayFraction()) != 0){
                throw new BizCoreRuntimeException(MaErrorConstants.WX_INFO_OUT_OF_DATE);
            }
            if (!ProductEnum.S_ST_BOX.value().equals(prePayBuyNowVO.getSpuType())){
                throw new BizCoreRuntimeException(MaErrorConstants.FRACTION_CAN_ONLY_PAY_BOX);
            }
            orderMoney = BigDecimal.ZERO;
        }

        if (OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(request.getPaymentMethod())){

            orderMoney = BigDecimal.ZERO;
        }

        if (OrderUtil.isCashPay(request.getPaymentMethod())){
           if (request.getBoxItemId() != null){
               orderMoney = maPayDeductService.handelPayDeduct(orderMoney);
           }
        }
        //校验
       if (null != request.getPlId() && request.getPlId() > 0){
           checkCrowd(request.getPlId(),request.getNum(),orderMoney,request.getSpuId(),spu.getName(),memberId);
       }
        //处理盲盒/购买商品消费提醒
        Boolean consumerReminder = iConsumerReminderService.handleConsumerReminder(request.getPaymentMethod(),memberId,chainId,orderMoney,spu.getSpuType());
        //订单标题
        String orderTitle = spu.getName();

        //下单
        Order order = new Order();
        order.setChainId(chainId);
        order.setTenantId(tenantId);
        order.setOrderSn(CodecUtil.createOrderId());
        order.setWechatAccount("");
        order.setRobotCode(request.getRobotCode());
        order.setMemberId(memberId);
        order.setOpenId(openId);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(orderMoney);
        order.setPaymentMoney(orderMoney);
        order.setShippingMoney(prePayBuyNowVO.getFreightFee());
        order.setShippingUser(request.getShippingUser());
        order.setTelNumber(request.getTelNumber());
        order.setProvinceName(request.getProvinceName());
        order.setCityName(request.getCityName());
        order.setCountyName(request.getCountyName());
        order.setAddress(request.getAddress());
        order.setEmail(request.getEmail());
        order.setPostalCode(request.getPostalCode());
        order.setNationalCode(request.getNationalCode());
        order.setOrderTitle(orderTitle);
        if (BooleanUtils.isTrue(spu.getIsCard())){
            if (ProductEnum.S_ST_PRODUCT.value().equals(spu.getSpuType())){
                order.setOrderType(OrderEnum.S_OOT_CARD.value());
            }
            if (ProductEnum.S_ST_BOX.value().equals(spu.getSpuType())){
                order.setOrderType(OrderEnum.S_OOT_BOX_CARD.value());
            }
        }else {
            if(null != request.getPlId() && request.getPlId() > 0){
                order.setOrderType(OrderEnum.S_OOT_CROWD_FUNDING.value());
                /*// 处理众筹订单数量
                Ssqb updateOrderCount = Ssqb.create("com.lewei.eshop.ma.crowd.funding.updateOrderCount")
                        .setParam("plId", request.getPlId());
                dao.updateByMybatis(updateOrderCount);*/
            }else {
                order.setOrderType(orderTypeMap.get(spu.getSpuType()));
            }
        }
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        order.setPickupTime(request.getPickupTime());
        order.setMemberCouponId(request.getMemberCouponId());
        order.setCouponPreferentialFee(prePayBuyNowVO.getCouponPreferentialFee());
        order.setReductionFee(prePayBuyNowVO.getReductionFee());
        order.setRemark(request.getRemark());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setLogisticsMode(request.getLogisticsMode());
        order.setCreateBy(memberId);
        order.setPlatform(platform);
        order.setInviterCode(request.getInviterCode());
        order.setPayScore(Optional.ofNullable(request.getPayScore()).orElse(BigDecimal.ZERO));
        order.setPayFraction(Optional.ofNullable(request.getPayFraction()).orElse(BigDecimal.ZERO));
        order.setScoreDeductionFee(prePayBuyNowVO.getScoreDeductionFee());
        handleOrderBizType(order, openId, request.getInviterCode());
        //需要验证的服务生成验证码
        if (verificationCodeRequired(spu.getSpuType(), spu.getVerifyType())) {
            order.setVerifyCode(1 + RandomStringUtils.randomNumeric(7));
        }
        order.setGsc(request.getGsc());
        if (request.getPaymentMethod().equals(OrderPaymentTypeEnum.S_OPM_BALANCE.value())){
            order.setOrderBalance(prePayBuyNowVO.getSubTotalBalance());
        }
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(request.getPaymentMethod())){
            map.put("payMethod",request.getPayMethod());
            order.setExtJson(JSON.toJSONString(map));
        }
        if (OrderEnum.S_OOT_PRODUCT.value().equals(order.getOrderType()) ||
            OrderEnum.S_OOT_CROWD_FUNDING.value().equals(order.getOrderType())){
            if(request.getPrePayAddressRequest() != null){
                map.put("area",request.getPrePayAddressRequest().getArea());
                map.put("city",request.getPrePayAddressRequest().getCity());
                map.put("address",request.getPrePayAddressRequest().getAddress());
                map.put("province",request.getPrePayAddressRequest().getProvince());
            }
            //退款截止时间
            SaasQueryBuilder queryRefund = SaasQueryBuilder.where();
            RefundConfig refund = dao.query(queryRefund, RefundConfig.class);
            if (!(Objects.isNull(refund) || !refund.getIsOpen() || refund.getIsUnlimited())) {
                Date receiptExpireTime = DateFormatUtils.addDate(getNowLastTime(), refund.getDuration());
                map.put("duration", refund.getDuration());
                map.put("receiptExpireTime", receiptExpireTime.getTime());
            }
            order.setExtJson(JSON.toJSONString(map));
        }
        if(OrderEnum.S_OOT_SERVICE.value().equals(order.getOrderType())){
            map.put("memberId", order.getMemberId());
            order.setExtJson(JSON.toJSONString(map));
        }
        dao.save(order);

        if(OrderEnum.S_OOT_CROWD_FUNDING.value().equals(order.getOrderType())) {
            //新增订单和众筹关联表
            CrowdOrderRef crowdOrderRef = new CrowdOrderRef();
            crowdOrderRef.setCreated(new Timestamp(System.currentTimeMillis()));
            crowdOrderRef.setOrderId(order.getId());
            crowdOrderRef.setCrowdId(request.getPlId());
            crowdOrderRef.setChainId(chainId);
            crowdOrderRef.setSpuId(request.getSpuId());
            crowdOrderRef.setNum(request.getNum());
            crowdOrderRef.setCrowdAmount(order.getOrderMoney());
            crowdOrderRef.setMemberId(order.getMemberId());
            crowdOrderRef.setPaymentMethod(request.getPaymentMethod());
            crowdOrderRef.setCrowdCost((sku.getPrimeCostFee().multiply(BigDecimal.valueOf(request.getNum()))).setScale(2,BigDecimal.ROUND_HALF_UP));
            crowdOrderRef.setStorageDataTime(new Timestamp(System.currentTimeMillis()));
            dao.save(crowdOrderRef);
        }

        //订单ID
        Long orderId = order.getId();

        //生成订单明细
        OrderProduct orderProduct = new OrderProduct();
        orderProduct.setChainId(chainId);
        orderProduct.setTenantId(tenantId);
        orderProduct.setOrderId(orderId);
        orderProduct.setParentSpuId(sku.getParentSpuId());
        orderProduct.setParentSkuId(sku.getParentSkuId());
        orderProduct.setSpuId(sku.getSpuId());
        orderProduct.setSkuId(sku.getId());
        orderProduct.setSupplySkuId(sku.getSupplySkuId());
        orderProduct.setProductType(spu.getSpuType());
        orderProduct.setProductName(spu.getName());
        orderProduct.setMainImage(sku.getMainImage());
        orderProduct.setAttrValues(attrValues);
        orderProduct.setCurrentUnitPrice(request.getCurrentUnitPrice());
        orderProduct.setQuantity(prePayBuyNowVO.getNum());
        if(null != request.getPlId() && request.getPlId() > 0){
            orderProduct.setNotShipQuantity(prePayBuyNowVO.getNum());
        }
        orderProduct.setTotalPrice(request.getCurrentUnitPrice().multiply(BigDecimal.valueOf(request.getNum())));
        orderProduct.setVerifyType(spu.getVerifyType());
        orderProduct.setCreated(new Timestamp(System.currentTimeMillis()));
        orderProduct.setCreateBy(memberId);
        orderProduct.setPlType(spu.getPlType());
        orderProduct.setPlId(spu.getPlId());
        orderProduct.setBoxItemId(request.getBoxItemId());
        orderProduct.setSecKillId(request.getSecKillId());
        orderProduct.setPrimeCostFee(sku.getPrimeCostFee());

        dao.save(orderProduct);
        if (OrderEnum.S_OOT_SERVICE.value().equals(order.getOrderType())){
            //
            SaasQueryBuilder queryProductSkuService = SaasQueryBuilder.where(Restrictions.eq("skuId", orderProduct.getSkuId()));
            ProductSkuService skuService = dao.query(queryProductSkuService,ProductSkuService.class);
            if (skuService != null) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("serviceTime",skuService.getServiceTime());
                jsonObject.put("quantityLimit",skuService.getQuantityLimit());
                jsonObject.put("unit",skuService.getUnit());
                OrderProductEx orderProductEx = new OrderProductEx();
                orderProductEx.setExtJson(jsonObject.toJSONString());
                orderProductEx.setCreated(DateFormatUtils.getNow());
                orderProductEx.setOrderProductId(orderProduct.getId());;
                dao.create(orderProductEx);
            }
        }
        //处理买卡事件
     //   applicationEventPublisher.publishEvent(new BuyCardEvent(this, Collections.singletonList(orderProduct), order, request.getCardItemSkuIds()));
        //购买盲盒事件
        applicationEventPublisher.publishEvent(new BuyBoxEvent(this,order,request.getBoxItemId(),orderProduct.getQuantity(),request.getIsAllBuy(),request.getIsAllBuyCard(),request.getCurrentUnitPrice(),prePayBuyNowVO.getBoxType(),sku.getSpuId(),request.getSkuId(),request.getInviterId(), request.getIsCouponAuto(),request.getSerialNums(),request.getStageId(), MDCTraceUtils.getTraceId(),request.getNewcomerRuleId(),request.getSelectSkuId(), request.getSubBoxItemId()));
     //   maProductService.updateSkuQuantity(sku.getId(), request.getNum(), spu.getName(), spu.getSpuType());
        //生成验证码 (是服务且需要到店验证)
//        if (verificationCodeRequired(spu.getSpuType(), spu.getVerifyType())) {
//            generateVerifyCode(orderId, orderProduct.getId(), orderProduct.getQuantity(), tenantId);
//        }

        //更新优惠券状态
        if (request.getMemberCouponId() != null) {
            maMemberCouponService.updateMemberCouponStatus(MemberCouponStatusEnum.S_MCDS_YSY.value(), request.getMemberCouponId());
        }
        //更新秒杀库存
//        if (request.getSecKillId() != null) {
//            secKillActivityService.updateSecKillQuantity(request.getNum(), sku.getParentSkuId(), request.getSecKillId());
//        }
        //抵扣积分
//        if (request.getPayScore() != null && request.getPayScore() > 0) {
//            memberFeignClient.consumeMemberScore(new ConsumeScoreRequest(memberId, ScoreTaskEnum.S_SST_SCORE_DEDUCTION.value(), BigDecimal.valueOf(request.getPayScore()), order.getOrderTitle()));
//        }

        if (ProductEnum.S_ST_SERVICE.value().equals(spu.getSpuType())){
            maProductService.updateSkuQuantity(sku.getId(), request.getNum(), spu.getName(), spu.getSpuType());

        }
        //处理IP分润
//        applicationEventPublisher.publishEvent( new MemberServiceEvent(this,order,orderProduct, MDCTraceUtils.getTraceId()));

        this.saveReductionGift(prePayBuyNowVO.getCalculateReductionFeeVo(), order);

        SaveOrderVO returnVo = new SaveOrderVO();
        returnVo.setOrderId(orderId);
        returnVo.setConsumerReminder(consumerReminder);
        //调取微信支付
        this.callPayApi(request.getPaymentMethod(), order, memberCache, chainId, tenantId, appId, returnVo,request.getBoxItemId(),request.getStageId(),request.getNum(),request.getReturnUrl(),request.getPayMethod(),request.getMemberBankId(),request.getPrePayAddressRequest());
        this.saveOrderPayInfo(orderId,returnVo.getPayUrl(),request.getPaymentMethod());
        return returnVo;
    }

    private Date getNowLastTime(){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 58);
        Date date = calendar.getTime();
       return date;
    }


    @Override
    public String toPay(Long orderId, MemberCache memberCache, String appId,String platform) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", orderId))
                .and(Restrictions.eq("dataStatus", 1));
        Order order = dao.query(query, Order.class);
        if (order == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_NON_EXISTENT);
        }
        if (order.getPayStatus() || !OrderEnum.S_OS_UNPAID.value().equals(order.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_NOT_UNPAID);
        }
        if (!Objects.equals(platform,order.getPlatform())){
            if (MaPlatformEnum.APP.value().equalsIgnoreCase(order.getPlatform())){
                throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PAY_PLATFORM_ERROR,
                        BizMessageSource.getInstance().getMessage("cem40036"),
                        BizMessageSource.getInstance().getMessage("cem40037"));
            }
            if (MaPlatformEnum.WEIXIN.value().equalsIgnoreCase(order.getPlatform())){
                throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PAY_PLATFORM_ERROR,
                        BizMessageSource.getInstance().getMessage("cem40037"),
                        BizMessageSource.getInstance().getMessage("cem40036"));
            }
        }

        //校验商品是否已删除 或已下架
        Ssqb queryDfkProducts = Ssqb.create("com.lewei.eshop.ma.queryDfkProducts")
                .setParam("orderId", orderId);
        List<DfkOrderProductVo> dfkOrderProductVos = dao.findForList(queryDfkProducts, DfkOrderProductVo.class);
        if (ListUtil.isNotEmpty(dfkOrderProductVos)) {
            List<DfkOrderProductVo> deleteOrOffShelfProducts = dfkOrderProductVos.stream().
                    filter(a -> BooleanUtils.isFalse(a.getDataStatus()) || !Objects.equals(a.getStatus(),ProductEnum.S_PS_ON.value())).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(deleteOrOffShelfProducts)) {
                StringBuilder stringBuilder = new StringBuilder();
                deleteOrOffShelfProducts.forEach(a -> {
                    stringBuilder.append("【").append(a.getName()).append("】");
                });
                throw new BizCoreRuntimeException(MaErrorConstants.PRODUCT_HAS_BEEN_DELETED_OR_OFF_SHELF, stringBuilder.toString());
            }
        }



        //商品限购验证
        if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_PRODUCT.value())){
            //根据orderId获取
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderId", order.getId()));
            List<OrderProduct> orderProduct = dao.queryList(queryBuilder, OrderProduct.class);
            //增加次数
            CommodityLimitationConfigVo limitationConfigVo = appFeignClient.queryCommodityLimitationConfig();
            //开启状态
            if (null != limitationConfigVo && BooleanUtils.isTrue(limitationConfigVo.getIsOpen())) {
                //包含在设置里的spu
                if (CollectionUtils.isNotEmpty(limitationConfigVo.getCommodityLimitationConfigSpuVos())){
                    List<Long> spuIds = limitationConfigVo.getCommodityLimitationConfigSpuVos().stream()
                            .map(p -> p.getSpuId()).collect(Collectors.toList());
                    //校验
                    this.checkCommodityLimitation(limitationConfigVo.getCommodityLimitationConfigSpuVos(),orderProduct,order.getMemberId(),limitationConfigVo.getNumberPieces());
                }
            }
        }

        if(null != order.getId() && Objects.equals(order.getOrderType(),OrderEnum.S_OOT_CROWD_FUNDING.value())){
            SaasQueryBuilder query1 = SaasQueryBuilder.where(Restrictions.eq("orderId", order.getId()));
            CrowdOrderRef crowdOrderRef = dao.query(query1, CrowdOrderRef.class);
            if (null != crowdOrderRef) {
                SaasQueryBuilder query2 = SaasQueryBuilder.where(Restrictions.eq("id", crowdOrderRef.getCrowdId()));
                CrowdFunding crowdFunding = dao.query(query2, CrowdFunding.class);
                if (null != crowdFunding) {
                    if (!CrowdFundingStatusEnum.S_CFS_ZCZ.value().equals(crowdFunding.getStatus())) {
                        throw new BizCoreRuntimeException(MaErrorConstants.CROWDFUNDING_STATUS_FAIL);
                    }
                }else {
                    throw new BizCoreRuntimeException(MaErrorConstants.CROWD_HAS_BEEN_DELETE);
                }
            }else {
                throw new BizCoreRuntimeException(MaErrorConstants.CROWD_HAS_BEEN_DELETE);
            }
        }
        //套盒重新支付验证
        if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_BOX.value())){
            BuyNowRequest buyNowRequest = new BuyNowRequest();
            JSONObject extJson = JSON.parseObject(order.getExtJson());
            if (extJson != null) {
                buyNowRequest.setNum(extJson.getInteger("num"));
                buyNowRequest.setBoxItemId(extJson.getLong("boxItemId"));
                buyNowRequest.setIsAllBuy(extJson.getBoolean("isAllBuy"));
                this.validateBoxItem(buyNowRequest, memberCache.getId());
            }
        }

        String bizType = null;
        if (OrderEnum.S_OOT_BOX.value().equals(order.getOrderType())){
            bizType = "default";
        }else {
            bizType = "mall";
        }
        String  payMethod = null;
        if (StringUtils.isNotEmpty(order.getExtJson())){
            payMethod = JSON.parseObject(order.getExtJson()).getString("payMethod");
        }

        //调取微信支付
        String pay = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                order.getOrderSn(), memberCache.getOpenId(), memberCache.getChainId(), appId, order.getPaymentMethod(), callBackUrl, null, payMethod, bizType, memberCache.getMobile());
        this.saveOrderPayInfo(orderId,pay,order.getPaymentMethod());
        return pay;


    }

    @Override
    public PayVo toPayNew(Long orderId, MemberCache memberCache, String appId, String platform) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", orderId))
                .and(Restrictions.eq("dataStatus", 1));
        Order order = dao.query(query, Order.class);
        if (order == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_NON_EXISTENT);
        }
        if (order.getPayStatus() || !OrderEnum.S_OS_UNPAID.value().equals(order.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_NOT_UNPAID);
        }
        if (!Objects.equals(platform,order.getPlatform())){
            if (MaPlatformEnum.APP.value().equalsIgnoreCase(order.getPlatform())){
                throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PAY_PLATFORM_ERROR,
                        BizMessageSource.getInstance().getMessage("cem40036"),
                        BizMessageSource.getInstance().getMessage("cem40037"));
            }
            if (MaPlatformEnum.WEIXIN.value().equalsIgnoreCase(order.getPlatform())){
                throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PAY_PLATFORM_ERROR,
                        BizMessageSource.getInstance().getMessage("cem40037"),
                        BizMessageSource.getInstance().getMessage("cem40036"));
            }
        }

        //校验商品是否已删除 或已下架
        Ssqb queryDfkProducts = Ssqb.create("com.lewei.eshop.ma.queryDfkProducts")
                .setParam("orderId", orderId);
        List<DfkOrderProductVo> dfkOrderProductVos = dao.findForList(queryDfkProducts, DfkOrderProductVo.class);
        if (ListUtil.isNotEmpty(dfkOrderProductVos)) {
            List<DfkOrderProductVo> deleteOrOffShelfProducts = dfkOrderProductVos.stream().
                    filter(a -> BooleanUtils.isFalse(a.getDataStatus()) || !Objects.equals(a.getStatus(),ProductEnum.S_PS_ON.value())).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(deleteOrOffShelfProducts)) {
                StringBuilder stringBuilder = new StringBuilder();
                deleteOrOffShelfProducts.forEach(a -> {
                    stringBuilder.append("【").append(a.getName()).append("】");
                });
                throw new BizCoreRuntimeException(MaErrorConstants.PRODUCT_HAS_BEEN_DELETED_OR_OFF_SHELF, stringBuilder.toString());
            }
        }


        //处理盲盒/购买商品消费提醒
        String spuType = null;
        if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_PRODUCT.value())){
            spuType = ProductEnum.S_ST_PRODUCT.value();
        }
        if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_BOX.value())){
            spuType = ProductEnum.S_ST_BOX.value();
        }
        Boolean consumerReminder = false;
        if (StringUtils.isNotBlank(spuType)) {
            consumerReminder = iConsumerReminderService.handleConsumerReminder(order.getPaymentMethod(), order.getMemberId(), order.getChainId(), order.getOrderMoney(), spuType);
        }

        //商品限购验证
        if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_PRODUCT.value())){
            //根据orderId获取
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderId", order.getId()));
            List<OrderProduct> orderProduct = dao.queryList(queryBuilder, OrderProduct.class);
//            //增加次数
//            CommodityLimitationConfigVo limitationConfigVo = appFeignClient.queryCommodityLimitationConfig();
//            //开启状态
//            if (null != limitationConfigVo && BooleanUtils.isTrue(limitationConfigVo.getIsOpen())) {
//                //包含在设置里的spu
//                if (CollectionUtils.isNotEmpty(limitationConfigVo.getCommodityLimitationConfigSpuVos())){
//                    List<Long> spuIds = limitationConfigVo.getCommodityLimitationConfigSpuVos().stream()
//                            .map(p -> p.getSpuId()).collect(Collectors.toList());
//                    //校验
//                    this.checkCommodityLimitation(limitationConfigVo.getCommodityLimitationConfigSpuVos(),orderProduct,order.getMemberId(),limitationConfigVo.getNumberPieces());
//                }
//            }

            Map<Long,Integer> spuMap = new ConcurrentHashMap<>();
            orderProduct.forEach(p -> {
                spuMap.put(p.getParentSpuId(),p.getQuantity());
            });

            this.checkDayLimit(spuMap,memberCache.getId());
        }

        if(null != order.getId() && Objects.equals(order.getOrderType(),OrderEnum.S_OOT_CROWD_FUNDING.value())){
            SaasQueryBuilder query1 = SaasQueryBuilder.where(Restrictions.eq("orderId", order.getId()));
            CrowdOrderRef crowdOrderRef = dao.query(query1, CrowdOrderRef.class);
            if (null != crowdOrderRef) {
                SaasQueryBuilder query2 = SaasQueryBuilder.where(Restrictions.eq("id", crowdOrderRef.getCrowdId()));
                CrowdFunding crowdFunding = dao.query(query2, CrowdFunding.class);
                if (null != crowdFunding) {
                    if (!CrowdFundingStatusEnum.S_CFS_ZCZ.value().equals(crowdFunding.getStatus())) {
                        throw new BizCoreRuntimeException(MaErrorConstants.CROWDFUNDING_STATUS_FAIL);
                    }
                }else {
                    throw new BizCoreRuntimeException(MaErrorConstants.CROWD_HAS_BEEN_DELETE);
                }
            }else {
                throw new BizCoreRuntimeException(MaErrorConstants.CROWD_HAS_BEEN_DELETE);
            }
        }
        //套盒重新支付验证
        if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_BOX.value())){
            BuyNowRequest buyNowRequest = new BuyNowRequest();
            JSONObject extJson = JSON.parseObject(order.getExtJson());
            if (extJson != null) {
                buyNowRequest.setNum(extJson.getInteger("num"));
                buyNowRequest.setBoxItemId(extJson.getLong("boxItemId"));
                buyNowRequest.setIsAllBuy(extJson.getBoolean("isAllBuy"));
                this.validateBoxItem(buyNowRequest, memberCache.getId());
            }
        }

        String bizType = null;
        if (OrderEnum.S_OOT_BOX.value().equals(order.getOrderType())){
            bizType = "default";
        }else {
            bizType = "mall";
        }
        String  payMethod = null;
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(order.getExtJson())){
            JSONObject orderExt = JSON.parseObject(order.getExtJson());
            payMethod = orderExt.getString("payMethod");

            map.put("memberBankId",orderExt.getLong("memberBankId"));
            map.put("orderId",orderId);
        }



        //调取微信支付
        String payUrl =  orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                order.getOrderSn(), memberCache.getOpenId(), memberCache.getChainId(), appId,order.getPaymentMethod(),callBackUrl,null,payMethod,bizType,memberCache.getMobile(),map);
        PayVo payVo = new PayVo();
        payVo.setPayUrl(payUrl);
        payVo.setConsumerReminder(consumerReminder);


        this.saveOrderPayInfo(orderId,payUrl,order.getPaymentMethod());
        return payVo;
    }

    @Override
    public void receipt(Long orderId, Long chainId) {
        Order order = dao.queryById(orderId, Order.class);
        if (order == null) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_NON_EXISTENT);
        }
        if (!OrderEnum.S_OS_DELIVERED.value().equals(order.getStatus())) {
            throw new BizCoreRuntimeException(MaErrorConstants.WX_ORDER_STATUS_ERROR);
        }
        order.setStatus(OrderEnum.S_OS_DONE.value());
        order.setTradeSuccTime(DateFormatUtils.getNow());
        order.setReceiveTime(DateFormatUtils.getNow());
        dao.update(order);

        //发布订单交易成功事件
        //applicationEventPublisher.publishEvent(new OrderStatusChangeEvent(this, order));


    }

    @Override
    public OrderStatusCountVo queryOrderCountByMemberId(Long memberId, Long tenantId) {
        OrderStatusCountVo orderStatusCountVo = new OrderStatusCountVo();
        orderStatusCountVo.setDfhNum(queryOrderNum(memberId, OrderEnum.S_OS_UNDELIVERED.value(), tenantId));
        orderStatusCountVo.setDfkNum(queryOrderNum(memberId, OrderEnum.S_OS_UNPAID.value(), tenantId));
        orderStatusCountVo.setDshNum(queryOrderNum(memberId, OrderEnum.S_OS_DELIVERED.value(), tenantId));
        orderStatusCountVo.setRefundingCount(maOrderRefundService.queryRefundingCountByMemberId(memberId));
        return orderStatusCountVo;
    }

    /**
     * 检查购买的产品是否需要生成验证码
     *
     * @param spuType    产品类型
     * @param verifyType 验证类型
     * @return
     */
    private Boolean verificationCodeRequired(String spuType, String verifyType) {
        if (Objects.equals(spuType, ProductEnum.S_ST_SERVICE.value())
                && Objects.equals(verifyType, ProductEnum.S_VT_TO_STORE.value())) {
            return true;
        }
        return false;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 订单产品验证码
     *
     * @param orderId        订单id
     * @param orderProductId 订单产品id
     * @param num            购买数量
     * <AUTHOR>
     */
    @Override
    public void generateVerifyCode(Long orderId, Long orderProductId, Integer num, Long tenantId) {

        List<OrderProductVerify> orderProductVerifyList = new ArrayList<>();
        OrderProductVerify orderProductVerify = null;
        for (int i = 0; i < num; i++) {
            orderProductVerify = new OrderProductVerify();
            orderProductVerify.setOrderId(orderId);
            orderProductVerify.setTenantId(tenantId);
            orderProductVerify.setOrderProductId(orderProductId);
            orderProductVerify.setVerifyCode(1 + RandomStringUtils.randomNumeric(7));
            orderProductVerify.setIsAvailable(true);
            orderProductVerify.setDataStatus(true);
            orderProductVerify.setCreated(new Timestamp(System.currentTimeMillis()));
            orderProductVerifyList.add(orderProductVerify);
        }
        dao.batchSave(orderProductVerifyList, OrderProductVerify.class);

    }

    @Override
    public void cancelOrder(Long orderId) {
        Ssqb cancelOrder = Ssqb.create("com.lewei.eshop.ma.cancelOrder")
                .setParam("orderId", orderId);
        dao.updateByMybatis(cancelOrder);
        redisCacheProvider.getRedisTemplate().opsForZSet().remove(MaCacheKeys.ORDER_PAY_QUEUE_KEY, orderId);
        Order order = dao.queryById(orderId,Order.class);
        if (order != null) {
            if (Objects.equals(order.getOrderType(),OrderEnum.S_OOT_BOX.value())){

                JSONObject jsonObject = JSON.parseObject(order.getExtJson());
                Long boxItemId = jsonObject.getLong("boxItemId");
                if (boxItemId != null) {
                    redisCacheProvider.delWith(getBoxItemPayQueueKey(boxItemId,order.getMemberId()));
                }
            }
        }


    }

    @Override
    public void deleteOrder(Long orderId, Long memberId) {
        Ssqb deleteOrder = Ssqb.create("com.lewei.eshop.ma.deleteOrder")
                .setParam("orderId", orderId)
                .setParam("memberId", memberId);
       int l =  dao.updateByMybatis(deleteOrder);
       if (l < 1){
           throw new BizCoreRuntimeException(MaErrorConstants.ORDER_STATUS_ERROR_OR_ORDER_DELETE);
       }
    }

    @Override
    public Set<Long>  queryOrderPayQueueList() {
        Set<Long> orderIds =  redisCacheProvider.getRedisTemplate().opsForZSet().rangeByScore(MaCacheKeys.ORDER_PAY_QUEUE_KEY, 0, System.currentTimeMillis(), 0, -1);
        Set<Long> removeOrderIds = new HashSet<>();

        if (orderIds != null && !orderIds.isEmpty()) {
            for (Long orderId : orderIds) {
                Long l =   redisCacheProvider.getRedisTemplate().opsForZSet().remove(MaCacheKeys.ORDER_PAY_QUEUE_KEY, orderId);
                //移除失败 说明被消费 不补偿
                if (l == null || l ==0L){
                    removeOrderIds.add(orderId);
                }
            }
        }
        if (!removeOrderIds.isEmpty()){
            orderIds.removeAll(removeOrderIds);
        }
        return orderIds;
        }

    @Override
    public Map<String, Object> queryOrderStatus(Long orderId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", orderId))
                .and(Restrictions.eq("dataStatus", 1));
        Order order = dao.query(query, Order.class);
        if (order == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> map = new HashMap<>();
        map.put("status", order.getStatus());
        map.put("id", order.getId());

        return map;
    }

    @Override
    public Map<String, Object> queryOrderPayInfo(Long orderId) {
        Map<String, Object> hashMap = new HashMap<>();
        OrderPayInfo orderPayInfo = dao.queryById(orderId, OrderPayInfo.class);
        if (Objects.nonNull(orderPayInfo)) {
            hashMap.put("payInfo",orderPayInfo.getPayInfo());
        }
        return hashMap;
    }

    /**
     * 保存订单支付信息
     */
    @Override
    public void saveOrderPayInfo(Long orderId, String payInfo,String payType) {
        try{
            if(StringUtils.isNotBlank(payInfo) && OrderUtil.isCashPay(payType)){
                Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.order.insertOrUpdateOrderPayInfo")
                        .setParam("orderId", orderId)
                        .setParam("payInfo", payInfo);
                dao.updateByMybatis(ssqb);
            }
        }catch (Exception e){
            log.error("saveOrderPayInfo error",e);
        }

    }

    private Integer queryOrderNum(Long memberId, String status, Long tenantId) {
        Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.queryOrderCountByMemberId")
                .setParam("memberId", memberId)
                .setParam("tenantId", tenantId)
                .setParam("status", status);
        return dao.findForInt(ssqb);
    }
    @Override
    public void checkPickUpTime(Date pickupDate, Long tenantId) {
        SelfPickPointVo selfPickPointVo = maProductService.querySelfPickPoint(tenantId);
        System.out.println(selfPickPointVo.getPstTime());
        if (pickupDate != null && selfPickPointVo != null && BooleanUtils.isTrue(selfPickPointVo.getIsTiming())) {
            //首先校验是否在提取时间段之内
            Time pickupTime1 = new Time(pickupDate.getTime());
            if (!(compTime(pickupTime1, selfPickPointVo.getPstTime()) && compTime(selfPickPointVo.getPeTime(), pickupTime1))) {
                throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PICK_UP_TIME_ERROR);
            }

            long pickupTime = pickupDate.getTime();
            Date now = DateFormatUtils.getNow();
            Date aheadMaxDate = DateFormatUtils.addDate(timeToToday(selfPickPointVo.getPeTime()), selfPickPointVo.getAheadMax() - 1);
            //不大于最大提前时间
            if (pickupTime > aheadMaxDate.getTime()) {
                throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PICK_UP_TIME_ERROR);
            }
            // 0 表示无需提前预约 不需要处理
            if (selfPickPointVo.getAheadMin() > 0) {
                Integer aheadMinute = timeTypeConversion(selfPickPointVo.getAheadMin(), selfPickPointVo.getAheadMinType());
                if (Objects.equals(selfPickPointVo.getAheadMinType(), TimeTypeEnum.S_DT_DAY.value())) {
                    //为自然天，如：提前1天，则不管是凌晨1点还是晚上23点，都只能下明天以后的订单
                    if (pickupTime < timeToToday(selfPickPointVo.getPstTime()).getTime() + aheadMinute * 60 * 1000) {
                        throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PICK_UP_TIME_ERROR);
                    }
                } else {
                    //不小于最小提前时间
                    if (pickupTime < now.getTime() + aheadMinute * 60 * 1000) {
                        throw new BizCoreRuntimeException(MaErrorConstants.ORDER_PICK_UP_TIME_ERROR);
                    }
                }
            }
        }
    }

    /**
     * 时间类型转化成分钟
     *
     * @param time
     * @param timeType
     * @return
     */
    private Integer timeTypeConversion(Integer time, String timeType) {
        if (TimeTypeEnum.S_DT_DAY.value().equals(timeType)) {
            return time * 60 * 24;
        } else if (TimeTypeEnum.S_DT_HOUR.value().equals(timeType)) {
            return time * 60;
        } else if (TimeTypeEnum.S_DT_MINUTE.value().equals(timeType)) {
            return time;
        }
        return 0;
    }

    /**
     * 比较time类型大小
     * @param s1
     * @param s2
     * @return boolean
     */
    private boolean compTime(Time s1, Time s2) {
        String[] array1 = s1.toString().split(":");
        int total1 = Integer.valueOf(array1[0]) * 3600 + Integer.valueOf(array1[1]) * 60 + Integer.valueOf(array1[2]);
        String[] array2 = s2.toString().split(":");
        int total2 = Integer.valueOf(array2[0]) * 3600 + Integer.valueOf(array2[1]) * 60 + Integer.valueOf(array2[2]);
        return total1 - total2 >= 0 ? true : false;

    }
    // TODO: 2020/4/18  线上190发布成功 相关代码删除
    /**
     * 校验过渡版本
     *
     * @param maVersion 小程序版本
     * @return Boolean
     */
    private Boolean checkIsInterimVersion(String maVersion) {
        boolean flag = false;
        if (!StringUtil.isEmpty(maVersion)) {
            String regEx = "[^0-9]";
            Pattern p = Pattern.compile(regEx);
            Matcher m = p.matcher(maVersion);
            if (Objects.equals(sysConfig.getProjectProfile(), Constants.SYSTEM_MODEL_PRODUCT)) {
                flag = StringUtil.toInt(m.replaceAll("").trim(), 0) < 310;
            } else {
                flag = StringUtil.toInt(m.replaceAll("").trim(), 0) < 310;
            }
        }
        return flag;
    }


    /**
     * 将time 转换成今天 yyyy-MM-dd HH:mm:ss
     *
     * @param time
     * @return
     */
    private Date timeToToday(Time time) {

        String todayStr = DateFormatUtils.formatDate(globalHandler.getChainLocalDate(), "yyyy-MM-dd");
        todayStr = todayStr + " " + time.toString();
        return DateFormatUtils.formatDateStr(todayStr, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 处理订单类型
     *
     * @param order       订单
     * @param openId      会员openId
     * @param inviterCode 邀请人code
     */
    private void handleOrderBizType(Order order, String openId, String inviterCode) {
        if (OrderUtil.isMoneyPay(order.getPaymentMethod())){
            if (StringUtils.isNotEmpty(inviterCode)) {
                order.setOrderBizType(OrderBizTypeEnum.S_OBZ_DIST.value());
                return;
            }
            MaMemberDetailVo memberDetailVo = memberService.querySimpleProxyByOpenId(openId);
            if (memberDetailVo != null) {
                if (StringUtils.isNotEmpty(memberDetailVo.getInviterCode())) {
                    order.setOrderBizType(OrderBizTypeEnum.S_OBZ_DIST.value());
                    return;
                }
                if (memberDetailVo.getProxy() != null) {
                    if (Objects.equals(memberDetailVo.getProxy().getMemberType(), MemberTypeEnum.S_MT_PROXY.value())) {
                        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_DIST.value());
                        return;
                    }
                    if (StringUtils.isNotEmpty(memberDetailVo.getProxy().getParentUserCode())) {
                        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_DIST.value());
                        return;
                    }
                }
            }
        }


        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_ESHOP.value());
    }

    /**
     * 处理自购返佣金额
     */
    private void handleCalcProfitAmount(List<PrePayVO> prePayVOS, String userCode) {
        CalcProfitAmountRequest request = new CalcProfitAmountRequest();
        request.setUserCode(userCode);
        List<CalcProfitAmountRequest.Goods> goodsList = new ArrayList<>();
        CalcProfitAmountRequest.Goods good;
        for (PrePayVO prePayVO : prePayVOS) {
            good = new CalcProfitAmountRequest.Goods();
            good.setGoodsId(prePayVO.getParentSpuId() != null ? prePayVO.getParentSpuId() : prePayVO.getSpuId() );
            good.setOriginalPrice(prePayVO.getOriginalPrice());
            good.setSkuId(prePayVO.getSkuId());
            good.setSpuType(prePayVO.getSpuType());
            goodsList.add(good);
        }
        request.setGoods(goodsList);
        List<CalcProfitAmountResponse> calcProfitAmountResponses = feignClient.calcProfitAmountByGoodsId(request);
        if (ListUtil.isNotEmpty(calcProfitAmountResponses)) {
            Map<Long, CalcProfitAmountResponse> skuId2ResponseMap = calcProfitAmountResponses.stream().collect(Collectors.toMap(CalcProfitAmountResponse::getSkuId, c -> c));
            prePayVOS.forEach(a -> {
                CalcProfitAmountResponse response = skuId2ResponseMap.get(a.getSkuId());
                if (response != null) {
                    BigDecimal selfBuyProfitAmount = response.getSelfBuyProfitAmount() == null ? BigDecimal.ZERO : response.getSelfBuyProfitAmount();
                    a.setSelfBuyProfitAmount(selfBuyProfitAmount);
                    a.setPriceFee(a.getOriginalPrice().subtract(selfBuyProfitAmount));
                }
            });
        }
    }

    private void saveReductionGift(CalculateReductionFeeVo calculateReductionFeeVo, Order order) {
        if (calculateReductionFeeVo != null && BooleanUtils.isTrue(calculateReductionFeeVo.getIsReduced())) {
            ReductionGift gift = new ReductionGift();
            gift.setActivityId(calculateReductionFeeVo.getActivityId());
            gift.setRuleId(calculateReductionFeeVo.getRuleId());
            gift.setMemberId(order.getMemberId());
            gift.setOrderId(order.getId());
            gift.setAmount(calculateReductionFeeVo.getAmount());
            gift.setReductionFee(calculateReductionFeeVo.getReductionFee());
            gift.setGiftExtJson(calculateReductionFeeVo.getGiftExtJson());
            gift.setCreated(DateFormatUtils.getNow());
            dao.save(gift);
        }
    }

    private void callPayApi(String payType, Order order, MemberCache member, Long chainId, Long tenantId, String appId, SaveOrderVO returnVo, Long boxItemId, Long stageId, Integer num, String returnUrl, String payMethod, Long memberBankId, PrePayAddressRequest addressRequest) {
        if (OrderUtil.isCashPay(order.getPaymentMethod())) {

            String biType = "";
            if (OrderEnum.S_OOT_BOX.value().equals(order.getOrderType())){
                biType = "default";
            }else {
                biType = "mall";
            }
            Map<String, Object> map = new HashMap<>();
            map.put("memberBankId", memberBankId);
            map.put("orderId", order.getId());
            if (StringUtils.isNotEmpty(order.getShippingUser()) && addressRequest != null){
                map.put("shippingUser", order.getShippingUser());
                map.put("telNumber", order.getTelNumber());
                map.put("province", addressRequest.getProvince());
                map.put("city", addressRequest.getCity());
                map.put("area", addressRequest.getArea());
                map.put("address", addressRequest.getAddress());
            }
            //调取微信支付
            String payUrl = orderPayService.callCashPayApi(order.getOrderTitle(), order.getPaymentMoney(),
                    order.getOrderSn(), member.getOpenId(), chainId, appId,payType,callBackUrl,returnUrl,payMethod,biType,member.getMobile(),map);
            returnVo.setPayUrl(payUrl);

            this.saveOrderPayQueue(order.getId());
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(payType)) {
            if (order.getPaymentMoney().compareTo(BigDecimal.ZERO) > 0){
                memberFeignClient.memberBalancePay(new MemberBalancePayRequest(tenantId, member.getId(), order.getPaymentMoney(), order.getOrderTitle(), order.getId()));
            }
            this.afterPay(order);
        }else if(OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(payType)){
            //暂时定为100积分抽一次
            memberFeignClient.consumeMemberScore(new ConsumeScoreRequest(member.getId(), ScoreTaskEnum.S_SST_SCORE_PAY.value(), order.getPayScore(), order.getOrderTitle(), ScoreSourceTypeEnum.MA.value()));
            this.afterPay(order);
        }else if(OrderPaymentTypeEnum.S_OPM_FRACTION.value().equals(payType)){
            //积分2 支付
            memberFeignClient.consumeMemberFraction(new ConsumeFractionRequest(member.getId(), ScoreTaskEnum.S_SST_SCORE_PAY.value(), order.getPayFraction(), order.getOrderTitle(),"ma"));
            this.afterPay(order);
        }else if(OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(payType)){
            //闯关票 支付
            MemberBoxTicketReq ticketReq = new MemberBoxTicketReq();
            ticketReq.setBoxItemId(boxItemId);
            ticketReq.setStageId(stageId);
            ticketReq.setMemberId(member.getId());
            ticketReq.setPlId(order.getId());
            ticketReq.setTitle(order.getOrderTitle());
            ticketReq.setType(MemberBoxTicketTypeEnum.consume.value());
            ticketReq.setQuantity(num);
            memberFeignClient.handelMemberBoxTicket(ticketReq);
            this.afterPay(order);
        }else if(OrderPaymentTypeEnum.S_OPM_TB_TICKET.value().equals(payType)){
            //淘宝票 支付
            Ssqb queryBoxTbTicket = Ssqb.create("com.lewei.eshop.ma.queryBoxTbTicket")
                    .setParam("boxItemId",boxItemId);
            String tbSkuId = dao.findForObj(queryBoxTbTicket,String.class);
            if (StringUtils.isEmpty(tbSkuId)){
                throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
            }
            MemberTbTicketRequest request = new MemberTbTicketRequest();
            request.setMemberId(member.getId());
            request.setQuantity(num);
            request.setTitle(order.getOrderTitle());
            request.setType(MemberTbTicketTypeEnum.consume.value());
            request.setPlId(order.getId().toString());
            request.setTbSkuId(tbSkuId);
            memberFeignClient.handelMemberTbTicket(request);

            this.afterPay(order);
        }else{
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }
    }

    public void afterPay(Order order) {

        String info = BizMessageSource.getInstance().getMessage("cem40038") + taskExecutor.getThreadNamePrefix() +
                BizMessageSource.getInstance().getMessage("cem40039") + taskExecutor.getThreadPoolExecutor().getTaskCount() +
                BizMessageSource.getInstance().getMessage("cem40040") + taskExecutor.getThreadPoolExecutor().getCompletedTaskCount() +
                BizMessageSource.getInstance().getMessage("cem40041") + taskExecutor.getThreadPoolExecutor().getActiveCount() +
                BizMessageSource.getInstance().getMessage("cem40042") + taskExecutor.getThreadPoolExecutor().getQueue().size();

        log.info(info);

        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("orderCode", order.getOrderSn());
                payCallbackProcessorV2.onPayTradeSuccess(order,jsonObject);
            }
        });
    }

    private void validateSecKill(BuyNowRequest request, Long memberId, Long parentSkuId) {
        if (request.getSecKillId() != null && request.getSecKillId() != 0L) {
            SecKillActivityDetailVo detailVo = appFeignClient.querySecKillActivityDetailVo(request.getSecKillId());
            if (detailVo == null) {
                throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40043"));
            }
            if (!Objects.equals(ActivityStatusEnum.S_MAT_JXZ.value(), detailVo.getActivityStatus())) {
                throw new BizCoreRuntimeException(MaErrorConstants.SEC_KILL_ACTIVITY_NOT_BG_OR_END);
            }
            if (request.getMemberCouponId() != null || request.getPayScore() != null) {
                throw new BizCoreRuntimeException(MaErrorConstants.SEC_KILL_ACTIVITY_CANNOT_COEXIST_WITH_OTHER_ACTIVITIES);
            }

            //库存校验
            //mapping >> <sku,SecKillActivityDetailVo.SecKillSKuVo>
            Map<Long, SecKillActivityDetailVo.SecKillSkuVo> mapping = detailVo.getSecKillSkuVos().stream().collect(Collectors.toMap(SecKillActivityDetailVo.SecKillSkuVo::getParentSkuId, c -> c));
            SecKillActivityDetailVo.SecKillSkuVo secKillSKuVo = mapping.get(parentSkuId);
            if (secKillSKuVo == null) {
                throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40044"));
            }
            if (BooleanUtils.isTrue(detailVo.getIsLimit())) {
                //查询会员购买秒杀产品次数
                Ssqb queryMemberBuySecKillProductNum = Ssqb.create("com.lewei.eshop.ma.queryMemberBuySecKillProductNum")
                        .setParam("memberId", memberId)
                        .setParam("secKillId", request.getSecKillId());
                Integer num = dao.findForObj(queryMemberBuySecKillProductNum, Integer.class);
                if ((num + request.getNum()) > detailVo.getLimitNum()) {
                    throw new BizCoreRuntimeException(MaErrorConstants.SEC_KILL_PRODUCT_LIMIT_BUY, detailVo.getLimitNum());
                }
            }
        }
    }

    private void validateBoxItem(BuyNowRequest request, Long memberId) {
        if (request.getBoxItemId() != null && request.getBoxItemId() != 0L) {
            // 现金抽赏笔数 日上限验证
            // 1.校验是否超过日上限
            if (OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(request.getPaymentMethod()) || OrderPaymentTypeEnum.S_OPM_ALIPAY.value().equals(request.getPaymentMethod())) {
                SaasQueryBuilder queryConfig = SaasQueryBuilder.where(Restrictions.eq("application", "bindBox-limitNumSetting"));
                ApplicationConfig config = dao.query(queryConfig, ApplicationConfig.class);
                if (config != null && BooleanUtils.isTrue(config.getIsMaShow()) && StringUtils.isNotEmpty(config.getExtJson())) {
                    // 2.查询日交易笔数
                    SaasQueryBuilder saasQueryBuilder = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                            .and(Restrictions.eq("memberId", memberId))
                            .and(Restrictions.ge("storageDataTime", DateZonetimeUtils.formatDate(DateFormatUtils.getNow(), "yyyy-MM-dd" ,globalHandler.getGlobalData().getZone())));
                    MemberRecord memberRecord = dao.query(saasQueryBuilder, MemberRecord.class);
                    if (memberRecord != null) {
                        Integer limitNum = JSON.parseObject(config.getExtJson()).getInteger("limitNum");
                        if (memberRecord.getRewardCashTimes() >= limitNum) {
                            throw new BizCoreRuntimeException(MaErrorConstants.CASH_TIMES_EXCEED_TODAY);
                        }
                    }
                }
            }
            MaBoxItemSimplelVo boxItem = productBoxService.queryBoxItemSimpleVo(request.getBoxItemId());
            if (boxItem == null) {
                throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40034"));
            }
            if (ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType()) && (StringUtils.isBlank(boxItem.getBoxPrizeMode())
                    || boxItem.getBoxPrizeMode().equals(ProductBoxPrizeModeTypeEnum.S_BPM_NUM.value()))
                    || ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType())){
                if (request.getNum() > boxItem.getLeftRewardNum()){
                    throw new BizCoreRuntimeException(MaErrorConstants.BOX_ITEM_LEFT_REWARD_NOT_ENOUGH);
                }
            }
            if (ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType()) && StringUtils.isNotBlank(boxItem.getBoxPrizeMode())
                    && boxItem.getBoxPrizeMode().equals(ProductBoxPrizeModeTypeEnum.S_BPM_ODDS.value())){
                if (request.getNum() > boxItem.getLeftRewardNum()){
                    throw new BizCoreRuntimeException(MaErrorConstants.BOX_ITEM_LEFT_REWARD_NOT_ENOUGH);
                }
            }
            if(!Objects.equals(BoxItemSaleStatusEnum.S_PBSS_ON_SHELF.value(),boxItem.getSaleStatus())){
                throw new BizCoreRuntimeException(MaErrorConstants.WX_BE_TAKEN_OFF);
            }
            if (BooleanUtils.isNotTrue(request.getIsAllBuy()) && BooleanUtils.isTrue(boxItem.getIsIndependentLottery()) && request.getNum() > 1){
                throw new BizCoreRuntimeException(MaErrorConstants.BOX_ITEM_CAN_ONLY_BUY_ONE_ONE_TIME);
            }
            if (BooleanUtils.isTrue(request.getIsAllBuy())){
                if (ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType()) || ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType())){
                    if (boxItem.getLeftRewardNum() > boxItem.getAllBuyNum()){
                        throw new BizCoreRuntimeException(MaErrorConstants.BOX_ITEM_NOT_APPLY_ALL_BUY_CONDITIONS,boxItem.getAllBuyNum());
                    }
                }
                if (ProductBoxTypeEnum.S_BT_UN_LIMIT.value().equals(boxItem.getBoxType()) ||  ProductBoxTypeEnum.S_BT_CARD.value().equals(boxItem.getBoxType()) || (ProductBoxTypeEnum.S_BT_CONCATENATE.value().equals(boxItem.getBoxType())) ){
                    throw new BizCoreRuntimeException(MaErrorConstants.BOX_ITEM_CANT_BUY_ALL);
                }
            }

            Boolean  flag  =  redisCacheProvider.getRedisTemplate().hasKey(this.getBoxItemPayQueueKey(request.getBoxItemId(),memberId));
            if (BooleanUtils.isTrue(flag)){
                throw new BizCoreRuntimeException(MaErrorConstants.BUY_BOX_IS_PROCESSING);
            }else {
                this.saveBoxItemPayQueue(request.getBoxItemId(),memberId);
            }

            if ( ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType())){
                if (BoxSequenceTypeEnum.S_BST_ASSIGN.value().equals(boxItem.getSequenceType())){
                    if (ListUtil.isEmpty(request.getSerialNums())){
                        throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUMBER_NOT_FOUND);
                    }
                    if (request.getSerialNums().size() != request.getNum()){
                        throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUMBER_NOT_MATCH);
                    }
                    List<Integer> disSerialNums = request.getSerialNums().stream().distinct().collect(Collectors.toList());
                    if (disSerialNums.size() < request.getSerialNums().size()){
                        throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUMBER_CAN_NOT_REPEAT);
                    }

                    for (Integer serialNum : request.getSerialNums()) {
                        if (serialNum < 1 || serialNum > boxItem.getRewardNum()){
                            throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUMBER_RANGE_ERROR);
                        }
                    }
                    SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItem.getBoxItemId()))
                            .and(Restrictions.in("serialNum",request.getSerialNums()))
                            .and(Restrictions.eq("isGift",0))
                            .and(Restrictions.ne("memberId",0))
                            ;
                    List<ProductBoxItemRewardTmpl> tmpls = dao.queryList(query,ProductBoxItemRewardTmpl.class);
                    if (ListUtil.isNotEmpty(tmpls)){
                        List<Integer> errNums = tmpls.stream().map(ProductBoxItemRewardTmpl::getSerialNum).collect(Collectors.toList());
                        String errNumStr = Joiner.on(",").skipNulls().join(errNums);
                        throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUMBER_HAVE_BEEN_OCCUPY,errNumStr);
                    }

                }
            }

            if (OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(request.getPaymentMethod())
                    && !ProductBoxTypeEnum.S_BT_CONCATENATE.value().equals(boxItem.getBoxType())
                    && !ProductBoxTypeEnum.S_BT_UP.value().equals(boxItem.getBoxType())){
                throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
            }

            if (ProductBoxTypeEnum.S_BT_CONCATENATE.value().equals(boxItem.getBoxType())){
                if (request.getStageId() == null){
                    throw new BizCoreRuntimeException(MaErrorConstants.CONCATENATE_BOX_STAGEID_IS_NULL);
                }
                if (request.getStageId().compareTo(1L) != 0){
                    if (!OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(request.getPaymentMethod())){
                        throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
                    }
                }else {
                    if (OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(request.getPaymentMethod())){
                        throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
                    }
                }
            }

            if (ProductBoxTypeEnum.S_BT_ADVENTURE.value().equals(boxItem.getBoxType())){
                if (request.getStageId() == null){
                    throw new BizCoreRuntimeException(MaErrorConstants.CONCATENATE_BOX_STAGEID_IS_NULL);
                }
                //查询所在关卡数
                SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItem.getBoxItemId()))
                        .and(Restrictions.eq("memberId",memberId))
                        ;
                MemberBoxStage boxStage = dao.query(query, MemberBoxStage.class);
                if (boxStage == null) {
                    boxStage = new MemberBoxStage();
                    boxStage.setStageId(1L);
                    boxStage.setMemberId(memberId);
                    boxStage.setBoxItemId(boxItem.getBoxItemId());
                    boxStage.setCreated(DateFormatUtils.getNow());
                    dao.save(boxStage);
                }
                if (request.getStageId().compareTo(boxStage.getStageId()) != 0){
                    throw new BizCoreRuntimeException(MaErrorConstants.ADVENTURE_BOX_STAGE_NOT_MATCH);
                }
            }

            //优惠券放开抽奖发数使用限制，3发、10发可自己控制是否使用
            if (request.getMemberCouponId() != null) {
                MemberCoupon memberCoupon = dao.queryById(request.getMemberCouponId(), MemberCoupon.class);
                if (memberCoupon != null){
                    if (BooleanUtils.isTrue(request.getIsAllBuy()) ){
                        if ( !memberCoupon.getLotteryNumType().contains(LotteryNumTypeEnum.S_CLNT_ALL.value())){
                            throw new BizCoreRuntimeException(MaErrorConstants.THIS_COUPON_NOT_USED_FOR_ALL_BUY);
                        }
                    } else if (request.getNum() == 1 ) {
                        if (!memberCoupon.getLotteryNumType().contains(LotteryNumTypeEnum.S_CLNT_ONE.value())){
                            throw new BizCoreRuntimeException(MaErrorConstants.THIS_COUPON_NOT_USED_FOR_ONE);
                        }
                    }else if (request.getNum() == 3  ){
                        if(!memberCoupon.getLotteryNumType().contains(LotteryNumTypeEnum.S_CLNT_THREE.value())){
                            throw new BizCoreRuntimeException(MaErrorConstants.THIS_COUPON_NOT_USED_FOR_THREE);
                        }
                    }else if (request.getNum() == 10 ){
                        if(!memberCoupon.getLotteryNumType().contains(LotteryNumTypeEnum.S_CLNT_TEN.value())){
                            throw new BizCoreRuntimeException(MaErrorConstants.THIS_COUPON_NOT_USED_FOR_TEN);
                        }
                    }else if (request.getNum() == 5 ){
                        if(!memberCoupon.getLotteryNumType().contains(LotteryNumTypeEnum.S_CLNT_FIVE.value())){
                            throw new BizCoreRuntimeException(MaErrorConstants.THIS_COUPON_NOT_USED_FOR_FIVE);
                        }
                    }
                }
            }

            // UP赏盲盒
            if (ProductBoxTypeEnum.S_BT_UP.value().equals(boxItem.getBoxType())){
                Long stageId = 1L;
                List<MemberBoxTicketVo> memberBoxTicketVos = memberFeignClient.queryMemberBoxStage(request.getBoxItemId(), memberId);
                if(ListUtil.isNotEmpty(memberBoxTicketVos)){
                    if(memberBoxTicketVos.size() != 1){
                        throw new BizCoreRuntimeException(MaErrorConstants.UP_BOX_STAGE_DATA_ERROR);
                    }
                    stageId = memberBoxTicketVos.get(0).getStageId();
                }

                if(!Objects.equals(request.getStageId(), stageId)){
                    throw new BizCoreRuntimeException(MaErrorConstants.UP_BOX_STAGE_ERROR);
                }
                // 验证第一关参数
                if(Objects.equals(request.getStageId(), 1L)){
                    // 验证选择奖励
                    if(request.getSelectSkuId() == null){
                        throw new BizCoreRuntimeException(MaErrorConstants.UP_BOX_STAGE_ONE_ERROR);
                    }
                    // 验证选择是否变更
                    List<BoxItemRewardListVo> boxItemRewardListVos = productBoxService.queryBoxItemRewardListVo(request.getBoxItemId(), stageId);
                    boolean isExist = boxItemRewardListVos.stream().anyMatch(a -> Objects.equals(a.getSkuItemId(), request.getSelectSkuId()));
                    if(!isExist){
                        throw new BizCoreRuntimeException(MaErrorConstants.UP_BOX_SELECT_ERROR);
                    }
                    // 验证开奖次数
                    JSONArray jsonArray = JSONObject.parseArray(boxItem.getStageJson());
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if (Objects.equals(jsonObject.getLong("stageId"), 1L)) {
                            if(!Objects.equals(jsonObject.getInteger("openNum"), request.getOpenNum())){
                                throw new BizCoreRuntimeException(MaErrorConstants.UP_BOX_OPEN_NUM_ERROR);
                            }
                            break;
                        }
                    }

                }else if(Objects.equals(request.getStageId(), 3L)){
                    // 验证第三关参数
                    if(request.getSubBoxItemId() == null || CollectionUtils.isEmpty(request.getSerialNums())){
                        throw new BizCoreRuntimeException(MaErrorConstants.UP_BOX_STAGE_THREE_ERROR);
                    }
                    // 验证是否序号是否被占
                    SaasQueryBuilder queryTmpl = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItem.getBoxItemId()))
                            .and(Restrictions.eq("subBoxItemId",request.getSubBoxItemId()))
                            .and(Restrictions.in("serialNum",request.getSerialNums()))
                            .and(Restrictions.ne("memberId",0))
                            ;
                    List<ProductBoxUpItemRewardTmpl> tmpls = dao.queryList(queryTmpl, ProductBoxUpItemRewardTmpl.class);
                    if (ListUtil.isNotEmpty(tmpls)){
                        List<Integer> errNums = tmpls.stream().map(ProductBoxUpItemRewardTmpl::getSerialNum).collect(Collectors.toList());
                        String errNumStr = Joiner.on(",").skipNulls().join(errNums);
                        throw new BizCoreRuntimeException(MaErrorConstants.SERIAL_NUMBER_HAVE_BEEN_OCCUPY,errNumStr);
                    }
                }
            }
        }


    }


    private ScorePayConfig queryScorePayConfig(Long spuId){
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus",1))
                .and(Restrictions.eq("spuId",spuId));
        return dao.query(query,ScorePayConfig.class);
    }

    private FractionPayConfig queryFractionPayConfig(Long spuId){
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus",1))
                .and(Restrictions.eq("spuId",spuId));
        return dao.query(query, FractionPayConfig.class);
    }


    private void validateService(BuyNowRequest request,ProductSpu spu, Long memberId) {

        if (ProductEnum.S_ST_SERVICE.value().equals(spu.getSpuType())){
            if (request.getNum() != 1){
                throw new BizCoreRuntimeException(MaErrorConstants.SERVICE_CAN_ONLY_ONE);
            }
            SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("memberId",memberId))
                    .and(Restrictions.eq("spuId",spu.getId()))
                    .and(Restrictions.eq("serviceStatus", MemberServiceStatusEnum.S_MS_SXZ.value()));
            Integer l = dao.queryForInt(query, MemberService.class);
            if (l > 0){
                throw new BizCoreRuntimeException(MaErrorConstants.SERVICE_ALREAY_BUY);
            }
        }
    }
    /**
     * 控奖验证
     * @param request               请求对象
     * @param boxItem               套盒信息
     * @param memberId              会员id
     * @return
     */
    private Boolean validateBuyBox(PrePayBuyNowRequest request, MaBoxItemSimplelVo boxItem, Long memberId) {
        Long boxItemId = boxItem.getBoxItemId();
        Long spuId = boxItem.getSpuId();
        //购买次数验证
        Integer allReadyBuyCount = 0;
        Ssqb queryOrderBoxItemMemberCountReward = null;

        if(ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxItem.getBoxType()) ) {
            //查询玩家已抽个数(抢车位)
            queryOrderBoxItemMemberCountReward = Ssqb.create("com.lewei.eshop.ma.queryProductBoxItemMemberCountReward")
                    .setParam("memberId", memberId)
                    .setParam("boxItemId", boxItemId);
            allReadyBuyCount = dao.findForInt(queryOrderBoxItemMemberCountReward);
        }

        if(ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType()) ) {
            //查询玩家已抽个数
            queryOrderBoxItemMemberCountReward = Ssqb.create("com.lewei.eshop.ma.queryOrderBoxItemMemberCountReward")
                    .setParam("memberId", memberId)
                    .setParam("boxItemId", boxItemId);
            allReadyBuyCount = dao.findForInt(queryOrderBoxItemMemberCountReward);
        }

        // TODO: 2021/11/24  限制次数验证
        if (boxItem.getLimitNum() != null && boxItem.getLimitNum() > 0) {
            if (allReadyBuyCount >= boxItem.getLimitNum()) {
                throw new BizCoreRuntimeException(MaErrorConstants.LIMIT_NUM_ERROR, boxItem.getLimitNum());
            }
        }

        BoxRewardConfig boxRewardConfig = productBoxService.queryControlPrizeConfig();
        //如果是 已开启状态
        if (Objects.nonNull(boxRewardConfig) && BooleanUtils.isTrue(boxRewardConfig.getIsOpen())) {
            if (BoxConfigEnum.S_CRT_ALL.value().equals(boxRewardConfig.getApplyType())) {
                //当前已抽个数
                Integer currentRewardNum = boxItem.getRewardNum() - boxItem.getLeftRewardNum();
                //设定个数
                BigDecimal controlPercentCount = BigDecimal.valueOf(boxItem.getRewardNum())
                        .multiply(boxRewardConfig.getPercentage().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
                //当前已抽个数 大于 设定个数 时
                if (StringUtils.isNotBlank(boxRewardConfig.getLotteryJson()) && BigDecimal.valueOf(currentRewardNum).compareTo(controlPercentCount) >= 0) {
                    if (ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType())) {
                        //查询限制内 抽取次数
                        int controlPercentNum = controlPercentCount.intValue();
                        queryOrderBoxItemMemberCountReward.setParam("controlPercentNum", controlPercentNum);
                        allReadyBuyCount = dao.findForInt(queryOrderBoxItemMemberCountReward);
                    }

                    //如果 当前玩家在本套中 抽奖次数 小于 设置次数
                    if (allReadyBuyCount <= boxRewardConfig.getLotteryNum()) {
                        JSONArray limitBuyCounts = JSON.parseArray(boxRewardConfig.getLotteryJson());
                        for (int i = 0; i < limitBuyCounts.size(); i++) {
                            //配置内的 json
                            Integer limitBuyCount = limitBuyCounts.getInteger(i);
                            if (BooleanUtils.isTrue(request.getIsAllBuy()) && limitBuyCount == -1) {
                                limitBuyCount = 0;
                            }
                            if (request.getNum().equals(limitBuyCount)) {
                                String[] errorMessage = getBoxControlErrorMessage(boxRewardConfig, limitBuyCount, allReadyBuyCount);
                                throw new BizCoreRuntimeException(MaErrorConstants.BOX_IS_PROTECTED, errorMessage[0], errorMessage[1], errorMessage[2]);

                            }
                        }
                    }
                }
            } else {
                if (StringUtils.isNotEmpty(boxRewardConfig.getSpuIds()) && boxRewardConfig.getSpuIds().contains(spuId.toString())) {
                    //当前已抽个数
                    Integer currentRewardNum = boxItem.getRewardNum() - boxItem.getLeftRewardNum();
                    //设定个数
                    BigDecimal controlPercentCount = BigDecimal.valueOf(boxItem.getRewardNum())
                            .multiply(boxRewardConfig.getPercentage().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
                    //当前已抽个数 大于 设定个数 时
                    if (StringUtils.isNotBlank(boxRewardConfig.getLotteryJson()) && BigDecimal.valueOf(currentRewardNum).compareTo(controlPercentCount) >= 0) {
                        if (ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxItem.getBoxType())) {
                            //查询限制内 抽取次数
                            int controlPercentNum = controlPercentCount.intValue();
                            queryOrderBoxItemMemberCountReward.setParam("controlPercentNum", controlPercentNum);
                            allReadyBuyCount = dao.findForInt(queryOrderBoxItemMemberCountReward);
                        }

                        //如果 当前玩家在本套中 抽奖次数 小于 设置次数
                        if (allReadyBuyCount <= boxRewardConfig.getLotteryNum()) {
                            JSONArray limitBuyCounts = JSON.parseArray(boxRewardConfig.getLotteryJson());
                            for (int i = 0; i < limitBuyCounts.size(); i++) {
                                //配置内的 json
                                Integer limitBuyCount = limitBuyCounts.getInteger(i);
                                if (BooleanUtils.isTrue(request.getIsAllBuy()) && limitBuyCount == -1) {
                                    limitBuyCount = 0;
                                }
                                if (request.getNum().equals(limitBuyCount)) {
                                    String[] errorMessage = getBoxControlErrorMessage(boxRewardConfig, limitBuyCount, allReadyBuyCount);
                                    throw new BizCoreRuntimeException(MaErrorConstants.BOX_IS_PROTECTED, errorMessage[0], errorMessage[1], errorMessage[2]);
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    private String[] getBoxControlErrorMessage(BoxRewardConfig boxRewardConfig, Integer limitBuyCount, Integer allReadyBuyCount) {
        //系统已开启用户保护模式，套盒前{0}数量内，检查到您没有消费记录，无法购买{2}
        //系统已开启用户保护模式，套盒前{0}数量内，检查到购买数量小于1次，无法购买{2}
        String[] errorMessage = new String[3];
        String strPercent = boxRewardConfig.getPercentage().setScale(0, BigDecimal.ROUND_HALF_UP).toString() + "%";
        String strLimitBuy = limitBuyCount + BizMessageSource.getInstance().getMessage("cem40045");
        if(limitBuyCount == 0) {
            strLimitBuy = BizMessageSource.getInstance().getMessage("cem40046");
        }
        errorMessage[0] = strPercent;
        if(allReadyBuyCount > 0) {
            errorMessage[1] = BizMessageSource.getInstance().getMessage("cem40047") + boxRewardConfig.getLotteryNum() + BizMessageSource.getInstance().getMessage("cem40045");
        } else {
            errorMessage[1] = BizMessageSource.getInstance().getMessage("cem40048");
        }
        errorMessage[2] = strLimitBuy;
        return errorMessage;
    }

    /**
     * 订单支付队列 延迟10分钟
     * @param orderId
     */
    private void saveOrderPayQueue(Long orderId){
        redisCacheProvider.getRedisTemplate().opsForZSet().add(MaCacheKeys.ORDER_PAY_QUEUE_KEY,orderId,System.currentTimeMillis() + 1000*60*10);
    }

    /**
     * 盒子支付队列  10s 过期
     * @param boxItemId 盒子id
     * @param memberId 会员id
     */
    private void saveBoxItemPayQueue(Long boxItemId,Long memberId){
        if (boxItemId != null && memberId != null){
            String key = this.getBoxItemPayQueueKey(boxItemId,memberId);
            redisCacheProvider.set(key,"",10L);

        }
    }

    private String getBoxItemPayQueueKey(Long boxItemId,Long memberId){
        return MaCacheKeys.BOX_ITEM_PAY_QUEUE_KEY + boxItemId + "@" + memberId;
    }


    private void validateLockBox(Long boxItemId, Long memberId){

        Map map = redisCacheProvider.getFromJson(MaCacheKeys.LOCK_BOX_QUEUE_KEY+boxItemId,Map.class);
        if (map != null) {
            if (!Objects.equals(memberId,map.get("memberId"))){
                throw new BizCoreRuntimeException(MaErrorConstants.BOX_IS_LOCKING_BY_OTHER);
            }
        }
    }


    private Long getTodayTemplate(){
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTimeInMillis();
    }

    private void checkCommodityLimitation(List<CommodityLimitationConfigVo.CommodityLimitationConfigSpuVo> spuVo
                                        ,List<OrderProduct> orderProducts,Long memberId,Integer configTotal){
        Integer memberTotal = 0;
        Integer total = 0;
        List<Long> spuIds = spuVo.stream()
                .map(p -> p.getSpuId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(spuIds)) {
            for (CommodityLimitationConfigVo.CommodityLimitationConfigSpuVo a : spuVo) {
              if (CollectionUtils.isNotEmpty(orderProducts)){
                 for (OrderProduct c:orderProducts){
                     ProductSpu spu = dao.queryById(c.getSpuId(), ProductSpu.class);
                    if (null != spu.getParentId() && a.getSpuId().equals(spu.getParentId())){
                        log.info("当前spuId是" + spu.getParentId());
                        Object num = redisCacheProvider.getRedisTemplate().opsForHash().get(MaCacheKeys.MEMBER_PRODUCT_QUEUE_KEY,
                                memberId.toString() + a.getSpuId().toString());
                        log.info("当前用户编号" + memberId + "spuId是" + a.getSpuId() + "当前商品购买次数是" + num);
                        if (num != null) {
                            Integer nums = (Integer) num;
                            memberTotal = memberTotal + nums;
                            log.info("当前用户购买次数是" + memberTotal);
                        }
                        total = total + c.getQuantity();
                        log.info("当前用户购买total是" + total);
                        break;
                    }
                 }
              }
            }
            memberTotal = memberTotal + total;
            if (memberTotal > configTotal) {
                throw new BizCoreRuntimeException(MaErrorConstants.COMMODITY_LIMITATION_ERROR, configTotal);
            }
        }
    }

    private  void checkCrowd(Long plId,Integer num,BigDecimal orderMoney,Long spuId,String spuName,Long memberId) {
//        SaasQueryBuilder queryProductSkuService = SaasQueryBuilder.where(Restrictions.eq("memberId", memberId))
//        .and(Restrictions.eq("crowdId", plId));
//        CrowdOrderRef crowdOrderRef = dao.query(queryProductSkuService, CrowdOrderRef.class);
//        if (null != crowdOrderRef) {
//            throw new BizCoreRuntimeException(MaErrorConstants.CROWDFUNDING_ORDER_EXIST);
//        }
        CrowdFundingVO crowdFundingVO = appFeignClient.queryDetail(plId,null);
        if (null == crowdFundingVO) {
            throw new BizCoreRuntimeException(MaErrorConstants.CROWD_FUNDING_HAS_BEEN_DELETE);
        }
        if (!crowdFundingVO.getStatus().equals(CrowdFundingStatusEnum.S_CFS_ZCZ.value())){
            throw new BizCoreRuntimeException(MaErrorConstants.CROWDFUNDING_STATUS_ERROR);
        }
        if (CollectionUtils.isEmpty(crowdFundingVO.getProductVOList())){
            throw new BizCoreRuntimeException(MaErrorConstants.CROWDFUNDING_PRODUCT_NOT_EXIST);
        }
        List<Long> spuIds =  crowdFundingVO.getProductVOList().stream().map(CrowdFundingVO.ProductVO :: getSpuId).collect(Collectors.toList());
        if (!spuIds.contains(spuId)){
            throw new BizCoreRuntimeException(MaErrorConstants.CROWDFUNDING_PRODUCT_NOT_EXIST);
        }
        List<CrowdFundingVO.ProductVO> productVOList = crowdFundingVO.getProductVOList().stream().filter(a->a.getSpuId().equals(spuId)).collect(Collectors.toList());
        CrowdFundingVO.ProductVO productVO= productVOList.get(0);
        log.info("当前数量是"+productVO.getCurrentNum(),"订单购买数量是"+num,"设置的数量是"+ productVO.getNum());
//        if ((productVO.getCurrentNum() + num) > productVO.getNum()) {
//            throw new BizCoreRuntimeException(MaErrorConstants.PRODUCT_NUM_INSUFFICIENT,spuName);
//        }
        //扣除库存
        //添加奖品数量
        Ssqb updateOrderCount = Ssqb.create("com.lewei.eshop.ma.crowd.funding.updateProduct")
                .setParam("plId", plId)
                .setParam("spuId", productVO.getSpuId())
                .setParam("currentNum", num);
       int l =  dao.updateByMybatis(updateOrderCount);
       if (l < 1){
           throw new BizCoreRuntimeException(MaErrorConstants.PRODUCT_NUM_INSUFFICIENT,spuName);

       }
    }
    //赏品日购买限制验证
    private void checkDayLimit(Map<Long, Integer> spuMap, Long memberId) {

        List<Long> spuIds = new ArrayList<>(spuMap.keySet());
        SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.in("id", spuIds));
        List<ProductSpu> spus = dao.queryList(queryBuilder, ProductSpu.class);
        if (CollectionUtils.isNotEmpty(spus)) {
            for (ProductSpu spu : spus) {
                if (ProductEnum.S_ST_PRODUCT.value().equals(spu.getSpuType())) {
                    if (StringUtils.isNotEmpty(spu.getExtJson())) {
                        JSONObject jsonObject = JSON.parseObject(spu.getExtJson());
                        Boolean dayLimit = jsonObject.getBoolean("dayLimit");
                        if (BooleanUtils.isTrue(dayLimit)) {
                            Integer limitNum = jsonObject.getInteger("limitNum");
                            log.info("当前spuId是" + spu.getId());
                            Integer num = (Integer) redisCacheProvider.getRedisTemplate().opsForHash().get(MaCacheKeys.MEMBER_PRODUCT_QUEUE_KEY,
                                    memberId.toString() + spu.getId().toString());
                            log.info("当前用户编号" + memberId + "spuId是" + spu.getId() + "当前商品购买次数是" + num);
                            if (num == null) {
                                num = 0;
                            }
                            Integer buyNum = spuMap.get(spu.getId());
                            if (num + buyNum > limitNum) {
                                throw new BizCoreRuntimeException(MaErrorConstants.PRODUCT_DAY_LIMIT_ERROR, spu.getName(), limitNum);
                            }
                        }
                        }
                    }
                }
            }
        }


}
