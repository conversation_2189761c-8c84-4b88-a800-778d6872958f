package com.lewei.eshop.ma.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.lewei.eshop.common.CodecUtil;
import com.lewei.eshop.common.OrderUtil;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.data.product.ProductEnum;
import com.lewei.eshop.common.request.market.AuctionDealRequest;
import com.lewei.eshop.common.request.market.PayAuctionRequest;
import com.lewei.eshop.common.request.market.PayFixedRequest;
import com.lewei.eshop.common.request.member.MemberBalancePayRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.vo.market.BlackMarketDetailVo;
import com.lewei.eshop.common.vo.market.MemberRewardPrimeCostFeeDTO;
import com.lewei.eshop.common.vo.member.MemberRewardVo;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.market.BlackMarketAuction;
import com.lewei.eshop.entity.market.BlackMarketAuctionReward;
import com.lewei.eshop.entity.market.types.BlackMarketStatusEnum;
import com.lewei.eshop.entity.market.types.MarketAuctionStatusEnum;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberReward;
import com.lewei.eshop.entity.member.types.BillTypeEnum;
import com.lewei.eshop.entity.member.types.MemberAuthorityEnum;
import com.lewei.eshop.entity.member.types.MemberBillTypeEnum;
import com.lewei.eshop.entity.member.types.MemberRewardStatusEnum;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.OrderProduct;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.ma.MaErrorConstants;
import com.lewei.eshop.ma.SysConfig;
import com.lewei.eshop.ma.biz.*;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.lewei.eshop.ma.client.PublicFeignClient;
import com.lewei.eshop.ma.client.paas.MessageFeignClient;
import com.lewei.eshop.ma.client.paas.request.SmsRequest;
import com.lewei.eshop.ma.pay.AuctionCallbackProcessor;
import com.lewei.eshop.ma.pay.FixedCallbackProcessor;
import com.lewei.eshop.ma.pay.IOrderPayService;
import com.lewei.eshop.ma.pay.PaasHttpProxy;
import com.lewei.eshop.message.mina.entity.MpMessageBuilder;
import com.lewei.eshop.message.mina.entity.TmplEvent;
import com.lewei.eshop.message.mina.entity.TmplTypeEnum;
import com.lewei.eshop.message.mina.service.IMessageCoreService;
import com.lewei.eshop.message.yunpian.YunpianSmsSender;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/4/14
 */
@Transactional
@Service
@Slf4j
public class MaMarketPayServiceImpl implements IMaMarketPayService {

    private final String fixCallBackUrl =  "/api/ma/fixed/callback";
    private final String auctionCallBackUrl =  "/api/ma/auction/callback";


    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private IMaBlackMarketService marketService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private FixedCallbackProcessor fixedCallbackProcessor;
    @Autowired
    private AuctionCallbackProcessor auctionCallbackProcessor;
    @Autowired
    private IMaMemberBillService memberBillService;
    @Autowired
    private IMaMemberBlacklistService blacklistService;
    @Autowired
    private IMaMemberRewardService memberRewardService;
    @Autowired
    private PaasHttpProxy paasHttpProxy;
    @Autowired
    private IMessageCoreService messageCoreService;
    @Autowired
    private YunpianSmsSender yunpianSmsSender;
    @Autowired
    private IMaMemberService memberService;
    @Autowired
    private MessageFeignClient messageFeignClient;

    @Autowired
    private IOrderPayService orderPayService;

    @Autowired
    private PublicFeignClient publicFeignClient;

    private final String SMS_BUY_SUCCESS_TPL_ID = "4457402";
    private final String SMS_SOLD_SUCCESS_TPL_ID = "4599722";






    @Override
    public Map<String, Object> payAuction(PayAuctionRequest request, Long memberId,String appId) {
        boolean f = memberService.checkIsAuthority(MemberAuthorityEnum.NO_TRADE,memberId);
        if (f){
            throw new BizCoreRuntimeException(MaErrorConstants.USER_MUST_NOT_TRADE);
        }

        Member member = dao.queryById(memberId,Member.class);
        if (member == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40023"));
        }
        BlackMarketDetailVo market = marketService.queryBlackMarketDetail(request.getMarketId(),null);
        if (market == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40024"));
        }
        if(!Objects.equals(market.getStatus(), BlackMarketStatusEnum.S_BMS_ON_SHELF.value())){
            throw new BizCoreRuntimeException(MaErrorConstants.BLACK_MARKET_STATUS_ERROR);
        }
        if (request.getAuctionPrice() == null){
            request.setAuctionPrice(BigDecimal.ZERO);
        }
        if (ListUtil.isEmpty(request.getMemberRewardIds())){
            if (request.getAuctionPrice().compareTo(market.getAuctionPrice()) < 0){
                throw new BizCoreRuntimeException(MaErrorConstants.CAN_NOT_LOWER_THAN_AUCTION_PRICE);
            }
        }

        SaasQueryBuilder queryAuction = SaasQueryBuilder.where(Restrictions.eq("memberId",memberId))
                .and(Restrictions.eq("marketId",request.getMarketId()))
                .and(Restrictions.eq("payStatus",1))
                .and(Restrictions.eq("status",MarketAuctionStatusEnum.S_MAS_JPZ.value()));
        BlackMarketAuction auction = dao.query(queryAuction,BlackMarketAuction.class);
        if (auction != null) {
            throw new BizCoreRuntimeException(MaErrorConstants.ALREADY_IN_THE_AUCTION);
        }

        if (ListUtil.isNotEmpty(request.getMemberRewardIds())){
            validateBlackMarketRequest(request.getMemberRewardIds(),memberId);
        }

        this.validateBlackList(market.getMemberId(),market.getId(),memberId);
        String title = BizMessageSource.getInstance().getMessage("cem40025");
        if (ListUtil.isNotEmpty(market.getRewardVos())){
            if (market.getRewardVos().size() > 1){
                title = title + market.getRewardVos().get(0).getRewardName() + BizMessageSource.getInstance().getMessage("cem40027");
            }else {
                title =title + market.getRewardVos().get(0).getRewardName();
            }
        }else {
            title = BizMessageSource.getInstance().getMessage("cem40026");
        }
        BigDecimal primeCostFee = market.getRewardVos().stream().map(BlackMarketDetailVo.RewardVo::getPrimeCostFee).reduce(BigDecimal.ZERO,BigDecimal::add);
        BlackMarketAuction marketAuction = new BlackMarketAuction();
        marketAuction.setAuctionPrice(request.getAuctionPrice());
        marketAuction.setMarketId(request.getMarketId());
        marketAuction.setMemberId(memberId);
        marketAuction.setStatus(MarketAuctionStatusEnum.S_MAS_JPZ.value());
        marketAuction.setOrderSn(CodecUtil.createOrderId());
        marketAuction.setTitle(title);
        marketAuction.setPaymentMethod(request.getPaymentMethod());
        marketAuction.setPayStatus(false);
        marketAuction.setCreated(DateFormatUtils.getNow());
        marketAuction.setDataStatus(true);
        marketAuction.setTotalPrimeCostFee(primeCostFee);
        marketAuction.setIsRecover(member.getIsRecover());
        dao.save(marketAuction);
        if (ListUtil.isNotEmpty(request.getMemberRewardIds())){
            List<BlackMarketAuctionReward> auctionRewards = new ArrayList<>();
            Ssqb query  = Ssqb.create("com.lewei.eshop.ma.black.market.queryMemberRewardPrimeCostFee")
                    .setParam("memberRewardIds",request.getMemberRewardIds());
            List<MemberRewardPrimeCostFeeDTO> costFeeDTOS =  dao.findForList(query, MemberRewardPrimeCostFeeDTO.class);
            BlackMarketAuctionReward auctionReward;
            for (MemberRewardPrimeCostFeeDTO costFeeDTO : costFeeDTOS) {
                auctionReward = new BlackMarketAuctionReward();
                auctionReward.setAuctionId(marketAuction.getId());
                auctionReward.setMemberRewardId(costFeeDTO.getMemberRewardId());
                auctionReward.setPrimeCostFee(costFeeDTO.getPrimeCostFee());
                auctionRewards.add(auctionReward);
            }
            dao.batchCreate(auctionRewards,BlackMarketAuctionReward.class);
//            if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())){
//                memberRewardService.batchUpdateMemberRewardStatus(request.getMemberRewardIds(),MemberRewardStatusEnum.S_MRS_ON_CHANGE.value());
//            }
        }

     //   marketService.queryBlackMarketRewards(request.getMarketId());


        Map<String,Object> resultMap = new HashMap<>();

        resultMap.put("auctionId",marketAuction.getId());

        if (request.getAuctionPrice().compareTo(BigDecimal.ZERO) == 0){
            this.afterAuctionPay(marketAuction);
        }else if (OrderUtil.isCashPay(request.getPaymentMethod())) {
//            PayWxRequest payWxRequest = new PayWxRequest();
//            payWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/ma/auction/callback");
//            payWxRequest.setOpenId(member.getBmOpenId());
//            payWxRequest.setOrderCodes(marketAuction.getOrderSn());
//            payWxRequest.setOrderTitle(marketAuction.getTitle());
//            payWxRequest.setPaymentMoney(request.getAuctionPrice());
//            payWxRequest.setPayMethod("wx_js_api");
//            payWxRequest.setWxAppId(appId);
//
//            Map retMap = paasHttpProxy.callWxPay(payWxRequest, XcrmThreadContext.getChainId());
//            resultMap.put("payParam",retMap);

             Map retMap = orderPayService.callCashPayApiForMap(marketAuction.getTitle(),request.getAuctionPrice(),marketAuction.getOrderSn(),memberId,XcrmThreadContext.getChainId(),appId,request.getPaymentMethod(),auctionCallBackUrl);
             resultMap.put("payParam",retMap);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())) {
            memberFeignClient.memberBalancePay(new MemberBalancePayRequest(null, member.getId(), request.getAuctionPrice(), title, marketAuction.getId()));
            this.afterAuctionPay(marketAuction);

            // 公众号消息推送
            try {
//                sendMpMessage(Boolean.TRUE,member.getChainId(),member.getTenantId(),
//                        marketAuction.getTitle(),marketAuction.getAuctionPrice().toString(),marketAuction.getOrderSn(),member);
            } catch (Exception e) {
                e.printStackTrace();
            }
            // TODO: 2021/4/19
        } else{
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }



        return resultMap;
    }


    @Override
    public void auctionDeal(AuctionDealRequest request, Long memberId) {
        BlackMarketAuction auction = dao.queryById(request.getAuctionId(),BlackMarketAuction.class);
        if (auction == null){
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40028"));
        }
        if (!Objects.equals(auction.getStatus(),MarketAuctionStatusEnum.S_MAS_JPZ.value()) || BooleanUtils.isFalse(auction.getPayStatus())){
            throw new BizCoreRuntimeException(MaErrorConstants.AUCTION_STATUS_ERROR);
        }
        // 是否不可与玩家交易
        boolean isAuthority;
        // 竞拍者
        Member member = dao.queryById(auction.getMemberId(),Member.class);
        if (member == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40029"));
        }else {
            isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_TRADE_WITH_PLAYER, member.getId());
            if (isAuthority) {
                throw new BizCoreRuntimeException(MaErrorConstants.CANNOT_TRADE_WITH_THIS_PLAYER);
            }
        }
        // 发布者
        Member recoverMember = dao.queryById(memberId, Member.class);
        if (recoverMember != null) {
            isAuthority = memberService.checkIsAuthority(MemberAuthorityEnum.NO_TRADE_WITH_PLAYER, memberId);
            if (isAuthority) {
                throw new BizCoreRuntimeException(MaErrorConstants.CANNOT_TRADE_WITH_THIS_PLAYER);
            }
        }
        //生成订单
        BlackMarketDetailVo market = marketService.queryBlackMarketDetail(request.getMarketId(),null);
        if (market == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40030"));
        }
        if (market.getMemberId().compareTo(memberId) != 0){
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40030"));
        }
        if (!Objects.equals(market.getStatus(),BlackMarketStatusEnum.S_BMS_ON_SHELF.value())){
            throw new BizCoreRuntimeException(MaErrorConstants.BLACK_MARKET_STATUS_ERROR);
        }

        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setWechatAccount(member.getWechatAccount());
        order.setMemberId(member.getId());
        order.setOpenId(member.getOpenId());
        order.setPaymentMethod(auction.getPaymentMethod());
        order.setOrderMoney(auction.getAuctionPrice());
        order.setPaymentMoney(auction.getAuctionPrice());
        order.setShippingMoney(BigDecimal.ZERO);
        order.setOrderTitle(auction.getTitle());
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(member.getId());
        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_MARKET.value());
        order.setPayTime(new Timestamp(System.currentTimeMillis()));
        order.setPayStatus(true);
        order.setStatus(OrderEnum.S_OS_DONE.value());
        order.setTradeSuccTime(DateFormatUtils.getNow());
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(order.getPaymentMethod())){
            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            order.setOrderBalance(order.getOrderMoney().multiply(chainConfig.getMoneyRatio()));
        }
        dao.save(order);

        List<OrderProduct> orderProducts = new ArrayList<>();

        for (BlackMarketDetailVo.RewardVo rewardVo : market.getRewardVos()) {
            OrderProduct orderProduct = new OrderProduct();
            orderProduct.setOrderId(order.getId());
            orderProduct.setParentSpuId(rewardVo.getSpuItemId());
            orderProduct.setParentSkuId(rewardVo.getSkuItemId());
            orderProduct.setSpuId(rewardVo.getSpuItemId());
            orderProduct.setSkuId(rewardVo.getSkuItemId());
            orderProduct.setProductType(ProductEnum.S_ST_PRODUCT.value());
            orderProduct.setProductName(rewardVo.getRewardName());
            orderProduct.setMainImage(rewardVo.getMainImage());
            orderProduct.setCurrentUnitPrice(BigDecimal.ZERO);
            orderProduct.setQuantity(1);
            orderProduct.setTotalPrice(BigDecimal.ZERO);
            orderProduct.setCreated(new Timestamp(System.currentTimeMillis()));
            orderProduct.setCreateBy(memberId);
            orderProducts.add(orderProduct);
        }
        dao.batchSave(orderProducts,OrderProduct.class);
        //购买者 赏袋入库  出售者赏品售出
        List<Long> memberRewardIds = market.getRewardVos().stream().map(BlackMarketDetailVo.RewardVo::getRewardId).collect(Collectors.toList());
        SaasQueryBuilder queryRewards = SaasQueryBuilder.where(Restrictions.in("id",memberRewardIds));
        List<MemberReward> soldRewards =  dao.queryList(queryRewards,MemberReward.class);
        List<MemberReward> buyerRewards = new ArrayList<>();
        for (MemberReward soldReward : soldRewards) {
            MemberReward reward = new MemberReward();
            BeanUtils.copyProperties(soldReward,reward);
            reward.setCreated(DateFormatUtils.getNow());
            reward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
            reward.setMemberId(auction.getMemberId());
            reward.setOrderId(order.getId());
            reward.setVerifyCode("");
            buyerRewards.add(reward);

            soldReward.setStatus(MemberRewardStatusEnum.S_MRS_SOLD.value());
            soldReward.setFinalStatus(MemberRewardStatusEnum.S_MRS_SOLD.finalStatus());
            soldReward.setUpdated(DateFormatUtils.getNow());
        }
        dao.batchUpdate(soldRewards,MemberReward.class);
        dao.batchSave(buyerRewards,MemberReward.class);

        //  置换赏品处理

        List<BlackMarketAuctionReward> auctionRewards = marketService.queryBlackMarketAuctionRewards(auction.getId());
        if(ListUtil.isNotEmpty(auctionRewards)){
            List<Long> auctionRewardIds = auctionRewards.stream().map(BlackMarketAuctionReward::getMemberRewardId).collect(Collectors.toList());
            SaasQueryBuilder queryActionRewards = SaasQueryBuilder.where(Restrictions.in("id",auctionRewardIds));
            List<MemberReward> auctionMemberRewards =  dao.queryList(queryActionRewards,MemberReward.class);
            List<MemberReward>  changeRewards = new ArrayList<>();

            for (MemberReward auctionMemberReward : auctionMemberRewards) {
                MemberReward reward = new MemberReward();
                BeanUtils.copyProperties(auctionMemberReward,reward);
                reward.setCreated(DateFormatUtils.getNow());
                reward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                reward.setMemberId(market.getMemberId());
                reward.setOrderId(order.getId());
                reward.setVerifyCode("");
                changeRewards.add(reward);
                auctionMemberReward.setStatus(MemberRewardStatusEnum.S_MRS_SOLD.value());
                auctionMemberReward.setFinalStatus(MemberRewardStatusEnum.S_MRS_SOLD.finalStatus());
                auctionMemberReward.setUpdated(DateFormatUtils.getNow());
            }
            dao.batchUpdate(auctionMemberRewards,MemberReward.class);
            dao.batchSave(changeRewards,MemberReward.class);
        }

        //更新黑市状态
//        BlackMarket dbMarket = new BlackMarket();
//        dbMarket.setUpdated(DateFormatUtils.getNow());
//        dbMarket.setStatus(BlackMarketStatusEnum.S_BMS_SOLD.value());
//        dbMarket.setId(request.getMarketId());
//        dao.update(dbMarket);

        Ssqb updateMarketStatus = Ssqb.create("com.lewei.eshop.ma.black.market.updateMarketStatus")
                .setParam("status",BlackMarketStatusEnum.S_BMS_SOLD.value())
                .setParam("marketId",request.getMarketId());

        int l = dao.updateByMybatis(updateMarketStatus);

        if(l < 1){
            throw new BizCoreRuntimeException(MaErrorConstants.BLACK_MARKET_STATUS_ERROR);
        }

        auction.setUpdated(DateFormatUtils.getNow());
        auction.setStatus(MarketAuctionStatusEnum.S_MAS_JPCG.value());
        // TODO 竞拍情况下，生成orderSn与order表不相对应
        auction.setOrderSn(order.getOrderSn());
        dao.update(auction);
        //余额充值到发布者钱包
        MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setBalance(auction.getAuctionPrice());
        balanceRequest.setMemberId(memberId);
        balanceRequest.setPlId(market.getId());
        balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40031") + order.getOrderTitle());
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        memberFeignClient.memberBalanceHandle(balanceRequest);
        //保存账单
        memberBillService.saveMemberBill(memberId,auction.getAuctionPrice(), BillTypeEnum.general, MemberBillTypeEnum.income,null,order.getOrderSn(), null);


        //其余退款至钱包
        this.refundToBalance(request.getMarketId());

        // 公众号消息推送
        try {

            sendMpMessage(Boolean.FALSE,order.getChainId(),order.getTenantId(),order.getOrderTitle(),order.getOrderMoney().toString(),order.getOrderSn(),auction.getMemberId());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Override
    public void auctionRevoke(Long auctionId, Long memberId) {
        BlackMarketAuction auction = dao.queryById(auctionId,BlackMarketAuction.class);
        if (auction == null){
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40028"));
        }
        if (auction.getMemberId().compareTo(memberId) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.CAN_NOT_REVOKE_OTHER_AUCTION);
        }
        if (!Objects.equals(auction.getStatus(),MarketAuctionStatusEnum.S_MAS_JPZ.value()) || BooleanUtils.isFalse(auction.getPayStatus())){
            throw new BizCoreRuntimeException(MaErrorConstants.AUCTION_STATUS_ERROR);
        }
        int l = marketService.updateMarketAuctionStatus(auctionId,MarketAuctionStatusEnum.S_MAS_YTK.value());

        if (l < 1){

            throw new BizCoreRuntimeException(MaErrorConstants.AUCTION_ALREADY_REFUND);
        }

        revertAuctionRewards(auction.getId());

        MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setBalance(auction.getAuctionPrice());
        balanceRequest.setMemberId(memberId);
        balanceRequest.setPlId(auction.getId());
        balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40032") + auction.getTitle());
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        memberFeignClient.memberBalanceHandle(balanceRequest);
        //保存账单
        memberBillService.saveMemberBill(auction.getMemberId(),auction.getAuctionPrice(), BillTypeEnum.general, MemberBillTypeEnum.refund,auction.getPaymentMethod(),auction.getOrderSn(), null);
    }

    private void revertAuctionRewards(Long auctionId) {
        List<BlackMarketAuctionReward> auctionRewards = marketService.queryBlackMarketAuctionRewards(auctionId);
        if (ListUtil.isNotEmpty(auctionRewards)) {
            List<Long> memberRewardIds = auctionRewards.stream().map(BlackMarketAuctionReward::getMemberRewardId).collect(Collectors.toList());
            memberRewardService.batchUpdateMemberRewardStatus(memberRewardIds, MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
        }
    }

    @Override
    public void refundToBalance(Long marketId) {
        List<BlackMarketAuction> auctions =  marketService.queryBlackMarketAuctions(marketId,MarketAuctionStatusEnum.S_MAS_JPZ.value());
        if (ListUtil.isNotEmpty(auctions)){
            for (BlackMarketAuction auction : auctions) {

                revertAuctionRewards(auction.getId());

                MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                balanceRequest.setGift(BigDecimal.ZERO);
                balanceRequest.setBalance(auction.getAuctionPrice());
                balanceRequest.setMemberId(auction.getMemberId());
                balanceRequest.setPlId(auction.getId());
                balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem40032") + auction.getTitle());
                balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                memberFeignClient.memberBalanceHandle(balanceRequest);
                auction.setStatus(MarketAuctionStatusEnum.S_MAS_YTK.value());
                auction.setUpdated(DateFormatUtils.getNow());
                dao.update(auction);
                //保存账单
                memberBillService.saveMemberBill(auction.getMemberId(),auction.getAuctionPrice(), BillTypeEnum.general, MemberBillTypeEnum.refund,auction.getPaymentMethod(),auction.getOrderSn(), null);
            }
        }
    }


    @Override
    public Map<String, Object> pay(PayFixedRequest request, Long memberId,String appId) {
        boolean f = memberService.checkIsAuthority(MemberAuthorityEnum.NO_TRADE,memberId);
        if (f){
            throw new BizCoreRuntimeException(MaErrorConstants.USER_MUST_NOT_TRADE);
        }

        Member member = dao.queryById(memberId,Member.class);
        if (member == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40023"));
        }
        BlackMarketDetailVo market = marketService.queryBlackMarketDetail(request.getMarketId(),null);
        if (market == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40024"));
        }
        if(!Objects.equals(market.getStatus(), BlackMarketStatusEnum.S_BMS_ON_SHELF.value())){
            throw new BizCoreRuntimeException(MaErrorConstants.BLACK_MARKET_STATUS_ERROR);
        }
        if (BooleanUtils.isFalse(market.getIsFixedPrice())){
            throw new BizCoreRuntimeException(MaErrorConstants.MARKET_NOT_SUPPORT_FIXED_PRICE);
        }
        if (market.getFixedPrice().compareTo(request.getFixedPrice()) != 0){
            throw new BizCoreRuntimeException(MaErrorConstants.BLACK_MARKET_EXPIRED);
        }
        this.validateBlackList(market.getMemberId(),market.getId(),memberId);
        String title = BizMessageSource.getInstance().getMessage("cem40025");
        if (ListUtil.isNotEmpty(market.getRewardVos())){
            if (market.getRewardVos().size() > 1){
                title = title + market.getRewardVos().get(0).getRewardName() + BizMessageSource.getInstance().getMessage("cem40027");
            }else {
                title =title + market.getRewardVos().get(0).getRewardName();
            }
        }else {
            title = BizMessageSource.getInstance().getMessage("cem40026");
        }
        Order order = new Order();
        order.setOrderSn(CodecUtil.createOrderId());
        order.setWechatAccount(member.getWechatAccount());
        order.setMemberId(member.getId());
        order.setOpenId(member.getOpenId());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderMoney(request.getFixedPrice());
        order.setPaymentMoney(request.getFixedPrice());
        order.setShippingMoney(BigDecimal.ZERO);
        order.setOrderTitle(title);
        order.setCreated(new Timestamp(System.currentTimeMillis()));
        order.setCreateBy(member.getId());
        order.setOrderBizType(OrderBizTypeEnum.S_OBZ_MARKET.value());
        order.setPayTime(new Timestamp(System.currentTimeMillis()));
        order.setPayStatus(false);
        order.setStatus(OrderEnum.S_OS_UNPAID.value());
        Map<String,Object> extJson = new HashMap<>();
        extJson.put("marketId",request.getMarketId());
        order.setExtJson(JSON.toJSONString(extJson));
        if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(order.getPaymentMethod())){
            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            order.setOrderBalance(order.getOrderMoney().multiply(chainConfig.getMoneyRatio()));
        }
        dao.save(order);

        List<OrderProduct> orderProducts = new ArrayList<>();

        for (BlackMarketDetailVo.RewardVo rewardVo : market.getRewardVos()) {
            OrderProduct orderProduct = new OrderProduct();
            orderProduct.setOrderId(order.getId());
            orderProduct.setParentSpuId(rewardVo.getSpuItemId());
            orderProduct.setParentSkuId(rewardVo.getSkuItemId());
            orderProduct.setSpuId(rewardVo.getSpuItemId());
            orderProduct.setSkuId(rewardVo.getSkuItemId());
            orderProduct.setProductType(ProductEnum.S_ST_PRODUCT.value());
            orderProduct.setProductName(rewardVo.getRewardName());
            orderProduct.setMainImage(rewardVo.getMainImage());
            orderProduct.setCurrentUnitPrice(BigDecimal.ZERO);
            orderProduct.setQuantity(1);
            orderProduct.setTotalPrice(BigDecimal.ZERO);
            orderProduct.setCreated(new Timestamp(System.currentTimeMillis()));
            orderProduct.setCreateBy(memberId);
            orderProducts.add(orderProduct);
        }
        dao.batchSave(orderProducts,OrderProduct.class);


        Map<String,Object> resultMap = new HashMap<>();

        resultMap.put("orderId",order.getId());

        if (OrderUtil.isCashPay(request.getPaymentMethod())) {
            Map retMap = orderPayService.callCashPayApiForMap(order.getOrderTitle(),order.getPaymentMoney(),order.getOrderSn(),memberId,XcrmThreadContext.getChainId(),appId,request.getPaymentMethod(),fixCallBackUrl);
            resultMap.put("payParam",retMap);
        } else if (OrderPaymentTypeEnum.S_OPM_BALANCE.value().equals(request.getPaymentMethod())) {
            memberFeignClient.memberBalancePay(new MemberBalancePayRequest(null, member.getId(), request.getFixedPrice(), title, order.getId()));
            this.afterPay(order);
        } else{
            throw new BizCoreRuntimeException(MaErrorConstants.PAY_TYPE_NOT_SUPPORT);
        }

//        try {
//            sendMpMessage(Boolean.TRUE,order.getChainId(),order.getTenantId(),order.getOrderTitle(),order.getOrderMoney().toString(),order.getOrderSn(),market.getMemberId());
//        } catch (Exception e) {
//            log.error("market pay error :{}",e.getMessage());
//            e.printStackTrace();
//        }
        return resultMap;
    }

//    private void callCashPayApi(String appId, Member member, Order order, Map<String, Object> resultMap,String pay) {
//        PayWxRequest payWxRequest = new PayWxRequest();
//        payWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/ma/fixed/callback");
//        payWxRequest.setOpenId(member.getBmOpenId());
//        payWxRequest.setOrderCodes(order.getOrderSn());
//        payWxRequest.setOrderTitle(order.getOrderTitle());
//        payWxRequest.setPaymentMoney(order.getOrderMoney());
//        payWxRequest.setPayMethod("wx_js_api");
//        payWxRequest.setWxAppId(appId);
//
//        Map retMap = paasHttpProxy.callWxPay(payWxRequest, XcrmThreadContext.getChainId());
//        resultMap.put("retMap",retMap);
//    }

    private void validateBlackList(Long memberId,Long marketId,Long blackMemberId){
        boolean flag1 = blacklistService.queryIsMemberBlack(memberId,blackMemberId);
        if (flag1){
            throw new BizCoreRuntimeException(MaErrorConstants.BE_MEMBER_BLACK);
        }
        boolean flag2 = blacklistService.queryIsMarKetBlack(marketId,blackMemberId);
        if (flag2){
            throw new BizCoreRuntimeException(MaErrorConstants.BE_MARKET_BLACK);
        }
    }


    private void validateBlackMarketRequest(List<Long> memberRewardIds, Long memberId){
        List<MemberRewardVo> rewardVos = memberRewardService.queryMemberRewardList(memberId,null,memberRewardIds);
        if (ListUtil.isEmpty(rewardVos)){
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem40033"));
        }
        if (rewardVos.size() != memberRewardIds.size()){
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_EXPIRED);
        }
        rewardVos = rewardVos.stream().filter(a->! Objects.equals(a.getStatus(), MemberRewardStatusEnum.S_MRS_UNAPPLY.value())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(rewardVos)){
            String rewardName = rewardVos.stream().map(MemberRewardVo::getName).collect(Collectors.joining(","));
            throw new BizCoreRuntimeException(MaErrorConstants.MEMBER_REWARD_STATUS_NOT_UNAPPLY,rewardName);
        }

    }

    private void sendMpMessage(Boolean isFixed,Long chainId,Long tenantId,String orderInfo,String price,String orderSn,Long memberId){

        Member member = dao.queryById(memberId,Member.class);
        if (member != null) {

            if (StringUtils.isEmpty(member.getMpOpenId()) ){
                MpMessageBuilder builder;
                if (isFixed){
                    builder = MpMessageBuilder.event(TmplEvent.fixed_success_send);
                } else {
                    builder = MpMessageBuilder.event(TmplEvent.bidding_success_send);
                }
                builder.maAppId(member.getAppId())
                        .chainId(chainId)
                        .tenantId(tenantId)
                        .openId(member.getMpOpenId())
                        .put("memberName",member.getMemberName())
                        .put("price",price)
                        .put("orderInfo",orderInfo)
                        .put("orderSn",orderSn)
                ;
                messageCoreService.sendMpMessageRestructure(builder);
            }

            if (StringUtils.isNotEmpty(member.getMobile())){
                String message;
                //发送短信消息

                if (isFixed){
                    try {
                        SmsRequest request = new SmsRequest();
                        request.setMobiles(member.getMobile());
                        request.setParam(orderInfo);
                        request.setTmplEvent(TmplEvent.fixed_success_send.value());
                        request.setTmplType(TmplTypeEnum.consume.value());
                        messageFeignClient.sendMessage(request);

                    } catch (Exception e) {
                        log.error("sms error = {}",e.getMessage());
                    }
                }else {

                    try {
                            List<String> paramList = new ArrayList<>();
                            paramList.add(member.getMemberName());
                            paramList.add(orderInfo);
                            SmsRequest request = new SmsRequest();
                            request.setMobiles(member.getMobile());
                            request.setParam(Joiner.on(",").skipNulls().join(paramList));
                            request.setTmplEvent(TmplEvent.bidding_success_send.value());
                            request.setTmplType(TmplTypeEnum.consume.value());
                            messageFeignClient.sendMessage(request);

                    } catch (Exception e) {
                        log.error("sms error = {}",e.getMessage());
                    }
                }

            }
        }
    }

    private void afterPay(Order order) {

        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                fixedCallbackProcessor.onPayTradeSuccess(order, null);
            }
        });
    }


    public void afterAuctionPay(BlackMarketAuction auction) {

        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                auctionCallbackProcessor.onPayTradeSuccess(auction);
            }
        });
    }


}
