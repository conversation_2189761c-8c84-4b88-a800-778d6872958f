package com.lewei.eshop.ma.resource;


import com.lewei.eshop.auth.BaseAuthedResource;
import com.lewei.eshop.common.vo.order.RefundConfigVO;
import com.lewei.eshop.ma.client.MemberFeignClient;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;

/**
 * 退款配置
 *
 * <AUTHOR>
 * @time 2022/7/11
 */
@Path("/refund/config")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class RefundConfigResource extends BaseAuthedResource {

    @Autowired
    private MemberFeignClient memberFeignClient;

    /**
     * 查询退款配置
     *
     * @return RefundConfigVO
     */
    @GET
    public Response queryConfigVO() {
        log.info("RefundConfigResource.queryConfigVO()");
        RefundConfigVO vo = memberFeignClient.queryRefundConfigVO();
        return Response.ok(vo).build();
    }

}
