########################## bootstrap级别通用配置 ##########################
# 默认开发环境
spring.profiles.active=develop

##### nacos(注册中心和配置中心)地址
spring.cloud.nacos.server-addr=nacos.internal.aluckybox.com:80
#spring.cloud.nacos.username=nacoss
#spring.cloud.nacos.password=nacos
spring.cloud.nacos.config.file-extension=yml
spring.cloud.nacos.config.shared-dataids=common.yml
spring.cloud.nacos.config.refreshable-dataids=common.yml
#默认开发环境
nacos.namespace=15160c83-3cdf-4bcf-ad68-71123203f87f
#nacos.namespace=d56974b1-1440-4422-bc88-0628b907c885


##### spring-boot-actuator配置
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

##### 允许bean覆盖
spring.main.allow-bean-definition-overriding=true