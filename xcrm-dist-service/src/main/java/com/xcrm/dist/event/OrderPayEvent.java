package com.xcrm.dist.event;

import com.xcrm.platform.entity.order.AppOrder;
import com.xcrm.platform.entity.order.AppProduct;
import org.springframework.context.ApplicationEvent;

/**
* 订单支付事件
* <AUTHOR>
* @date 2020/06/20
**/

public class OrderPayEvent extends ApplicationEvent {

    private AppOrder order;

    private AppProduct product;

    public OrderPayEvent(Object source) {
        super(source);
    }

    public OrderPayEvent(Object source, AppOrder order, AppProduct product) {
        super(source);
        this.order = order;
        this.product = product;
    }

    public AppOrder getOrder() {
        return order;
    }

    public AppProduct getProduct() {
        return product;
    }
}