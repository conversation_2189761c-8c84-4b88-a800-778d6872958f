package com.xcrm.dist.resource.request;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 成长任务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Data
public class GrowTaskConfigRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务名称
     */
    @NotEmpty(message = "name is required")
    private String name;
    /**
     * 任务类型
     */
    @NotEmpty(message = "task is required")
    private String task;
    /**
     * 支付类型
     */
    private String payType;
    /**
     * 关联id
     */
    private Long plId;
    /**
     * 分销用户关联等级 0 自己 1直属上级 2 非直属上级
     */
    //@NotNull(message = "level is required")
    private Integer level;
    /**
     * 获得成长值
     */
    @NotNull(message = "point is required")
    @DecimalMax(value = "99999.99")
    private BigDecimal point;
    /**
     * 日上限
     */
    @NotNull(message = "dayPoint is required")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999999.99")
    private BigDecimal dayPoint;
}
