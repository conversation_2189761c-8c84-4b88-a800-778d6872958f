package com.xcrm.dist.resource.response;

import com.xcrm.platform.auth.i18n.annotation.I18nSysCode;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/6/22
 */
@Data
public class OrderDetailVo {

    private Long id;
    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 订单业务类型，PS/图文单次, PSM/图文会员
     */
    private String orderBizType;

    /**
     * 图片
     */
    private String image;
    /**
     * 订单标题
     */
    private String orderTitle;

    /**
     * 收货地址信息json
     */
    private String addressJson;
    /**
     * 发货信息json
     */
    private String deliveryJson;
    /**
     * 发货时间
     */
    private Date shippingTime;
    /**
     * 收货时间
     */
    private Date receiveTime;

    /**
     * 购买个数
     */
    private Integer amount;

    /**
     * 订单状态: 待支付 已取消 交易成功
     */
    private String status;

    @I18nSysCode
    private String statusDesc;

    /**
     * 下单人id
     */
    private String userCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     *会员昵称
     */
    private String nickName;


    /**
     * 推荐人编号
     */
    private String referrerCode;

    /**
     *  支付方式: 微信
     */
    private String paymentMethod;

    /**
     *  订单金额
     */
    private BigDecimal orderMoney;

    /**
     *  支付金额
     */
    private BigDecimal paymentMoney;

    /**
     * 支付时间
     */
    private Date payTime;


    private String remark;

    private Date created;




}
