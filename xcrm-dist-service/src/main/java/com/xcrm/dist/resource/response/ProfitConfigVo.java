package com.xcrm.dist.resource.response;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2020/6/13
**/
@Data
public class ProfitConfigVo {

    /**
     * 分润配置id
     */
    private Long profitConfigId;

    /**
     * 等级id
     */
    private Long levelId;

    /**
     * 等级名称
     */
    private String level;

    /**
     * 自推佣金
     */
    private BigDecimal selfBuyProfitAmount;

    /**
     * 直接粉丝 一级 分润配置json
     */
    @JsonIgnore
    private String strLevel1ProfitJson;

    /**
     * 间接粉丝 二级 分润配置json
     */
    @JsonIgnore
    private String strLevel2ProfitJson;

    private List<JSONObject> level1ProfitJson;

    private List<JSONObject> level2ProfitJson;

    /**
     * 人数
     */
    private Integer userCount;

}