package com.xcrm.dist.resource.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
* 按照订单方式分润 响应
* <AUTHOR>
* @date 2020/6/22
**/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderProfitVo {

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 分润金额
     */
    private BigDecimal profitAmount;
}