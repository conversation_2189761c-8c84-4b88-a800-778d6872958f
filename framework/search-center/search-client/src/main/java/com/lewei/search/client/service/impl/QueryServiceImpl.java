package com.lewei.search.client.service.impl;

import com.lewei.common.model.PageResult;
import com.lewei.search.client.service.IQueryService;
import com.lewei.search.client.utils.io.IOAttach;
import com.lewei.search.client.utils.io.IOStatsAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.LoggingDeprecationHandler;
import org.elasticsearch.common.xcontent.NamedXContentRegistry;
import org.elasticsearch.common.xcontent.XContentParser;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.SearchModule;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zxp.esclientrhl.enums.AggsType;
import org.zxp.esclientrhl.repository.Attach;
import org.zxp.esclientrhl.repository.PageSortHighLight;
import org.zxp.esclientrhl.repository.Sort;
import org.zxp.esclientrhl.util.Constant;
import org.zxp.esclientrhl.util.JsonUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 搜索客户端Service
 *
 * <AUTHOR>
 * @date 2019/4/24
 */
@Service
@Slf4j
public class QueryServiceImpl implements IQueryService {
//    @Resource
//    private SearchServiceClient searchServiceClient;
//
//    @Resource
//    private AggregationService aggregationService;
//
//    @Autowired
//    private ElasticsearchTemplate elasticsearchTemplate;

    @Autowired
    private RestHighLevelClient client;

    private static final String keyword = ".keyword";

    private static final NamedXContentRegistry xContentRegistry;

    static {
        SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());
        xContentRegistry =
                new NamedXContentRegistry(searchModule.getNamedXContents());
    }
    @Override
    public <Y, T> PageResult<T> search(String indexName, String queryBuilder, Attach attach, Class<T> clazz) throws Exception {
        return null;
    }

    @Override
    public <Y, T> List<T> searchList(String indexName, String sourceJson, Attach attach, Class<T> clazz) throws Exception {
        return null;
    }




    //    @Override
//    public PageResult<JSONObject> strQuery(String indexName, SearchDto searchDto) {
//        return strQuery(indexName, searchDto, null);
//    }
//
//    @Override
//    public PageResult<JSONObject> strQuery(String indexName, SearchDto searchDto, LogicDelDto logicDelDto) {
//        setLogicDelQueryStr(searchDto, logicDelDto);
//        return searchServiceClient.strQuery(indexName, searchDto);
//    }
//
//    /**
//     * 拼装逻辑删除的条件
//     * @param searchDto 搜索dto
//     * @param logicDelDto 逻辑删除dto
//     */
//    private void setLogicDelQueryStr(SearchDto searchDto, LogicDelDto logicDelDto) {
//        if (logicDelDto != null
//                && StrUtil.isNotEmpty(logicDelDto.getLogicDelField())
//                && StrUtil.isNotEmpty(logicDelDto.getLogicNotDelValue())) {
//            String result;
//            //搜索条件
//            String queryStr = searchDto.getQueryStr();
//            //拼凑逻辑删除的条件
//            String logicStr = logicDelDto.getLogicDelField() + ":" + logicDelDto.getLogicNotDelValue();
//            if (StrUtil.isNotEmpty(queryStr)) {
//                result = "(" + queryStr + ") AND " + logicStr;
//            } else {
//                result = logicStr;
//            }
//            searchDto.setQueryStr(result);
//        }
//    }
//
//    /**
//     * 访问统计聚合查询
//     * @param indexName 索引名
//     * @param routing es的路由
//     */
//    @Override
//    public Map<String, Object> requestStatAgg(String indexName, String routing) {
//        return aggregationService.requestStatAgg(indexName, routing);
//    }


    @Override
    public Map<String, Stats> statsAggs(String metricName, String sourceJson, Class clazz, String bucketName, String... indexs) throws Exception {
        return null;
    }

    @Override
    public <Y, T> PageResult<T> search(String indexName, QueryBuilder queryBuilder, Attach attach, Class<T> clazz) throws Exception {
        List<T> list = new ArrayList<>();
        PageSortHighLight pageSortHighLight = attach.getPageSortHighLight();
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        //分页
        if (pageSortHighLight.getPageSize() != 0) {
            //search after不可指定from
            if(!attach.isSearchAfter()) {
                searchSourceBuilder.from((pageSortHighLight.getCurrentPage() - 1) * pageSortHighLight.getPageSize());
            }
            searchSourceBuilder.size(pageSortHighLight.getPageSize());
        }
        //排序
        if (pageSortHighLight.getSort() != null) {
            Sort sort = pageSortHighLight.getSort();
            List<Sort.Order> orders = sort.listOrders();
            for (int i = 0; i < orders.size(); i++) {
                searchSourceBuilder.sort(new FieldSortBuilder(orders.get(i).getProperty()).order(orders.get(i).getDirection()));
            }
        }
        //TrackTotalHits设置为true，解除查询结果超出10000的限制
        if(attach.isTrackTotalHits()){
            searchSourceBuilder.trackTotalHits(attach.isTrackTotalHits());
        }

        //设定返回source
        if(attach.getExcludes()!= null || attach.getIncludes() != null){
            searchSourceBuilder.fetchSource(attach.getIncludes(),attach.getExcludes());
        }
        searchRequest.source(searchSourceBuilder);
        //设定routing
        if(!StringUtils.isEmpty(attach.getRouting())){
            searchRequest.routing(attach.getRouting());
        }
        //打印日志
        log.info(searchSourceBuilder.toString());
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        SearchHits hits = searchResponse.getHits();
        SearchHit[] searchHits = hits.getHits();
        for (SearchHit hit : searchHits) {
            list.add(JsonUtils.string2Obj(hit.getSourceAsString(), clazz));
        }

        return PageResult.<T>builder().list(list).code(0).totalCount(Math.toIntExact(hits.getTotalHits().value))
                .pageNo(pageSortHighLight.getCurrentPage()).pageSize(pageSortHighLight.getPageSize()).build();
    }

    @Override
    public <Y, T> List<T> searchList(String indexName, QueryBuilder queryBuilder, Attach attach, Class<T> clazz) throws Exception {
        List<T> list = new ArrayList<>();
        PageSortHighLight pageSortHighLight = attach.getPageSortHighLight();
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);

        //排序
        if (pageSortHighLight.getSort() != null) {
            Sort sort = pageSortHighLight.getSort();
            List<Sort.Order> orders = sort.listOrders();
            for (int i = 0; i < orders.size(); i++) {
                searchSourceBuilder.sort(new FieldSortBuilder(orders.get(i).getProperty()).order(orders.get(i).getDirection()));
            }
        }
        //TrackTotalHits设置为true，解除查询结果超出10000的限制
//        if(attach.isTrackTotalHits()){
////            searchSourceBuilder.trackTotalHits(attach.isTrackTotalHits());
////        }

        //设定返回source
        if(attach.getExcludes()!= null || attach.getIncludes() != null){
            searchSourceBuilder.fetchSource(attach.getIncludes(),attach.getExcludes());
        }
        searchRequest.source(searchSourceBuilder);
        //设定routing
        if(!StringUtils.isEmpty(attach.getRouting())){
            searchRequest.routing(attach.getRouting());
        }
        //打印日志
        log.info(searchSourceBuilder.toString());
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        SearchHits hits = searchResponse.getHits();
        SearchHit[] searchHits = hits.getHits();
        for (SearchHit hit : searchHits) {
            list.add(JsonUtils.string2Obj(hit.getSourceAsString(), clazz));
        }

       return list;

    }

    @Override
    public Map<String, Stats> statsAggs(String metricName, QueryBuilder queryBuilder, Class clazz, String bucketName, String... indexs) throws Exception {
        String[] indexname = indexs;
        Field f_metric = clazz.getDeclaredField(metricName.replaceAll(keyword, ""));
        Field f_bucket = clazz.getDeclaredField(bucketName.replaceAll(keyword, ""));
        if (f_metric == null) {
            throw new Exception("metric field is null");
        }
        if (f_bucket == null) {
            throw new Exception("bucket field is null");
        }
        //  metricName = genKeyword(f_metric, metricName);
        //    bucketName = genKeyword(f_bucket, bucketName);

        String by = "by_" + bucketName.replaceAll(keyword, "");
        String me = "stats" + "_" + metricName.replaceAll(keyword, "");

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        TermsAggregationBuilder aggregation = AggregationBuilders.terms(by)
                .field(bucketName);
        //默认按照count的降序排序
        aggregation.order(BucketOrder.count(false));
        aggregation.subAggregation(AggregationBuilders.stats(me).field(metricName)).size(Constant.AGG_RESULT_COUNT);
        if (queryBuilder != null) {
            searchSourceBuilder.query(queryBuilder);
        }

        DateHistogramAggregationBuilder teamAgg = AggregationBuilders
                .dateHistogram("player_count")
                .field("created")
                .dateHistogramInterval(DateHistogramInterval.HOUR).format("yyyy-MM-dd HH:mm:ss");
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(aggregation).aggregation(teamAgg);
        SearchRequest searchRequest = new SearchRequest(indexname);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        Aggregations aggregations = searchResponse.getAggregations();
        Terms by_risk_code = aggregations.get(by);
        Map<String, Stats> map = new LinkedHashMap<>();
        for (Terms.Bucket bucket : by_risk_code.getBuckets()) {
            Stats stats = bucket.getAggregations().get(me);
            map.put(bucket.getKey().toString(), stats);
        }
        return map;
    }

    @Override
    public Map<String, Map<String, Stats>> statsComplexAggs(String metricName, QueryBuilder queryBuilder, Class clazz, String bucketName1, String bucketName2, String... indexs) throws Exception {
        String[] indexname = indexs;
       // Field f_metric = clazz.getDeclaredField(metricName.replaceAll(keyword, ""));
       // Field f_bucket = clazz.getDeclaredField(bucketName1.replaceAll(keyword, ""));
//        if (f_metric == null) {
//            throw new Exception("metric field is null");
//        }
//        if (f_bucket == null) {
//            throw new Exception("bucket field is null");
//        }
        //  metricName = genKeyword(f_metric, metricName);
        //    bucketName = genKeyword(f_bucket, bucketName);

        String by = "by_" + bucketName1.replaceAll(keyword, "");
        String by2 = "by_" + bucketName2.replaceAll(keyword, "");
        String me = "stats" + "_" + metricName.replaceAll(keyword, "");

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        TermsAggregationBuilder aggregation = AggregationBuilders.terms(by)
                .field(bucketName1)
                .size(Constant.AGG_RESULT_COUNT)
                .subAggregation(
                        AggregationBuilders.terms(by2)
                                .field(bucketName2)
                                .size(Constant.AGG_RESULT_COUNT)
                                .order(BucketOrder.count(false))
                        .subAggregation(AggregationBuilders.stats(me).field(metricName)).size(Constant.AGG_RESULT_COUNT)

                );
        //默认按照count的降序排序
       // aggregation.order(BucketOrder.count(false));
        //aggregation.subAggregation(AggregationBuilders.stats(me).field(metricName)).size(Constant.AGG_RESULT_COUNT);
        if (queryBuilder != null) {
            searchSourceBuilder.query(queryBuilder);
        }

//        DateHistogramAggregationBuilder teamAgg = AggregationBuilders
//                .dateHistogram("player_count")
//                .field("created")
//                .dateHistogramInterval(DateHistogramInterval.HOUR).format("yyyy-MM-dd HH:mm:ss");
//        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(aggregation);
        SearchRequest searchRequest = new SearchRequest(indexname);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        Aggregations aggregations = searchResponse.getAggregations();
        Terms by_risk_code = aggregations.get(by);
        Map<String, Map<String, Stats>> map = new LinkedHashMap<>();
        Map<String, Stats> subMap = new LinkedHashMap<>();
        for (Terms.Bucket bucket : by_risk_code.getBuckets()) {
            Terms by_risk_code2 = bucket.getAggregations().get(by2);
            subMap = new LinkedHashMap<>();
            for (Terms.Bucket bucket2 : by_risk_code2.getBuckets()) {
                Stats stats = bucket2.getAggregations().get(me);
                subMap.put(bucket2.getKey().toString(), stats);
            }
            map.put(bucket.getKey().toString(),subMap);

        }
        return map;


    }

    @Override
    public Map<String, Long> cardinalityAggs(String metricName, QueryBuilder queryBuilder, Class clazz, String bucketName, String... indexs) throws Exception {
        String[] indexname = indexs;
        Field f_metric = clazz.getDeclaredField(metricName.replaceAll(keyword, ""));
        Field f_bucket = clazz.getDeclaredField(bucketName.replaceAll(keyword, ""));
        if (f_metric == null) {
            throw new Exception("metric field is null");
        }
        if (f_bucket == null) {
            throw new Exception("bucket field is null");
        }
        //  metricName = genKeyword(f_metric, metricName);
        //    bucketName = genKeyword(f_bucket, bucketName);

        String by = "by_" + bucketName.replaceAll(keyword, "");
        String me = "cardinality" + "_" + metricName.replaceAll(keyword, "");

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        TermsAggregationBuilder aggregation = AggregationBuilders.terms(by)
                .field(bucketName);
        //默认按照count的降序排序
        aggregation.order(BucketOrder.count(false));
        aggregation.subAggregation(AggregationBuilders.cardinality(me).field(metricName)).size(Constant.AGG_RESULT_COUNT);
        if (queryBuilder != null) {
            searchSourceBuilder.query(queryBuilder);
        }

        DateHistogramAggregationBuilder teamAgg = AggregationBuilders
                .dateHistogram("player_count")
                .field("created")
                .dateHistogramInterval(DateHistogramInterval.HOUR).format("yyyy-MM-dd HH:mm:ss");
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(aggregation).aggregation(teamAgg);
        SearchRequest searchRequest = new SearchRequest(indexname);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        Aggregations aggregations = searchResponse.getAggregations();
        Terms by_risk_code = aggregations.get(by);
        Map<String, Long> map = new LinkedHashMap<>();
        for (Terms.Bucket bucket : by_risk_code.getBuckets()) {
            ParsedCardinality cardinality = bucket.getAggregations().get(me);
            map.put(bucket.getKey().toString(), cardinality.getValue());
        }
        return map;
    }

    @Override
    public BigDecimal aggs(String metricName, AggsType aggsType, String sourceJson,  String... indexs) throws Exception {
        String[] indexname = indexs;
        String me = aggsType.toString() + "_" + metricName.replaceAll(keyword, "");
        XContentParser parser = XContentType.JSON.xContent().createParser(xContentRegistry, LoggingDeprecationHandler.INSTANCE, sourceJson);
        SearchSourceBuilder searchSourceBuilder = SearchSourceBuilder.fromXContent(parser);

        searchSourceBuilder.size(0);
        if (AggsType.count == aggsType) {
            searchSourceBuilder.aggregation(AggregationBuilders.count(me).field(metricName));
        } else if (AggsType.min == aggsType) {
            searchSourceBuilder.aggregation(AggregationBuilders.min(me).field(metricName));
        } else if (AggsType.max == aggsType) {
            searchSourceBuilder.aggregation(AggregationBuilders.max(me).field(metricName));
        } else if (AggsType.sum == aggsType) {
            searchSourceBuilder.aggregation(AggregationBuilders.sum(me).field(metricName));
        } else if (AggsType.avg == aggsType) {
            searchSourceBuilder.aggregation(AggregationBuilders.avg(me).field(metricName));
        }
        SearchRequest searchRequest = new SearchRequest(indexname);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        BigDecimal value = BigDecimal.ZERO;
        if (AggsType.count == aggsType) {
            ValueCount count = searchResponse.getAggregations().get(me);
            value = BigDecimal.valueOf(count.getValue());
        } else if (AggsType.min == aggsType) {
            ParsedMin min = searchResponse.getAggregations().get(me);
            value = BigDecimal.valueOf(min.getValue());
        } else if (AggsType.max == aggsType) {
            ParsedMax max = searchResponse.getAggregations().get(me);
            value = BigDecimal.valueOf(max.getValue());
        } else if (AggsType.sum == aggsType) {
            ParsedSum sum = searchResponse.getAggregations().get(me);
            value = BigDecimal.valueOf(sum.getValue());
        } else if (AggsType.avg == aggsType) {
            ParsedAvg avg = searchResponse.getAggregations().get(me);
            value = BigDecimal.valueOf(avg.getValue());
        }
        return value.setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    @Override
    public Integer deleteByCondition(QueryBuilder queryBuilder, String indexName) throws Exception {
    return this.deleteByCondition(queryBuilder,2000,indexName);
    }

    @Override
    public Integer deleteByCondition(QueryBuilder queryBuilder, int size, String indexName) throws Exception {
        log.info("QueryServiceImpl.deleteByCondition index = {}:", indexName);
        long s = System.currentTimeMillis();
        //总共删除的文档数量
        int totalDeleted = 0;
        //是否还有更多文档需要删除
        boolean hasMoreDocuments = true;

        while (hasMoreDocuments) {
            DeleteByQueryRequest request = new DeleteByQueryRequest(indexName);
            request.setQuery(queryBuilder);
            //删除最大数量
            request.setMaxDocs(size);
            //删除后刷新索引 能保证每次删除成功
            request.setRefresh(true);
            BulkByScrollResponse bulkResponse = client.deleteByQuery(request, RequestOptions.DEFAULT);

            int deletedDocuments = Math.toIntExact(bulkResponse.getDeleted());
            totalDeleted += deletedDocuments;

            if (deletedDocuments < size) {
                hasMoreDocuments = false;
            }
            log.info("index is deleteByCondition documents = {},index = {}", totalDeleted,indexName);

            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {

            }
        }
        log.info("Total deleted documents = {},index = {},deleteTime = {}", totalDeleted,indexName,System.currentTimeMillis()  - s);
        return totalDeleted;
    }

    @Override
    public BulkByScrollResponse deleteByConditionEs(QueryBuilder queryBuilder, int size, String indexName) throws Exception {
        log.info("QueryServiceImpl.deleteByConditionEs index = {}:", indexName);
        long s = System.currentTimeMillis();
        DeleteByQueryRequest request = new DeleteByQueryRequest(indexName);
        request.setQuery(queryBuilder);
        //删除最大数量
        request.setMaxDocs(size);
        BulkByScrollResponse bulkResponse = client.deleteByQuery(request, RequestOptions.DEFAULT);
        log.info("QueryServiceImpl.deleteByConditionEs bulkResponse = {}:", bulkResponse);

        //总共删除的文档数量
        int deletedDocuments = Math.toIntExact(bulkResponse.getDeleted());

        log.info("Total deleted documents = {},index = {},deleteTime = {}", deletedDocuments,indexName,System.currentTimeMillis()  - s);
        return bulkResponse;
    }
}
