package com.lewei.eshop.app.resource;

import com.lewei.eshop.app.space.IDifferentSpaceService;
import com.lewei.eshop.auth.BaseAuthedResource;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.space.DeleteDifferentSpaceRequest;
import com.lewei.eshop.common.request.space.DifferentSpaceRequest;
import com.lewei.eshop.common.request.space.DifferentSpaceStatusRequest;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.Collections;

/**
 * 异空间
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Path("/different/space")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class DifferentSpaceResource extends BaseAuthedResource {


    @Autowired
    public IDifferentSpaceService differentSpaceService;


    /**
     * 保存异空间
     * @param request
     * @return
     */
    @POST
    public Response saveDifferentSpace(@Valid DifferentSpaceRequest request) {
        log.debug("DifferentSpaceResource.saveDifferentSpace(request = {})",request);
        Long differentSapceId = differentSpaceService.saveDifferentSpace(super.getUserId(),request,super.getChainId());
        return Response.status(Response.Status.CREATED).entity(Collections.singletonMap("differentSapceId",differentSapceId)).build();
    }

    /**
     * 编辑异空间
     * @param request
     * @param differentSpaceId
     * @return
     */
    @PUT
    @Path("/{differentSpaceId}")
    public Response updateDifferentSpace(@Valid DifferentSpaceRequest request, @NotNull(message = "differentSpaceId不能为空") @PathParam("differentSpaceId") Long differentSpaceId) {
        log.info("DifferentSpaceResource.updateDifferentSpace(request= {})",request);
        differentSpaceService.updateDifferentSpace(super.getUserId(), request,differentSpaceId,super.getChainId());
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 查询异空间列表
     * @param queryKey
     * @param status
     * @param request
     * @return
     */
    @GET
    public Response queryDifferentSpacePage(@QueryParam("queryKey") String queryKey,
                                    @QueryParam("status") String status,
                                    @Valid @BeanParam PageRequest request) {
        log.info("DifferentSpaceResource.queryDifferentSpacePage(queryKey= {},status= {},request= {})",queryKey,status,request);
        return Response.ok(differentSpaceService.queryDifferentSpacePage(queryKey, status, request)).build();
    }

    /**
     * 查询异空间详情
     * @param differentSpaceId
     * @return
     */
    @GET
    @Path("/{differentSpaceId}")
    public Response queryDifferentSpaceDetail(@NotNull(message = "differentSpaceId is required") @PathParam("differentSpaceId") Long differentSpaceId) {
        log.debug("DifferentSpaceResource.queryDifferentSpaceDetail(differentSpaceId={})",differentSpaceId);
        return Response.ok(differentSpaceService.queryDifferentSpaceDetail(differentSpaceId)).build();
    }

    /**
     * 批量删除
     * @param request
     * @return
     */
    @DELETE
    public Response bathDeleteDifferentSpace(@Valid DeleteDifferentSpaceRequest request) {
        log.debug("DifferentSpaceResource.bathDeleteDifferentSpace(request={})", request);
        differentSpaceService.bathDeleteDifferentSpace(request,super.getChainId());
        return Response.noContent().build();
    }

    /**
     * 状态变更
     * @param request
     * @return
     */
    @POST
    @Path("/updStatus")
    public Response updStatus(@Valid DifferentSpaceStatusRequest request) {
        log.debug("DifferentSpaceResource.updStatus(request = {})", request.toString());
        differentSpaceService.updStatus(request, super.getUserId());
        return Response.status(Response.Status.CREATED).build();
    }

    /**
     * 异空间入场劵记录
     * @param queryKey
     * @param request
     * @return
     */
    @GET
    @Path("/record/{differentSpaceId}")
    public Response queryDifferentSpaceRecord(@NotNull(message = "differentSpaceId is required") @PathParam("differentSpaceId") Long differentSpaceId,
                                        @QueryParam("queryKey") String queryKey,
                                        @QueryParam("isRecover") Boolean isRecover,
                                        @Valid @BeanParam PageRequest request) {
        log.info("DifferentSpaceResource.queryDifferentSpaceRecord(differentSpaceId ={},queryKey= {},isRecover={},request= {})",differentSpaceId,queryKey,isRecover,request);
        return Response.ok(differentSpaceService.queryDifferentSpaceRecord(differentSpaceId,queryKey,isRecover,request)).build();
    }

    /**
     * 异空间入场劵记录详情
     * @return
     */
    @GET
    @Path("/record/detail")
    public Response queryDifferentSpaceRecordDetail(@NotNull(message = "differentSpaceId 不能为空") @QueryParam("differentSpaceId") Long differentSpaceId,
                                                    @NotNull(message = "memberId 不能为空") @QueryParam("memberId") Long memberId,
                                                    @Valid @BeanParam PageRequest request) {
        log.info("DifferentSpaceResource.queryDifferentSpaceRecordDetail(differentSpaceId= {}, memberId={}, request={})",differentSpaceId, memberId, request);
        return Response.ok(differentSpaceService.queryDifferentSpaceRecordDetail(differentSpaceId, memberId, request)).build();
    }

    /**
     * 查询入场劵记录Top数据
     * @param differentSpaceId
     * @return
     */
    @GET
    @Path("/record/top/{differentSpaceId}")
    public Response queryDifferentSpaceRecordQuantity(@NotNull(message = "differentSpaceId is required") @PathParam("differentSpaceId") Long differentSpaceId,
                                                      @QueryParam("queryKey") String queryKey,
                @QueryParam("isRecover") Boolean isRecover) {
        log.info("DifferentSpaceResource.queryDifferentSpaceRecordQuantity(differentSpaceId ={})",differentSpaceId);
        return Response.ok(differentSpaceService.queryDifferentSpaceRecordQuantity(differentSpaceId,queryKey,isRecover)).build();
    }
}
