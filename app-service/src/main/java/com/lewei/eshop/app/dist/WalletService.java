package com.lewei.eshop.app.dist;

import com.alibaba.fastjson.JSON;
import com.lewei.eshop.app.AppErrorDef;
import com.lewei.eshop.app.SysConfig;
import com.lewei.eshop.app.WalletErrorDef;
import com.lewei.eshop.app.client.MemberFeignClient;
import com.lewei.eshop.app.client.PublicFeignClient;
import com.lewei.eshop.app.client.ShopFeignClient;
import com.lewei.eshop.app.client.paas.DistFeignClient;
import com.lewei.eshop.app.client.paas.PayFeignClient;
import com.lewei.eshop.app.client.paas.response.PayConfigResponse;
import com.lewei.eshop.app.client.paas.response.WalletResponse;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.CodeGenerator;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.member.MemberMerchantCoinRequest;
import com.lewei.eshop.common.request.member.OqMemberBalanceRequest;
import com.lewei.eshop.common.request.order.TradeFlowReq;
import com.lewei.eshop.common.request.pub.IsVerifyCodeRequest;
import com.lewei.eshop.common.request.pub.VerifyCodeRequest;
import com.lewei.eshop.common.request.wallet.*;
import com.lewei.eshop.common.vo.member.MemberBalanceVo;
import com.lewei.eshop.common.vo.shop.UserSimpleVO;
import com.lewei.eshop.common.vo.wallet.*;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberOqRef;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.OrderRefund;
import com.lewei.eshop.entity.order.OrderRefundLog;
import com.lewei.eshop.entity.order.types.OrderRefundMoneyStateEnum;
import com.lewei.eshop.entity.order.types.OrderRefundState;
import com.lewei.eshop.entity.order.types.OrderRefundStateEnum;
import com.lewei.eshop.entity.trade.types.TradeTypeEnum;
import com.lewei.eshop.entity.wallet.WalletWithdraw;
import com.lewei.eshop.entity.wallet.WalletWithdrawChannel;
import com.lewei.eshop.entity.wallet.WalletWithdrawConfig;
import com.lewei.eshop.entity.wallet.types.*;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.InputStreamUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.ISimpleBaseDaoSupport;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.db.saas.IIdWorker;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.QueryParam;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.lewei.eshop.entity.wallet.types.MoneyStatusEnum.*;
import static com.lewei.eshop.entity.wallet.types.WithDrawStatusEnum.*;
import static com.lewei.eshop.entity.wallet.types.WithDrawTransferTypeEnum.*;

/**
* 钱包
* <AUTHOR>
* @date 2020/5/20
**/
@Component(value= "walletService")
@Service
@Transactional
@Slf4j
public class WalletService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private PayFeignClient paasHttpProxy;
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private DistFeignClient distFeignClient;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private PayFeignClient payFeignClient;
    @Autowired
    private IIdWorker idWorker;
    @Autowired
    private ShopFeignClient shopFeignClient;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private RedisCacheProvider redisCacheProvider;

    /**
     * 提现记录
     */
    public Pagination queryWithdrawPage(String queryKey, String withdrawStatus, String moneyStatus, String applicationType, String batchNO, Date st, Date et
            , String channel, String transferType ,String orderName, String order,  PageRequest request,String withdrawOrderNO,String withdrawPlatform,String oqId,Long memberId) {
        List<String> withdrawStatusList = null;
        List<String> moneyStatusList = null;
        List<String> channelList = null;
        List<String> transferTypeList = null;
        List<String> applicationTypeList = null;
        List<String> withdrawPlatformList = null;
        if(StringUtils.isNotBlank(withdrawStatus)) {
            withdrawStatusList = Arrays.stream(withdrawStatus.split(",")).collect(Collectors.toList());

        }
        if(StringUtils.isNotBlank(moneyStatus)) {
            moneyStatusList = Arrays.stream(moneyStatus.split(",")).collect(Collectors.toList());

        }
        if(StringUtils.isNotBlank(channel)) {
            channelList = Arrays.stream(channel.split(",")).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(transferType)) {
            transferTypeList = Arrays.stream(transferType.split(",")).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(applicationType)) {
            applicationTypeList = Arrays.stream(applicationType.split(",")).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(withdrawPlatform)) {
            withdrawPlatformList = Arrays.stream(withdrawPlatform.split(",")).collect(Collectors.toList());
        }

        Ssqb query = Ssqb.create("com.lewei.eshop.app.wallet.queryWithdrawPage")
                .setParam("queryKey",queryKey).setParam("withdrawStatusList", withdrawStatusList)
                .setParam("moneyStatusList", moneyStatusList).setParam("batchNO", batchNO)
                .setParam("st", st).setParam("channelList", channelList)
                .setParam("et", et).setParam("transferTypeList", transferTypeList)
                .setParam("orderName", orderName).setParam("order", order)
                .setParam("applicationTypeList", applicationTypeList)
                .setParam("withdrawOrderNO", withdrawOrderNO)
                .setParam("withdrawPlatformList", withdrawPlatformList)
                .setParam("memberId", memberId)
                .setParam("oqId", oqId)
                .setParam("pageNo", request.getPageNo())
                .setParam("pageSize", request.getPageSize())
                ;
        return dao.findForPage(query);
    }

    /**
     * 提现记录-导出
     */
    public List<WithdrawVo> exportWithdrawList(String queryKey, String withdrawStatus, String moneyStatus, String batchNO, Date st, Date et
            , String channel, String transferType, String oqId, String applicationType) {
        List<String> withdrawStatusList = null;
        List<String> moneyStatusList = null;
        List<String> channelList = null;
        List<String> transferTypeList = null;
        List<String> applicationTypeList = null;
        if(StringUtils.isNotBlank(withdrawStatus)) {
            withdrawStatusList = Arrays.stream(withdrawStatus.split(",")).collect(Collectors.toList());

        }
        if(StringUtils.isNotBlank(moneyStatus)) {
            moneyStatusList = Arrays.stream(moneyStatus.split(",")).collect(Collectors.toList());

        }
        if(StringUtils.isNotBlank(channel)) {
            channelList = Arrays.stream(channel.split(",")).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(transferType)) {
            transferTypeList = Arrays.stream(transferType.split(",")).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(applicationType)) {
            applicationTypeList = Arrays.stream(applicationType.split(",")).collect(Collectors.toList());
        }
        Ssqb query = Ssqb.create("com.lewei.eshop.app.wallet.queryWithdrawPage")
                .setParam("queryKey",queryKey).setParam("withdrawStatusList", withdrawStatusList)
                .setParam("moneyStatusList", moneyStatusList).setParam("batchNO", batchNO)
                .setParam("st", st).setParam("channelList", channelList)
                .setParam("oqId", oqId)
                .setParam("et", et).setParam("transferTypeList", transferTypeList)
                .setParam("applicationTypeList", applicationTypeList)
                ;
        return dao.findForList(query,WithdrawVo.class);
    }


    /**
     * 转账记录
     */
    public Pagination queryTransferPage(String orderName, String order, Date st, Date et, PageRequest request) {
        Ssqb query = Ssqb.create("com.lewei.eshop.app.wallet.queryTransferPage")
                .setParam("st", st)
                .setParam("et", et)
                .setParam("orderName", orderName)
                .setParam("order", order)
                .setParam("pageNo", request.getPageNo())
                .setParam("pageSize", request.getPageSize())
                ;
        return dao.findForPage(query);
    }

    /**
     * 转账记录-导出
     */
    public List<TransferRecordVo> exportTransferList(Date st, Date et) {
        Ssqb query = Ssqb.create("com.lewei.eshop.app.wallet.queryTransferPage")
                .setParam("st", st)
                .setParam("et", et)
                ;
        return dao.findForList(query, TransferRecordVo.class);
    }

    /**
     * 提现审批
     * @param request
     * @param opUserId
     */
    public void approveWithDraw(ApproveWithDrawRequest request, Long opUserId, Long chainId) {

        List<Long> withDrawIds = Arrays.stream(request.getWithDrawIds().split(",")).map(item->Long.parseLong(item)).collect(Collectors.toList());
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.in("id", withDrawIds))
                .and(Restrictions.eq("withdrawStatus", S_WWS_REVIEWING.value()));
        List<WalletWithdraw> withdraws = dao.queryList(query, WalletWithdraw.class);
        Date now = new Timestamp(System.currentTimeMillis());
        //提现配置
        WalletWithdrawConfig withdrawConfig = this.queryWalletWithdrawConfig();
        //税率
        BigDecimal taxRate = withdrawConfig.getTaxRate();
        if(ListUtil.isNotEmpty(withdraws)) {
            this.checkOqBinding(withdraws);
            Boolean agree = request.getAgree();
            String transferType = request.getTransferType();
            if(BooleanUtils.isTrue(agree)) {
                //同意提现
                if(StringUtils.isBlank(transferType)) {
                    throw new BizCoreRuntimeException(WalletErrorDef.PARAM_IS_REQUIRED, BizMessageSource.getInstance().getMessage("cem10009"));
                }
                WithDrawTransferTypeEnum transferTypeEnum = WithDrawTransferTypeEnum.fromCode(transferType);
                //处理批次号
                String batchNO = CodeGenerator.getShortCode("B");
                if(S_WWT_OFFLINE_TRANS.equals(transferTypeEnum) || S_WWT_BANK_TRANS.equals(transferTypeEnum)) {
                    //下线打款
                    transferByOffLine(withdraws, opUserId, batchNO, transferType, taxRate);
                } else {
                    //微信 支付宝 打款
                    validateWithdraw(withdraws, withdrawConfig, transferTypeEnum);

                    Long payConfigId = null;
                    if(S_WWT_WX.equals(transferTypeEnum)) {
                        payConfigId = withdrawConfig.getWxPlatformPayConfigId();
                    } else {
                        payConfigId = withdrawConfig.getAlipayPlatformPayConfigId();
                    }

                    List<TransferBizRequest.Transfer> transfers = new ArrayList<>();
                    //微信 企业转账
                    for(WalletWithdraw withdraw : withdraws) {

                        withdraw.setBatchNO(batchNO);
                        withdraw.setWithdrawStatus(S_WWS_PASSED.value());
                        withdraw.setMoneyStatus(S_WWMS_TRANSING.value());
                        withdraw.setMoneyRemark(BizMessageSource.getInstance().getI18nMessage("cem10010",transferTypeEnum.desc()));
                        withdraw.setTransferType(transferType);
                        withdraw.setOpUserId(opUserId);
                        withdraw.setOpTime(now);
                        withdraw.setUpdated(now);

                        cutTax(withdraw, taxRate);

                        TransferBizRequest.Transfer transfer = new TransferBizRequest.Transfer();
                        transfer.setAmount(withdraw.getCutTaxAmount());
                        transfer.setBizTransferCode(withdraw.getWithdrawOrderNo());
                        transfer.setDesc(BizMessageSource.getInstance().getMessage("cem10011"));
                        transfer.setOpenId(withdraw.getOpenId());
                        if(S_WWT_ALIPAY_TRANS.equals(transferTypeEnum)) {
                            //支付宝提现 必须传递姓名
                            transfer.setRealName(withdraw.getRealName());
                            transfer.setOpenId(withdraw.getAccount());
                        }

                        transfers.add(transfer);
                    }
                    TransferBizRequest requestObj = new TransferBizRequest();
                    requestObj.setPayConfigId(payConfigId);
                    requestObj.setNotifyUrl(sysConfig.getEshopService() + "/api/app/transfer/callback");
                    requestObj.setTransfers(transfers);
                    paasHttpProxy.callTransferApi(requestObj);

                    dao.batchUpdate(withdraws, WalletWithdraw.class);

                }

            } else {
                //拒绝提现
                if(StringUtils.isBlank(request.getRejectReason())) {
                    throw new BizCoreRuntimeException(WalletErrorDef.PARAM_IS_REQUIRED, BizMessageSource.getInstance().getMessage("cem10014"));
                }

                List<WalletWithdraw> distWithDraw = withdraws.stream().filter(a->Objects.equals(a.getWithdrawPlatform(),WithDrawPlatformEnum.dist.value())
                        || StringUtils.isEmpty(a.getWithdrawPlatform())).collect(Collectors.toList());

                if (ListUtil.isNotEmpty(distWithDraw)){
                    List<WalletIncomeRequest.Income> incomes = new ArrayList<>();
                    Set<Long> memberIds = distWithDraw.stream().map(WalletWithdraw::getMemberId).collect(Collectors.toSet());
                    SaasQueryBuilder queryMember = SaasQueryBuilder.where(Restrictions.in("id", memberIds));
                    List<Member> members = dao.queryList(queryMember, Member.class);
                    //<memberId, userCode>
                    Map<Long, String> mapping = members.stream().collect(Collectors.toMap(Member::getId, Member::getUserCode));
                    for(WalletWithdraw withdraw : distWithDraw) {
                        Long memberId = withdraw.getMemberId();
                        withdraw.setWithdrawStatus(S_WWS_REJECTED.value());
                        withdraw.setRejectReason(request.getRejectReason());
                        withdraw.setOpUserId(opUserId);
                        withdraw.setOpTime(now);
                        withdraw.setUpdated(now);

                        WalletIncomeRequest.Income income = new WalletIncomeRequest.Income();
                        income.setAmount(withdraw.getWithdrawAmount());
                        income.setContent(BizMessageSource.getInstance().getMessage("cem10012"));
                        income.setRemark(BizMessageSource.getInstance().getMessage("cem10013"));
                        income.setUserCode(mapping.get(memberId));

                        incomes.add(income);
                    }

                    //提现补回 还原钱包金额
                    WalletIncomeRequest requestObj = new WalletIncomeRequest();
                    requestObj.setIncomeType(WalletRecordTypeEnum.WITHDRAW_BACK.value());
                    requestObj.setIncomes(incomes);
                    distFeignClient.callWalletIncome(requestObj);

                    dao.batchUpdate(distWithDraw, WalletWithdraw.class);
                }

                List<WalletWithdraw> eshopWithDraw = withdraws.stream().filter(a->Objects.equals(a.getWithdrawPlatform(),WithDrawPlatformEnum.eshop.value())).collect(Collectors.toList());
                if (ListUtil.isNotEmpty(eshopWithDraw)){
                    for(WalletWithdraw withdraw : eshopWithDraw) {
                        Long memberId = withdraw.getMemberId();
                        withdraw.setWithdrawStatus(S_WWS_REJECTED.value());
                        withdraw.setRejectReason(request.getRejectReason());
                        withdraw.setOpUserId(opUserId);
                        withdraw.setOpTime(now);
                        withdraw.setUpdated(now);

                        MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                        balanceRequest.setGift(BigDecimal.ZERO);
                        balanceRequest.setBalance(withdraw.getWithdrawAmount());
                        balanceRequest.setMemberId(memberId);
                        balanceRequest.setPlId(withdraw.getId());
                        balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem10012"));
                        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                        memberFeignClient.memberBalanceHandle(balanceRequest);

                        dao.update(withdraw);

                    }
                }

                List<WalletWithdraw> merchantWithDraw = withdraws.stream().filter(a->Objects.equals(a.getWithdrawPlatform(),WithDrawPlatformEnum.merchant.value())).collect(Collectors.toList());
                if (ListUtil.isNotEmpty(merchantWithDraw)){
                    for(WalletWithdraw withdraw : merchantWithDraw) {
                        Long memberId = withdraw.getMemberId();
                        withdraw.setWithdrawStatus(S_WWS_REJECTED.value());
                        withdraw.setRejectReason(request.getRejectReason());
                        withdraw.setOpUserId(opUserId);
                        withdraw.setOpTime(now);
                        withdraw.setUpdated(now);

                        MemberMerchantCoinRequest balanceRequest = new MemberMerchantCoinRequest();
                        balanceRequest.setCoin(withdraw.getWithdrawAmount());
                        balanceRequest.setMemberId(memberId);
                        balanceRequest.setPlId(withdraw.getId());
                        balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem10012"));
                        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                        memberFeignClient.memberMerchantCoinHandler(balanceRequest);
                        dao.update(withdraw);

                    }
                }

                List<WalletWithdraw> babyWithDraw = withdraws.stream().filter(a->Objects.equals(a.getWithdrawPlatform(),WithDrawPlatformEnum.babywallet.value())).collect(Collectors.toList());
                if (ListUtil.isNotEmpty(babyWithDraw)){
                    for(WalletWithdraw withdraw : babyWithDraw) {
                        Long memberId = withdraw.getMemberId();
                        withdraw.setWithdrawStatus(S_WWS_REJECTED.value());
                        withdraw.setRejectReason(request.getRejectReason());
                        withdraw.setOpUserId(opUserId);
                        withdraw.setOpTime(now);
                        withdraw.setUpdated(now);
                        //查询oqId
                        MemberOqRef memberOqRef = this.queryOqRef(withdraw.getMemberId());
                        OqMemberBalanceRequest balanceRequest = new OqMemberBalanceRequest();
                        balanceRequest.setOqId(memberOqRef.getOqId());
                        balanceRequest.setMemberId(memberId);
                        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                        balanceRequest.setFlag(false);
                        balanceRequest.setCarriedForward(withdraw.getWithdrawAmount());
                        balanceRequest.setPcType(true);
                        balanceRequest.setTitle("提现退回");
                        balanceRequest.setRejectReason(request.getRejectReason());
                        memberFeignClient.oqMemberBalanceHandler(balanceRequest);
                        dao.update(withdraw);

                    }
                }
                //TODO 消息推送
                //withdraws.forEach(a->messagePushService.withdrawFail(a));
            }
        }
    }

    /**
     * 重新打款
     * @param request
     * @param opUserId
     */
    public void reTransfer(ReTransferRequest request, Long opUserId, Long chainId) {

        List<Long> withDrawIds = Arrays.stream(request.getWithDrawIds().split(",")).map(item->Long.parseLong(item)).collect(Collectors.toList());
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.in("id", withDrawIds))
                .and(Restrictions.eq("withdrawStatus", S_WWS_PASSED.value()))
                .and(Restrictions.eq("moneyStatus", S_WWS_FALIED.value()));
        List<WalletWithdraw> withdraws = dao.queryList(query, WalletWithdraw.class);
        Date now = new Timestamp(System.currentTimeMillis());
        //提现配置
        WalletWithdrawConfig withdrawConfig = this.queryWalletWithdrawConfig();
        //税率
        BigDecimal taxRate = withdrawConfig.getTaxRate();
        if(ListUtil.isNotEmpty(withdraws)) {
            String transferType = request.getTransferType();
            WithDrawTransferTypeEnum transferTypeEnum = WithDrawTransferTypeEnum.fromCode(transferType);
            if(S_WWT_OFFLINE_TRANS.equals(transferTypeEnum) || S_WWT_BANK_TRANS.equals(transferTypeEnum)) {
                //下线打款
                transferByOffLine(withdraws, opUserId, null, transferType, taxRate);
            } else {

                validateWithdraw(withdraws, withdrawConfig, transferTypeEnum);

                Long payConfigId = null;
                if(S_WWT_WX.equals(transferTypeEnum)) {
                    payConfigId = withdrawConfig.getWxPlatformPayConfigId();
                } else {
                    payConfigId = withdrawConfig.getAlipayPlatformPayConfigId();
                }

                List<TransferBizRequest.Transfer> transfers = new ArrayList<>();
                //微信 企业转账
                for(WalletWithdraw withdraw : withdraws) {

                    withdraw.setMoneyStatus(S_WWMS_TRANSING.value());
                    withdraw.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem10015"));
                    withdraw.setTransferType(transferType);
                    withdraw.setOpUserId(opUserId);
                    withdraw.setUpdated(now);

                    TransferBizRequest.Transfer transfer = new TransferBizRequest.Transfer();
                    transfer.setAmount(withdraw.getCutTaxAmount());
                    transfer.setBizTransferCode(withdraw.getWithdrawOrderNo());
                    transfer.setDesc(BizMessageSource.getInstance().getMessage("cem10011"));
                    transfer.setOpenId(withdraw.getOpenId());
                    if(S_WWT_ALIPAY_TRANS.equals(transferTypeEnum)) {
                        //支付宝提现 必须传递姓名
                        transfer.setRealName(withdraw.getRealName());
                        transfer.setOpenId(withdraw.getAccount());
                    }

                    transfers.add(transfer);

                }
                TransferBizRequest requestObj = new TransferBizRequest();
                requestObj.setPayConfigId(payConfigId);
                requestObj.setNotifyUrl(sysConfig.getEshopService() + "/api/app/transfer/callback");
                requestObj.setTransfers(transfers);
                paasHttpProxy.callTransferApi(requestObj);

                dao.batchUpdate(withdraws, WalletWithdraw.class);
            }
        }
    }

    /**
     * 下线打款
     */
    private void transferByOffLine(List<WalletWithdraw> withdraws, Long opUserId, String batchNO, String transferType, BigDecimal taxRate) {
        Date now = new Timestamp(System.currentTimeMillis());
        for(WalletWithdraw withdraw : withdraws) {
            withdraw.setWithdrawStatus(S_WWS_PASSED.value());
            withdraw.setMoneyStatus(S_WWS_SUC.value());
            withdraw.setTransferType(transferType);
            withdraw.setOpUserId(opUserId);
            if(StringUtils.isNotBlank(batchNO)) {
                withdraw.setOpTime(now);
            }
            withdraw.setUpdated(now);
            withdraw.setBatchNO(batchNO);

            cutTax(withdraw, taxRate);

            this.handelOrderRefund(withdraw);
        }
        dao.batchUpdate(withdraws, WalletWithdraw.class);
        //TODO 消息推送
        //withdraws.forEach(a->messagePushService.withdrawSuccess(a));
    }

    private WalletWithdraw cutTax(WalletWithdraw withdraw, BigDecimal taxRate) {
        if(taxRate.compareTo(BigDecimal.ZERO) > 0) {
            //扣税金额 = 提现金额 * 税率
            BigDecimal taxAmount = withdraw.getWithdrawAmount().multiply(taxRate).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            //税后金额
            BigDecimal cutTaxAmount = withdraw.getWithdrawAmount().subtract(taxAmount);
            withdraw.setTaxAmount(taxAmount);
            withdraw.setCutTaxAmount(cutTaxAmount);
        } else {
            withdraw.setTaxAmount(BigDecimal.ZERO);
            withdraw.setCutTaxAmount(withdraw.getWithdrawAmount());
        }
        return withdraw;
    }

    /**
     * 处理转账回调
     * @param request
     * @return
     */
    public String handleTransferCallBack(HttpServletRequest request) {

        try {
            String httpBody = InputStreamUtils.InputStreamTOString(request.getInputStream(),"utf-8");
            log.info("handleTransferCallBack callbackData : {}", httpBody);
            TransferCallBackData callBackData = JSON.parseObject(httpBody, TransferCallBackData.class);
            Long chainId = callBackData.getChainId();
            //转账回调数据
            List<TransferCallBackData.Transfer> transfers = callBackData.getTransfers();
            Date now = new Timestamp(System.currentTimeMillis());
            XcrmThreadContext.setChainId(chainId);
            XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
            if(ListUtil.isNotEmpty(transfers)) {
                //<业务转账订单号，转账对象>
                Map<String, TransferCallBackData.Transfer> transferMap = transfers.stream()
                        .collect(Collectors.toMap(TransferCallBackData.Transfer::getBizTransferCode, item->item));
                Set<String> bizTransferCodes = transferMap.keySet();
                SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.in("withdrawOrderNo", new ArrayList(bizTransferCodes)))
                        .and(Restrictions.eq("dataStatus", 1));
                List<WalletWithdraw> withdraws = dao.queryList(query, WalletWithdraw.class);
                for(WalletWithdraw withdraw : withdraws) {
                    TransferCallBackData.Transfer transfer = transferMap.get(withdraw.getWithdrawOrderNo());
                    if(transfer != null) {
                        String transferStatus = transfer.getStatus();
                        if("TRANSFER_SUCC".equals(transferStatus)) {
                            withdraw.setMoneyStatus(S_WWS_SUC.value());
                            withdraw.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem10016"));
                            handelOrderRefund(withdraw);

                        } else if("TRANSFER_FAILED".equals(transferStatus)) {
                            withdraw.setMoneyStatus(S_WWS_FALIED.value());
                            withdraw.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem10017") + transfer.getErrorReason() + "(" + transfer.getErrorCode() + ")");
                            if (ApplicationTypeEnum.S_WWA_REFUND.value().equals(withdraw.getApplicationType())){
                                String refundCode = withdraw.getPlId();
                                if (StringUtils.isNotEmpty(refundCode)){
                                    OrderRefund orderRefund = new OrderRefund();
                                    orderRefund.setMoneyState(OrderRefundMoneyStateEnum.S_ORM_REFUND_FAILED.value());
                                    orderRefund.setUpdated(DateFormatUtils.getNow());
                                    orderRefund.setRefundCode(withdraw.getPlId());
                                    dao.update(orderRefund);
                                    this.saveRefundLog(refundCode,BizMessageSource.getInstance().getMessage("cem10018"), withdraw.getMoneyRemark(),"sys");
                                }
                            }
                        } else {
                            log.warn("未知转账状态 ：{} {}", withdraw.getWithdrawOrderNo(), transferStatus);
                        }
                        withdraw.setNotifyTime(now);
                        withdraw.setTransferCode(transfer.getTransferCode());




                    }
                }
                dao.batchUpdate(withdraws, WalletWithdraw.class);
                //消息推送

                try {
                    withdraws.forEach(a->{
                        if (S_WWS_SUC.value().equals(a.getMoneyStatus())){
                            //TODO
                            //messagePushService.withdrawSuccess(a);
                        }else if (S_WWS_FALIED.value().equals(a.getMoneyStatus())){
                            a.setRejectReason("转账失败，请联系客服！");
                            //messagePushService.withdrawFail(a);
                        }
                    });
                } catch (Exception e) {
                    log.error("handleTransferCallBack.send message fail",e);
                }
            }

        }  catch (Exception e) {
            log.error("#########################handleTransferCallBack#########################",e);
            //throw e;
        } finally {
            XcrmThreadContext.removeAccessType();
            XcrmThreadContext.removeChainId();
        }
        return "ok";
    }



    public List<WalletWithdrawChannel> queryWithdrawChannels(Long memberId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("memberId", memberId))
                .and(Restrictions.eq("dataStatus", 1));
        return dao.queryList(query, WalletWithdrawChannel.class);
    }

    public WalletWithdrawChannel queryWithdrawChannel(Long channelId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", channelId))
                .and(Restrictions.eq("dataStatus", 1));
        return dao.query(query, WalletWithdrawChannel.class);
    }

    /**
     * 钱包记录
     */
    public Pagination queryWalletRecordPage(String userCode, Long st, Long et, String recordType, PageRequest request) {
        return distFeignClient.queryWalletRecordPage(userCode, st, et, true, recordType, request.getPageNo(), request.getPageSize());
    }

    /**
     * 钱包余额
     */
    public BigDecimal queryWalletBalance(String userCode, Long chainId) {
        WalletResponse walletResponse = distFeignClient.queryWalletBalance(userCode);
        if(walletResponse != null) {
            return walletResponse.getBalance();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 查询提现配置
     */
    public WalletWithdrawConfig queryWalletWithdrawConfig() {
        return dao.query(SaasQueryBuilder.create(), WalletWithdrawConfig.class);
    }

    /**
     * 更新提现配置
     */
    public WalletWithdrawConfig updateWalletWithdrawConfig(WithDrawConfigRequest request, Long opUserId) {
        WalletWithdrawConfig walletWithdrawConfig = this.queryWalletWithdrawConfig();

        if(request.getWxPlatformPayConfigId() != null && request.getWxPlatformPayConfigId().compareTo(0L) > 0) {
            PayConfigResponse payConfigResponse = payFeignClient.queryPayConfigById(request.getWxPlatformPayConfigId());
            if(payConfigResponse == null || BooleanUtils.isFalse(payConfigResponse.getStatus())) {
                throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_PAY_CONFIG_NOT_FOUND);
            }
            if(!"wx".equals(payConfigResponse.getPayType())) {
                throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_PAY_CONFIG_NOT_MATCH);
            }
        }

        if(request.getAlipayPlatformPayConfigId() != null && request.getAlipayPlatformPayConfigId().compareTo(0L) > 0) {
            PayConfigResponse payConfigResponse = payFeignClient.queryPayConfigById(request.getAlipayPlatformPayConfigId());
            if(payConfigResponse == null || BooleanUtils.isFalse(payConfigResponse.getStatus())) {
                throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_PAY_CONFIG_NOT_FOUND);
            }
            if(!"alipay".equals(payConfigResponse.getPayType())) {
                throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_PAY_CONFIG_NOT_MATCH);
            }
        }

        walletWithdrawConfig.setTaxRate(request.getTaxRate());
        walletWithdrawConfig.setLimitFee(request.getLimitFee());
        walletWithdrawConfig.setLimitTimes(request.getLimitTimes());
        walletWithdrawConfig.setWithdrawalRules(request.getWithdrawalRules());
        walletWithdrawConfig.setAutomatic(request.getAutomatic());
        walletWithdrawConfig.setAlipayPlatformPayConfigId(request.getAlipayPlatformPayConfigId());
        walletWithdrawConfig.setWxPlatformPayConfigId(request.getWxPlatformPayConfigId());
        walletWithdrawConfig.setUpdateBy(opUserId);
        walletWithdrawConfig.setUpdated(new Timestamp(System.currentTimeMillis()));
        dao.update(walletWithdrawConfig, ISimpleBaseDaoSupport.UpdateMode.MAX);

        return walletWithdrawConfig;
    }

//    public boolean validateWithdraw(WithDrawValidateRequest request) {
//        List<Long> withDrawIds = request.getWithdrawIds();
//        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.in("id", withDrawIds))
//                .and(Restrictions.eq("withdrawStatus", S_WWS_REVIEWING.value()));
//        List<WalletWithdraw> withdraws = dao.queryList(query, WalletWithdraw.class);
//        if(CollectionUtils.isNotEmpty(withdraws)) {
//            //提现配置
//            WalletWithdrawConfig withdrawConfig = this.queryWalletWithdrawConfig();
//            //转账方式
//            WithDrawTransferTypeEnum transferTypeEnum = WithDrawTransferTypeEnum.fromCode(request.getTransferType());
//
//            validateWithdraw(withdraws, withdrawConfig, transferTypeEnum);
//            return true;
//        }
//        return false;
//    }

    /**
     * 查询收款人信息
     */
    public SearchPayeeVo queryPayee(String mobile) {
        Ssqb queryMember = Ssqb.create("com.lewei.eshop.app.wallet.queryPayeeInfo")
                .setParam("mobile", mobile);
        SearchPayeeVo searchPayeeVo = dao.findForObj(queryMember, SearchPayeeVo.class);
        if(searchPayeeVo == null){
            throw new BizCoreRuntimeException(WalletErrorDef.NO_HAVE_CERTAIN_USER);
        }
        Ssqb queryMemberAccounts = Ssqb.create("com.lewei.eshop.app.wallet.queryPayeeAccounts")
                .setParam("memberId", searchPayeeVo.getMemberId());
        List<SearchPayeeAccountsVo> accountList = dao.findForList(queryMemberAccounts, SearchPayeeAccountsVo.class);
        Ssqb queryMemberLastAccount = Ssqb.create("com.lewei.eshop.app.wallet.queryPayeeLastAccount")
                .setParam("memberId", searchPayeeVo.getMemberId());
        SearchPayeeAccountsVo  memberLastAccount = dao.findForObj(queryMemberLastAccount,SearchPayeeAccountsVo.class);
        if(memberLastAccount != null){
            accountList.add(0,memberLastAccount);
        }
        List<SearchPayeeAccountsVo> memberAccounts = accountList.stream().distinct().collect(Collectors.toList());
        searchPayeeVo.setAccounts(memberAccounts);
        if (searchPayeeVo.getBalance() != null){
            ChainConfig chainConfig = publicFeignClient.queryChainConfig();
            BigDecimal withDrawMoney = searchPayeeVo.getBalance().divide(chainConfig.getMoneyRatio(),2,BigDecimal.ROUND_HALF_UP);
            searchPayeeVo.setWithDrawMoney(withDrawMoney);
        }

        return searchPayeeVo;
    }


    /**
     * 验证是否可以提现
     * @param withdraws                 提现申请记录
     * @param withdrawConfig            提现配置
     * @param transferTypeEnum          打款方式
     */
    private void validateWithdraw(List<WalletWithdraw> withdraws, WalletWithdrawConfig withdrawConfig, WithDrawTransferTypeEnum transferTypeEnum) {
        //微信 支付宝 打款
        List<WalletWithdraw> wxWalletWithdraws = withdraws.stream().filter(item -> item.getChannel().equals(WithDrawChannelEnum.wx.value())).collect(Collectors.toList());
        if(wxWalletWithdraws.size() > 0 && !S_WWT_WX.equals(transferTypeEnum)) {
            throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_CHANNEL_ERROR);
        }
        List<WalletWithdraw> alipayWalletWithdraws = withdraws.stream().filter(item -> item.getChannel().equals(WithDrawChannelEnum.alipay.value())).collect(Collectors.toList());
        if(alipayWalletWithdraws.size() > 0 && !S_WWT_ALIPAY_TRANS.equals(transferTypeEnum)) {
            throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_CHANNEL_ERROR);
        }

        if(S_WWT_WX.equals(transferTypeEnum) && withdrawConfig.getWxPlatformPayConfigId() == null) {
            throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_PAY_CONFIG_NO_SET);
        }

        if(S_WWT_ALIPAY_TRANS.equals(transferTypeEnum) && withdrawConfig.getAlipayPlatformPayConfigId() == null) {
            throw new BizCoreRuntimeException(WalletErrorDef.WITH_DRAW_PAY_CONFIG_NO_SET);
        }

    }

    /**
     * 转账打款
     * @param request TransferMoneyRequest
     */
    public WalletWithdraw  approveWithDraw(TransferMoneyRequest request, Long opUserId, String appId) {
        if (BooleanUtils.isNotTrue(request.getIsRefund())){
            if ((request.getWithdrawAmount()).compareTo(BigDecimal.valueOf(10000)) >= 0) {
                if (StringUtils.isNotBlank(request.getCode())) {
                    this.verifyCode(request.getCode());
                }else {
                    throw new BizCoreRuntimeException(WalletErrorDef.PLEASE_ENTER_THE_VERIFICATION_CODE);
                }
            }
        }

        Long memberId = request.getMemberId();
        SaasQueryBuilder queryMember = SaasQueryBuilder.where(Restrictions.eq("id", memberId == null ? 0 : memberId))
                .and(Restrictions.eq("dataStatus", 1));
        Member member = dao.query(queryMember, Member.class);

        WalletWithdraw withdraw = new WalletWithdraw();
        withdraw.setId(idWorker.nextId());
        BigDecimal balance = BigDecimal.ZERO;
        Date now = new Timestamp(System.currentTimeMillis());
        WithDrawTransferTypeEnum transferTypeEnum = WithDrawTransferTypeEnum.fromCode(request.getTransferType());

        validateTransfer(transferTypeEnum, request, member);

        Long payConfigId = request.getPlatformPayConfigId();
        if (memberId != null && !memberId.equals(0L)) {
            MemberBalanceVo balanceVo = memberFeignClient.getMemberBalance(memberId, null);
            balance = balanceVo.getBalance().add(balanceVo.getGift());
            if (BooleanUtils.isTrue(request.getIsSubBalance())) {
                if (member == null) {
                    throw new BizCoreRuntimeException(WalletErrorDef.NOT_SUB_BALANCE);
                }
                if (balance.compareTo(request.getWithdrawAmount()) < 0) {
                    throw new BizCoreRuntimeException(WalletErrorDef.BALANCE_NOT_ENOUGH);
                }
                MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                balanceRequest.setGift(BigDecimal.ZERO);
                balanceRequest.setBalance(request.getWithdrawAmount());
                balanceRequest.setMemberId(request.getMemberId());
                balanceRequest.setPlId(withdraw.getId());
                balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem10019"));
                balanceRequest.setType(MemberBalanceRecordTypeEnum.consume.value());
                memberFeignClient.memberBalanceHandle(balanceRequest);
                balanceVo = memberFeignClient.getMemberBalance(request.getMemberId(), null);
                balance = balanceVo.getBalance().add(balanceVo.getGift());
            }
        }
        withdraw.setMemberId(memberId == null ? 0 : memberId);
        withdraw.setRealName(request.getRealName());
        withdraw.setWithdrawAmount(request.getWithdrawAmount());
        withdraw.setCutTaxAmount(request.getWithdrawAmount());
        withdraw.setWithdrawStatus(S_WWS_PASSED.value());
        withdraw.setMoneyStatus(S_WWMS_TRANSING.value());
        withdraw.setWithdrawOrderNo(CodeGenerator.getShortCode("WD"));
        withdraw.setCreated(new Timestamp(System.currentTimeMillis()));
        withdraw.setAccount(request.getAccount());
        withdraw.setChannel(request.getType());
        withdraw.setTransferType(request.getTransferType());
        withdraw.setBatchNO(CodeGenerator.getShortCode("B"));
        withdraw.setMoneyRemark(BizMessageSource.getInstance().getI18nMessage("cem10010",transferTypeEnum.desc()));
        withdraw.setSysRemark(request.getPlId());
        withdraw.setPlId(request.getPlId());
//        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("appId", appId))
//                .and(Restrictions.eq("memberId", memberId == null ? 0 : memberId));
//        MemberMaRef memberMaRef = dao.query(query, MemberMaRef.class);
//        if(member != null) {
//            withdraw.setOpenId(memberMaRef != null ? memberMaRef.getOpenId() : member.getOpenId());
//        }else {
//            withdraw.setOpenId(null);
//        }

        if(member != null) {
            withdraw.setOpenId(member.getOpenId());
            withdraw.setAppId(member.getAppId());
        }
        if (request.getMaxWithdrawAmount() != null){
            withdraw.setMaxWithdrawAmount(request.getMaxWithdrawAmount());
        }else {
            withdraw.setMaxWithdrawAmount(balance);

        }
        withdraw.setWithdrawPlatform(request.getWithdrawPlatform());

        withdraw.setApplicationType(request.getApplicationType());
        withdraw.setIsSubBalance(request.getIsSubBalance());
        withdraw.setOpUserId(opUserId);
        withdraw.setOpTime(now);
        dao.create(withdraw);

        List<TransferBizRequest.Transfer> transfers = new ArrayList<>();
        TransferBizRequest.Transfer transfer = new TransferBizRequest.Transfer();
        transfer.setAmount(withdraw.getWithdrawAmount());
        transfer.setBizTransferCode(withdraw.getWithdrawOrderNo());
        transfer.setDesc(BizMessageSource.getInstance().getMessage("cem10019"));
        transfer.setOpenId(withdraw.getOpenId());
        if(S_WWT_ALIPAY_TRANS.equals(transferTypeEnum)) {
            //支付宝提现 必须传递姓名
            transfer.setRealName(withdraw.getRealName());
            transfer.setOpenId(withdraw.getAccount());
        }
        transfers.add(transfer);
        TransferBizRequest requestObj = new TransferBizRequest();
        requestObj.setPayConfigId(payConfigId);
        requestObj.setNotifyUrl(sysConfig.getEshopService() + "/api/app/transfer/callback");
        requestObj.setTransfers(transfers);
        payFeignClient.callTransferApi(requestObj);

        // 删除验证码
        this.deleteCode();
        return withdraw;
    }

    /**
     * 验证是否可以打款
     * @param transferTypeEnum          打款方式
     * @param request                   参数
     * @param member                    会员表
     */
    private void validateTransfer(WithDrawTransferTypeEnum transferTypeEnum, TransferMoneyRequest request, Member member) {

        if (S_WWT_WX.equals(transferTypeEnum)) {
            if (BooleanUtils.isFalse(request.getIdentity())) {
                throw new BizCoreRuntimeException(WalletErrorDef.IDENTITY_ERROR);
            }
            if (member == null) {
                throw new BizCoreRuntimeException(WalletErrorDef.NOT_MEMBER);
            }
        }
        if (S_WWT_ALIPAY_TRANS.equals(transferTypeEnum) && BooleanUtils.isTrue(request.getIdentity())){
            if (member == null) {
                throw new BizCoreRuntimeException(WalletErrorDef.NOT_MEMBER);
            }
        }
    }

    /**
     * 转账打款 弹窗提示
     * <AUTHOR>
     */
    public TransferValidateVo queryIsTransfer(TransferValidateRequest request) {
        TransferValidateVo transferValidateVo = new TransferValidateVo();
        transferValidateVo.setIsTransfer(false);
        transferValidateVo.setIsSamePeople(false);
        BigDecimal allWithdrawAmount = new BigDecimal(5000);
        Ssqb query = Ssqb.create("com.lewei.eshop.app.wallet.queryIsTransfer")
                .setParam("realName", request.getRealName())
                .setParam("account", request.getAccount());
        if (request.getMemberId() != null && request.getMemberId() != 0) {
            query.setParam("memberId", request.getMemberId());
        }
        TransferValidateVo transferValidateVos = dao.findForObj(query, TransferValidateVo.class);
        if (transferValidateVos != null && transferValidateVos.getTransferCount() > 0) {
            // 当天内给同一人转账超过5K/3次，请确认是否继续转账?
            if (transferValidateVos.getTransferCount() >= 3 || allWithdrawAmount.compareTo((transferValidateVos.getAllWithdrawAmount()).add(request.getWithdrawAmount())) < 0) {
                transferValidateVo.setIsTransfer(true);
                transferValidateVo.setTransferCount(transferValidateVos.getTransferCount());
                transferValidateVo.setAllWithdrawAmount(transferValidateVos.getAllWithdrawAmount());
            }
            // 是否是同一人转账
            Ssqb queryPeople = Ssqb.create("com.lewei.eshop.app.wallet.queryPeople");
            TransferSamePeopleVo queryMember = dao.findForObj(queryPeople, TransferSamePeopleVo.class);
            if((queryMember.getMemberId()).equals(request.getMemberId()) || (queryMember.getAccount().equals(request.getAccount()))){
                transferValidateVo.setIsSamePeople(true);
            }
        }
        return transferValidateVo;
    }

    /**
     * 发送验证码
     */
    public void queryVerifyCode() {
        // 查询管理员信息
        String mobile = "";
        UserSimpleVO userSimpleVO = shopFeignClient.queryUserById();
        if (userSimpleVO != null){
            mobile = userSimpleVO.getUsername();
        }
        // 生成验证码
        VerifyCodeRequest verifyCodeRequest = new VerifyCodeRequest();
        verifyCodeRequest.setMobile(mobile);
        verifyCodeRequest.setTime(3);
        publicFeignClient.generateVerifyCode(verifyCodeRequest);
    }

    /**
     * 验证验证码
     * @param code 验证码
     */
    public void verifyCode(String code) {
        // 查询管理员信息
        String mobile = "";
        UserSimpleVO userSimpleVO = shopFeignClient.queryUserById();
        if (userSimpleVO != null){
            mobile = userSimpleVO.getUsername();
        }
        // 验证验证码
        IsVerifyCodeRequest isVerifyCodeRequest = new IsVerifyCodeRequest();
        isVerifyCodeRequest.setMobile(mobile);
        isVerifyCodeRequest.setCode(code);
        publicFeignClient.verifyCode(isVerifyCodeRequest);
    }

    /**
     * 删除验证码
     */
    public void deleteCode() {
        // 查询管理员信息
        String mobile = "";
        UserSimpleVO userSimpleVO = shopFeignClient.queryUserById();
        if (userSimpleVO != null){
            mobile = userSimpleVO.getUsername();
        }
        // 删除验证码
        VerifyCodeRequest verifyCodeRequest = new VerifyCodeRequest();
        verifyCodeRequest.setMobile(mobile);
        publicFeignClient.deleteCode(verifyCodeRequest);
    }

    private void saveRefundLog(String refundCode, String title, String content, String from) {
        OrderRefundLog log = new OrderRefundLog();
        log.setRefundCode(refundCode);
        log.setTitle(title);
        log.setContent(content);
        log.setFrom(from);
        log.setCreated(new Timestamp(System.currentTimeMillis()));

        dao.save(log);
    }
    private void handelOrderRefund(WalletWithdraw withdraw) {
        if (ApplicationTypeEnum.S_WWA_REFUND.value().equals(withdraw.getApplicationType())){
            String refundCode = withdraw.getPlId();
            if (StringUtils.isNotEmpty(refundCode)){
                OrderRefund orderRefund = dao.queryById(refundCode,OrderRefund.class);
                if (orderRefund != null) {
                    orderRefund.setMoneyState(OrderRefundMoneyStateEnum.S_ORM_REFUND_SUC.value());
                    orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_SUCCESS.value());
                    orderRefund.setUpdated(DateFormatUtils.getNow());
                    orderRefund.setRealChannel(withdraw.getChannel());
                    dao.update(orderRefund);
                    String title = BizMessageSource.getInstance().getMessage("cem10020");
                    if (S_WWT_WX.value().equals(withdraw.getTransferType())){
                        title = title + BizMessageSource.getInstance().getMessage("cem10021");
                    }else if (S_WWT_ALIPAY_TRANS.value().equals(withdraw.getTransferType())){
                        title = title +  BizMessageSource.getInstance().getMessage("cem10022");
                    }else {
                        title =  BizMessageSource.getInstance().getMessage("cem10023");
                    }
                    this.saveRefundLog(refundCode,title,null,"sys");

                    Order order = new Order();
                    order.setRefundState(OrderRefundState.S_ORS_REFUND_FINISH.value());
                    order.setId(orderRefund.getOrderId());
                    order.setUpdated(DateFormatUtils.getNow());
                    dao.update(order);

                }


                TradeFlowReq tradeFlow = new TradeFlowReq();
                tradeFlow.setTradeContent(BizMessageSource.getInstance().getMessage("cem10024"));
                tradeFlow.setOrderId(orderRefund != null ? orderRefund.getOrderId() : 0L);
                tradeFlow.setMemberId(withdraw.getMemberId());
                tradeFlow.setTradeAmount(withdraw.getWithdrawAmount());
                tradeFlow.setTradeType(TradeTypeEnum.S_TT_REFUND.value());
                tradeFlow.setTradeSource("pc");
                memberFeignClient.saveTradeFlow(tradeFlow);
            }
        }
    }

    private MemberOqRef queryOqRef(Long memberId){
        SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("memberId",memberId))
                .and(Restrictions.eq("dataStatus",1));
        MemberOqRef ref = dao.query(queryBuilder, MemberOqRef.class);
        return ref;
    }

    private void checkOqBinding(List<WalletWithdraw> walletWithdraws){
        List<WalletWithdraw> babyWithDraw = walletWithdraws.stream().filter(a->Objects.equals(a.getWithdrawPlatform(),WithDrawPlatformEnum.babywallet.value())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(babyWithDraw)){
            for(WalletWithdraw withdraw : babyWithDraw) {
                MemberOqRef ref =  this.queryOqRef(withdraw.getMemberId());
                if (ref==null || StringUtils.isBlank(ref.getOqId())){
                    throw new BizCoreRuntimeException(AppErrorDef.OQ_MEMBER_NO_EXIST);
                }
            }
        }
    }
}