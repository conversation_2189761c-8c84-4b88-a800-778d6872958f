package com.lewei.eshop.app.client.paas.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 入会条件配置
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
@Data
public class DistUserLevelConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    private Long chainId;
    private Long tenantId;
    /**
     * 入会条件
     */
    private String conditionType;
    private Long levelId;
    /**
     * 有效期、月份
     */
    private Integer effectiveTime;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新时间
     */
    private Date updated;
    /**
     * 更新人
     */
    private Long updateBy;
    private Boolean dataStatus;
}
