package com.lewei.eshop.app;

/**
* 错误定义
* <AUTHOR>
* @date 2020-05-08
**/

public class AppErrorDef {

    /**
     * {0}
     */
    public static final String CUS_ERROR = "91001";


    /**
     * 抽赏利润范围错误
     */
    public static final String SWEET_PROFIT_ERROR = "91002";

    /**
     * 奖品回收价范围错误
     */
    public static final String SWEET_RECOVERY_ERROR = "91003";

    /**
     * 请选择会员
     */
    public static final String SWEET_MEMBERS_IS_NULL = "91004";

    /**
     * 补偿对象与已存在【{0}】重复，请调整对象或利润范围
     */
    public static final String SWEET_MEMBERS_CONFLICT = "91005";

    /**
     * Sweet规则名称不可重复
     */
    public static final String SWEET_TITLE_IS_SAME = "91006";

    /**
     * 配置已经存在，请刷新重试
     */
    public static final String CONFIG_IS_EXIST = "91007";

    /**
     * 补偿对象与已存在【{0}】重复，请调整对象或抽赏金额范围
     */
    public static final String SWEET_MEMBERS_CONFLICT_TWO = "91008";

    /**
     * 每抽选择的盲盒不可重复
     */
    public static final String BOX_IS_NOT_REPEAT = "91009";

    /**
     * {0}
     */
    public static final String ROLL_PARAM_ERROR = "91010";
    /**
     * 请先配置ROLL设置
     */
    public static final String ROLL_CONFIG_IS_NULL = "91011";

    /**
     * 赏品状态异常
     */
    public static final String MEMBER_REWARD_STATUS_ERROR = "91012";

    /**
     * 只有待审核状态可以变更
     */
    public static final String ROLL_AUDIT_STATUS = "91013";
    /**
     * ROLL房状态错误
     */
    public static final String ROLL_STATUS_ERROR = "91014";

    /**
     * 进行中房间不可删除，如有问题可将房间关闭后，进行删除
     */
    public static final String ROLL_DELETE_STATUS_ERROR = "91015";

    /**
     * 只有未开始状态可以编辑
     */
    public static final String ROLL_UPDATE_STATUS_ERROR = "91016";

    /**
     * 只有官方ROLL房可编辑
     */
    public static final String ROLL_UPDATE_SOURCE_ERROR = "91017";
    /**
     * 昵称不能重复
     */
    public static final String MEMBER_NAME_IS_EXIST = "91018";



    /**
     * ROLL已过期不能开始或审核通过
     */
    public static final String ROLL_TIME_EXPIRED = "91019";
    /**
     * 已有所选商品，请勿重复添加
     */
    public static final String REWARD_DO_NOT_REPEAT = "91020";
    /**
     * 已参与状态下不可删除
     */
    public static final String CANNOT_BE_DELETED_IN_THE_PARTICIPATING_STATE = "91021";

    /**
     * 对战功能未开启
     */
    public static final String FIGHT_NOT_ENABLE = "91022";

    /**
     * 对战只支持无限盲盒
     */
    public static final String FIGHT_ONLY_SUPPORT_UN_LIMIT= "91023";

    /**
     * 放奖范围不能为空
     */
    public static final String SWEET_RULE_IS_NULL = "91024";
    /**
     * 放奖范围不能交叉
     */
    public static final String SWEET_RULE_CAN_NOT_CROSS = "91025";

    /**
     * 补偿机制和奖池机制不能同时开启
     */
    public static final String SWEET_RULE_CONFLICT = "91026";
    /**
     * 开启对战音效但未上传音效音频
     */
    public static final String FIGHT_SOUND_URL_NOT_EXIST = "91027";


    /**
     * 自动开启时间不能小于当前时间5分钟
     */
    public static final String ROLL_OPEN_TIME_COMPARE_NOW_ERROR = "91028";

    /**
     * 开启方式为自动开启时自动开启时间不能为空
     */
    public static final String ROLL_OPEN_TIME_IS_NOT_NULL = "91029";


    /**
     * 自动开启时间不能大于开奖时间
     */
    public static final String ROLL_OPEN_TIME_COMPARE_LOTTERY_ERROR = "91030";

    /**
     * 自动开启时间不能大于自动关闭时间
     */
    public static final String ROLL_OPEN_TIME_COMPARE_CLOSE_ERROR = "91031";

    /**
     * 打款失败，该会员编码已解除
     */
    public static final String OQ_MEMBER_NO_EXIST = "91032";
    /**
     * 助力配置不能为空
     */
    public static final String ASSIST_CONFIG_IS_NULL = "91033";
    /**
     * 被合成商品不能为空
     */
    public static final String PASSIVE_IS_NULL = "91034";
    /**
     * 被合成商品不能超过一个
     */
    public static final String PASSIVE_IS_MORE_THAN_ONE = "91035";
    /**
     * 被合成商品不能超过十个
     */
    public static final String PASSIVE_IS_MORE_THAN_TEN = "91036";
    /**
     * 合成商品不能为空
     */
    public static final String ACTIVE_IS_NULL = "91037";
    /**
     * 合成数量总和不能超过2000个
     */
    public static final String SYNTHESIZED_QUANTITY_CANNOT_EXCEED = "91038";
    /**
     * 合成商品数量不能为空
     */
    public static final String ACTIVE_NUM_IS_NULL = "91039";
    /**
     * 指定合成商品回收价和不能小于被合成商品售价
     */
    public static final String ACTIVE_TOTAL_LESS_PASSIVE = "91040";
    /**
     * 当前名称重复请重新输入名称
     */
    public static final String SYNTHESIZED_NAME_EXIST = "91041";
    /**
     * 每日限制次数与每小时限制次数积数必须为24
     */
    public static final String BROADCAST_CONFIG_NUM_ERROR = "91042";

    /**
     * 收入设置的百分之最大值不能超过1
     */
    public static final String PERCENTAGE_MAX_EXIST = "91043";
    /**
     * 收入设置的固定值最大值不能超过999
     */
    public static final String FIXED_MAX_EXIST = "91044";

    /**
     * 当前盲盒已经存在聚宝盆规则
     */
    public static final String BOXITEMID_CORNUCOPIA_IS_EXIST = "91045";

    /**
     * 当前聚宝盆规则不存在
     */
    public static final String CORNUCOPIA_IS_NOT_EXIST = "91046";
    /**
     * 当前聚宝盆已经开启无法删除请先暂停在删除
     */
    public static final String CORNUCOPIA_IS_OPEN = "91047";



    /**
     * 自动上架时间必须在当前时间30分钟之后
     */
    public static final String AUTOSHELVES_UPDATE_ERROR = "91048";

    /**
     * 只能修改自动上架状态为未上架的
     */
    public static final String AUTOSHELVES_STATUS_ERROR = "91049";
    /**
     * 未找到自动上架信息
     */
    public static final String AUTOSHELVES_IS_NOT_FOUND_ERROR = "91050";
    /**
     * 未找到自动上架配置信息
     */
    public static final String AUTOSHELVES_CONFIG_IS_NOT_FOUND_ERROR = "91051";
    /**
     * 上架分组中不能有重复盲盒
     */
    public static final String AUTOSHELVES_GROUP_REPEAT_ERROR = "91052";

    /**
     * 阶段奖励不能为空
     */
    public static final String STAGE_REWARDS_CANNOT_BE_EMPTY = "91053";
    /**
     * 适用范围不能为空
     */
    public static final String SUIT_TYPE_CANNOT_BE_EMPTY = "91054";
    /**
     * {0}阶段奖励范围不能小于{1}阶段奖励范围
     */
    public static final String STAGE_REWARD_SCOPE_ERROR = "91055";
    /**
     * 适用范围选择盲盒数量不能大于200
     */
    public static final String BOXES_APPLICABLE_RANGE_CANNOT_EXCEED = "91056";
    /**
     * 适用范围选择会员数量不能大于200
     */
    public static final String MEMBER_APPLICABLE_RANGE_CANNOT_EXCEED = "91057";
    /**
     * 当前流水返现配置不存在
     */
    public static final String FLOWBACK_CONFIGURATION_NOT_EXIST = "91058";
    /**
     * 未找到娃娃机信息
     */
    public static final String NOT_FIND_DOLL_ERROR = "91059";
    /**
     * 实际中奖概率不能为空
     */
    public static final String PROBABILITY_NOT_EXIST = "91060";
    /**
     * 最大开盒概率不能超过80%
     */
    public static final String MAX_BOX_OPEN_PROBABILITY_GREATER_THAN_EIGHTY = "91061";
    /**
     * 最小开盒概率不能小于0.01%
     */
    public static final String MIN_BOX_OPEN_PROBABILITY_GREATER_THAN = "91062";
    /**
     * 实际中奖概率大小降低不能大于50%
     */
    public static final String PROBABILITY_GREATER_THAN_FIFTY = "91063";
    /**
     * 适用对象与已存在【{0}】重复，请调整对象或开盒概率范围
     */
    public static final String SWEET_MEMBERS_CONFLICT_FIVE = "91064";
    /**
     * 累计充值金额不能小于0
     */
    public static final String RECHARGE_MONEY_IS_NONE = "91065";
    /**
     * 当前亏损补偿配置不存在
     */
    public static final String LOSS_COMPENSATE_CONFIG_NOT_EXIST = "91066";
    /**
     * 累计消费金额不能小于0
     */
    public static final String TRADE_MONEY_IS_NONE = "91067";

    /**
     * 赠送范围单价不能有交集
     */
    public static final String POST_CARD_BEGIN_END_MONEY_ERROR = "91068";

    /**
     * 单价开始价格不能大于结束价格
     */
    public static final String POST_CARD_BEGIN_MONEY_SIZE_ERROR = "91069";

    /**
     * 指定用户数不能为0
     */
    public static final String GIFT_MEMBERS_IS_NONE = "91067";

    /**
     * 下一阶段金额必须大于上一阶段金额
     */
    public static final String REMINDER_CONFIG_STAGE_IS_ERROR = "91071";

    /**
     * 限制消费额度必须大于最大的阶段金额
     */
    public static final String REMINDER_CONFIG_CONFIG_IS_ERROR = "91072";
    /**
     * 当前号段已经存在注册限制设置中
     */
    public static final String REGISTRATION_RESTRICTIONS_IS_EXIST = "91073";

    /**
     * 合成商品限制金额不能为空
     */
    public static final String SYNTHETIC_FEE_LIMIT_IS_NULL = "91074";

    /**
     * 标签已存在
     */
    public static final String LABEL_ALREADY_EXISTS = "91075";

    /**
     * 转盘格子最少添加2个，最多添加8个
     */
    public static final String TURNTABLE_CELL_LIMIT = "91076";

    /**
     * 中奖概率总和需等于100%
     */
    public static final String TURNTABLE_WINPERCENT_LIMIT = "91077";

    /**
     * 请输入验证码
     */
    public static final String PLEASE_ENTER_THE_VERIFICATION_CODE = "91078";

    /**
     * 开启自定义数量时，售价与最低购买数不能为空
     */
    public static final String COLLISION_IS_CUSTOM_ERROR = "91079";

    /**
     * {0}的范围在{1}-{2}之间
     */
    public static final String COLLISION_PARAM_LIMIT = "91080";

    /**
     * 闯关对对碰，关卡信息不能为空
     */
    public static final String COLLISION_STAGE_LIMIT = "91081";

    /**
     * 规则类型不能为空
     */
    public static final String COLLISION_RULE_TYPE_ERROR = "91082";

    /**
     * 续包内容不能为空
     */
    public static final String COLLISION_CONTINUE_ERROR = "91083";

    /**
     * 奖品数量必须为10个
     */
    public static final String COLLISION_PRIZE_LIMIT = "91084";

    /**
     * 奖品不可重复
     */
    public static final String COLLISION_DIS_LIMIT = "91085";

    /**
     * 此对对碰已不存在
     */
    public static final String COLLISION_NOT_EXIST = "91086";

    /**
     * 此对对碰未下架，不可编辑
     */
    public static final String COLLISION_NOT_UPD = "91087";

    /**
     * 最后一关，通关次数必须为空
     */
    public static final String COLLISION_LAST_IS_NULL = "91088";

    /**
     * 对碰次数奖励不能为空
     */
    public static final String COLLISION_GIFT_IS_NULL = "91089";
/**
     * 对碰奖励次数不能相同
     */
    public static final String COLLISION_GIFT_TIMES_DUPLICATE = "91090";
    /**
     * 只能审核待审核状态的记录
     */
    public static final String GIFT_APPROVE_STATUS_IS_ERROR = "91091";

    /**
     * 活动已经结束,未找到活动信息
     */
    public static final String GIFT_APPROVE_ACTIVITY_STATUS_IS_ERROR = "91092";

    /**
     * 奖品最多可以添加50个
     */
    public static final String DIFFERENT_SPACE_REWARD_SIZE_LIMIT = "91093";

    /**
     * 此异空间已不存在
     */
    public static final String DIFFERENT_SPACE_NOT_EXIST = "91094";

    /**
     * 此异空间未下架，不可编辑
     */
    public static final String DIFFERENT_SPACE_NOT_UPD = "91095";

    /**
     * 同一个盲盒只能开启一个异空间
     */
    public static final String DIFFERENT_SPACE_UP_ERROR = "91096";

    /**
     * 概率总和需等于100%
     */
    public static final String DIFFERENT_SPACE_ODDS_LIMIT = "91097";
}