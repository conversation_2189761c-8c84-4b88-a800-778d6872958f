package com.lewei.eshop.app.space.impl;

import com.lewei.eshop.app.AppErrorDef;
import com.lewei.eshop.app.client.MaPublicFeignClient;
import com.lewei.eshop.app.space.IDifferentSpaceService;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.space.DeleteDifferentSpaceRequest;
import com.lewei.eshop.common.request.space.DifferentSpaceRequest;
import com.lewei.eshop.common.request.space.DifferentSpaceStatusRequest;
import com.lewei.eshop.common.vo.space.DifferentSpaceDetailVo;
import com.lewei.eshop.common.vo.space.DifferentSpaceRewardRecordDetailVo;
import com.lewei.eshop.common.vo.space.DifferentSpaceRewardRecordTopVo;
import com.lewei.eshop.entity.space.DifferentSpace;
import com.lewei.eshop.entity.space.DifferentSpaceReward;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.lewei.eshop.entity.space.type.DifferentSpaceStatusEnum.S_DSS_DOWN;
import static com.lewei.eshop.entity.space.type.DifferentSpaceStatusEnum.S_DSS_UP;

/**
 * <p>
 * 对对碰 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Transactional
public class DifferentSpaceServiceImpl implements IDifferentSpaceService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private MaPublicFeignClient maPublicFeignClient;


    /**
     * 保存异空间
     * @param userId
     * @param request
     * @return
     */
    @Override
    public Long saveDifferentSpace(Long userId, DifferentSpaceRequest request, Long chainId) {
        this.vaildData(request);
        Date now = DateFormatUtils.getNow();

        // 保存异空间
        DifferentSpace space = new DifferentSpace();
        space.setTitle(request.getTitle());
        space.setBoxSpuId(request.getBoxSpuId());
        space.setProgressNum(request.getProgressNum());
        space.setClearCategoryId(request.getClearCategoryId());
        space.setStatus(S_DSS_DOWN.value());
        space.setCreated(now);
        space.setCreateBy(userId);
        dao.save(space);

        // 保存异空间奖品
        saveDifferentSpaceReward(request, space.getId(), userId);

        return space.getId();
    }

    @Override
    public void updateDifferentSpace(Long userId, DifferentSpaceRequest request, Long differentSpaceId,Long chainId) {
        this.vaildData(request);
        this.vaildUpdData(differentSpaceId);
        Date now = DateFormatUtils.getNow();

        // 编辑主表
        DifferentSpace space = new DifferentSpace();
        space.setId(differentSpaceId);
        space.setTitle(request.getTitle());
        space.setClearCategoryId(request.getClearCategoryId());
        space.setUpdated(now);
        space.setUpdateBy(userId);
        dao.update(space);

        // 删除异空间奖品原有奖品
        SaasQueryBuilder delete = SaasQueryBuilder.where(Restrictions.eq("differentSpaceId",differentSpaceId));
        dao.deleteByQuery(delete, DifferentSpaceReward.class);
        // 保存异空间奖品
        saveDifferentSpaceReward(request, differentSpaceId, userId);

    }

    /**
     * 保存异空间奖品
     * @param request
     * @param differentSpaceId
     * @param userId
     */
    public void saveDifferentSpaceReward(DifferentSpaceRequest request, Long differentSpaceId, Long userId){
        // 保存奖品
        List<DifferentSpaceReward> rewards = new ArrayList<>();
        request.getRewards().stream().forEach(a -> {
            DifferentSpaceReward reward = new DifferentSpaceReward();
            reward.setDifferentSpaceId(differentSpaceId);
            reward.setSpuId(a.getSpuId());
            reward.setSkuId(a.getSkuId());
            reward.setCategoryId(a.getCategoryId());
            reward.setOdds(a.getOdds());
            reward.setPriority(a.getPriority());
            reward.setCreated(DateFormatUtils.getNow());
            reward.setCreateBy(userId);
            rewards.add(reward);
        });
        dao.batchSave(rewards, DifferentSpaceReward.class);
    }

    /**
     * 验证参数
     * @param request
     */
    private void vaildData(DifferentSpaceRequest request){
        // 奖品校验
        if(request.getRewards().size() > 50){
            throw new BizCoreRuntimeException(AppErrorDef.DIFFERENT_SPACE_REWARD_SIZE_LIMIT);
        }
        if(request.getRewards().stream().mapToDouble(DifferentSpaceRequest.Reward::getOdds).sum() != 1D){
            throw new BizCoreRuntimeException(AppErrorDef.DIFFERENT_SPACE_ODDS_LIMIT);
        }
    }

    /**
     * 验证参数
     * @param differentSpaceId
     */
    private void vaildUpdData(Long differentSpaceId){
        DifferentSpace differentSpace = dao.queryById(differentSpaceId, DifferentSpace.class);
        if(differentSpace == null){
            throw new BizCoreRuntimeException(AppErrorDef.DIFFERENT_SPACE_NOT_EXIST);
        }else {
            if(!Objects.equals(S_DSS_DOWN.value(), differentSpace.getStatus())){
                throw new BizCoreRuntimeException(AppErrorDef.DIFFERENT_SPACE_NOT_UPD);
            }
        }
    }

    /**
     * 查询异空间列表
     * @param queryKey
     * @param status
     * @param request
     * @return
     */
    @Override
    public Pagination queryDifferentSpacePage(String queryKey, String status, PageRequest request){
        Ssqb query = Ssqb.create("com.lewei.eshop.app.different.space.queryDifferentSpacePage")
                .setParam("queryKey", queryKey)
                .setParam("status", status)
                .setParam("pageNo", request.getPageNo())
                .setParam("pageSize", request.getPageSize())
                ;

        return dao.findForPage(query);
    }

    /**
     * 查询异空间详情
     * @param differentSpaceId
     * @return
     */
    @Override
    public DifferentSpaceDetailVo queryDifferentSpaceDetail(Long differentSpaceId) {
        Ssqb query = Ssqb.create("com.lewei.eshop.app.different.space.queryDifferentSpaceDetail")
                .setParam("id",differentSpaceId);
        return dao.findForObj(query, DifferentSpaceDetailVo.class);
    }

    /**
     * 批量删除
     * @param request
     */
    @Override
    public void bathDeleteDifferentSpace(DeleteDifferentSpaceRequest request, Long chainId){
        List<DifferentSpace> DifferentSpaces = dao.queryByIds(request.getDifferentSpaceIds(), DifferentSpace.class);
        List<Long> ids = DifferentSpaces.stream().filter(a -> Objects.equals(a.getStatus(), S_DSS_DOWN.value())).map(DifferentSpace::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(ids)){
            // 删除
            SaasQueryBuilder collisionDelete = SaasQueryBuilder.where(Restrictions.in("id",ids));
            dao.deleteByQuery(collisionDelete, DifferentSpace.class);
            SaasQueryBuilder delete = SaasQueryBuilder.where(Restrictions.in("differentSpaceId",ids));
            dao.deleteByQuery(delete, DifferentSpaceReward.class);
            for (Long id : ids) {
                maPublicFeignClient.clearKey(MaCacheKeys.DIFFERENT_SPACE_KEY + id);
            }

        }
    }

    /**
     * 批量修改状态
     * @param request
     * @param userId
     */
    @Override
    public void updStatus(DifferentSpaceStatusRequest request, Long userId){
        if(!CollectionUtils.isEmpty(request.getDifferentSpaceIds())){

            // 启用逻辑校验：同一个盲盒不可同时启用
            if(Objects.equals(S_DSS_UP.value(),request.getStatus())) {
                Set<Long> boxSpuIds = new HashSet<>();
                SaasQueryBuilder querySpaces = SaasQueryBuilder.where(Restrictions.in("id", request.getDifferentSpaceIds()))
                        .and(Restrictions.eq("dataStatus", 1));
                List<DifferentSpace> spaces = dao.queryList(querySpaces, DifferentSpace.class);
                spaces.forEach(space -> {
                    if (!boxSpuIds.add(space.getBoxSpuId())) {
                        throw new BizCoreRuntimeException(AppErrorDef.DIFFERENT_SPACE_UP_ERROR);
                    }
                });

                Ssqb querySpaceUp = Ssqb.create("com.lewei.eshop.app.different.space.queryDifferentSpaceUp")
                        .setParam("boxSpuIds", boxSpuIds)
                        .setParam("ids", request.getDifferentSpaceIds())
                        .setParam("status", S_DSS_UP.value());
                List<DifferentSpace> differentSpaces = dao.findForList(querySpaceUp, DifferentSpace.class);
                if (!CollectionUtils.isEmpty(differentSpaces)) {
                    throw new BizCoreRuntimeException(AppErrorDef.DIFFERENT_SPACE_UP_ERROR);
                }
            }

            // 批量更新状态
            Date now = DateFormatUtils.getNow();
            List<DifferentSpace> updSpaces = new ArrayList<>();
            request.getDifferentSpaceIds().stream().forEach(id ->{
                DifferentSpace updSpace = new DifferentSpace();
                updSpace.setId(id);
                updSpace.setStatus(request.getStatus());
                updSpace.setUpdated(now);
                updSpace.setUpdateBy(userId);
                updSpaces.add(updSpace);
            });
            dao.batchUpdate(updSpaces, DifferentSpace.class);
        }
    }

    /**
     * 异空间入场劵记录
     * @param queryKey
     * @param request
     * @return
     */
    @Override
    public Pagination queryDifferentSpaceRecord(Long differentSpaceId,String queryKey,Boolean isRecover,PageRequest request){
        Ssqb query = Ssqb.create("com.lewei.eshop.app.different.space.queryDifferentSpaceRecord")
                .setParam("differentSpaceId", differentSpaceId)
                .setParam("queryKey", queryKey)
                .setParam("isRecover", isRecover)
                .setParam("pageNo", request.getPageNo())
                .setParam("pageSize", request.getPageSize())
                ;

        return dao.findForPage(query);
    }

    /**
     * 异空间入场劵记录详情
     * @return
     */
    @Override
    public Pagination queryDifferentSpaceRecordDetail(Long differentSpaceId, Long memberId, PageRequest request){
        Ssqb query = Ssqb.create("com.lewei.eshop.app.different.space.queryDifferentSpaceRecordDetail")
                .setParam("differentSpaceId", differentSpaceId)
                .setParam("memberId", memberId)
                .setParam("pageNo", request.getPageNo())
                .setParam("pageSize", request.getPageSize())
                ;

        return dao.findForPage(query);
    }

    @Override
    public DifferentSpaceRewardRecordTopVo queryDifferentSpaceRecordQuantity(Long differentSpaceId,String queryKey,Boolean isRecover){
        Ssqb query = Ssqb.create("com.lewei.eshop.app.different.space.queryDifferentSpaceRecordQuantity")
                .setParam("differentSpaceId", differentSpaceId)
                .setParam("queryKey", queryKey)
                .setParam("isRecover", isRecover)
                ;

        return dao.findForObj(query, DifferentSpaceRewardRecordTopVo.class);

    }
}
