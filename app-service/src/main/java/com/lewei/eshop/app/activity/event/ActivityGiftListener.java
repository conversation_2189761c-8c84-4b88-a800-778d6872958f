package com.lewei.eshop.app.activity.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.app.ActivityErrorDef;
import com.lewei.eshop.app.client.MemberFeignClient;
import com.lewei.eshop.app.client.PublicFeignClient;
import com.lewei.eshop.app.coupon.ICouponActivityService;
import com.lewei.eshop.common.data.coupon.CouponPreferentialTypeEnum;
import com.lewei.eshop.common.data.coupon.CouponReceiveWayEnum;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.member.MemberBalanceTradeTypeEnum;
import com.lewei.eshop.common.data.member.PointTaskEnum;
import com.lewei.eshop.common.data.member.ScoreTaskEnum;
import com.lewei.eshop.common.request.activity.ActivityRecordRequest;
import com.lewei.eshop.common.request.coupon.CouponActivityRequest;
import com.lewei.eshop.common.request.member.BatchAddPointRequest;
import com.lewei.eshop.common.request.member.BatchMemberBalanceRequest;
import com.lewei.eshop.common.request.member.MemberRecordReq;
import com.lewei.eshop.common.request.taoBao.MemberTbTicketRequest;
import com.lewei.eshop.common.vo.activity.IssueProductInfoVo;
import com.lewei.eshop.common.vo.activity.TbTicketVo;
import com.lewei.eshop.common.vo.member.BatchAddFractionRequest;
import com.lewei.eshop.common.vo.member.BatchAddScoreRequest;
import com.lewei.eshop.common.vo.product.box.BoxRewardProductVo;
import com.lewei.eshop.entity.activity.ActivityRecord;
import com.lewei.eshop.entity.activity.types.ActivityTypeEnum;
import com.lewei.eshop.entity.activity.types.GiftTypeEnum;
import com.lewei.eshop.entity.app.coupon.Coupon;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.member.MemberReward;
import com.lewei.eshop.entity.member.RechargeTbConfig;
import com.lewei.eshop.entity.member.types.MemberRewardStatusEnum;
import com.lewei.eshop.entity.member.types.MemberTbTicketTypeEnum;
import com.lewei.eshop.entity.member.types.RewardReceiveWayEnum;
import com.lewei.eshop.entity.product.box.ProductBox;
import com.lewei.eshop.entity.product.box.ProductBoxItem;

import com.lewei.eshop.message.mina.entity.MaMessageEntity;
import com.lewei.eshop.message.mina.entity.MaMessageTypeEnum;
import com.lewei.eshop.message.mina.service.IMaMessageService;
import com.lewei.log.trace.MDCTraceUtils;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/12/14
 */
@Component
@Transactional
@Slf4j
public class ActivityGiftListener {

    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IMaMessageService messageService;
    @Autowired
    private ICouponActivityService couponActivityService;
    @Autowired
    private PublicFeignClient publicFeignClient;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;




    @EventListener(condition = "event.giftType eq 'balance'")
    public void handlerBalance(ActivityGiftEvent event){
        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerBalance not fount member! acttivityId={}", activityId);
            return;
        }


        JSONObject jsonObject = JSON.parseObject(event.getGiftJson());
        BigDecimal amount = jsonObject.getBigDecimal("amount");
        if (amount != null && event.getTimes() !=null) {
            amount = amount.multiply(BigDecimal.valueOf(event.getTimes()));
        }



        BatchMemberBalanceRequest balanceRequest = new BatchMemberBalanceRequest();
        balanceRequest.setBalance(amount);
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setContent(event.getTitle());
        balanceRequest.setPlId(activityId);
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_ISSUE.value());
        balanceRequest.setMemberIds(memberIds);
        balanceRequest.setCreateBy(event.getUserId());
        memberFeignClient.batchMemberBalanceHandle(balanceRequest);

        sendMessage(event, memberIds);

        //保存记录
        this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),event.getGiftType(),null,null,amount,null,null,null,null,null,jsonObject.toJSONString(),event.getUserId()),memberIds);



    }

    @EventListener(condition = "event.giftType eq 'free'")
    public void handlerFree(ActivityGiftEvent event){
        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerFree not fount member! acttivityId={}", activityId);
            return;
        }


        JSONObject jsonObject = JSON.parseObject(event.getGiftJson());
        Integer amount = jsonObject.getInteger("amount");
        if (amount != null && event.getTimes() !=null) {
            amount = amount * event.getTimes();
        }
        BigDecimal price = jsonObject.getBigDecimal("price");
        price = price.multiply(BigDecimal.valueOf(amount));



        BatchMemberBalanceRequest balanceRequest = new BatchMemberBalanceRequest();
        balanceRequest.setBalance(price);
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setContent(event.getTitle());
        balanceRequest.setPlId(activityId);
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        balanceRequest.setTradeType(MemberBalanceTradeTypeEnum.S_MBTT_ISSUE.value());
        balanceRequest.setMemberIds(memberIds);
        balanceRequest.setCreateBy(event.getUserId());
        memberFeignClient.batchMemberBalanceHandle(balanceRequest);

        sendMessage(event, memberIds);

        //保存记录
        this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),event.getGiftType(),null,null,null,price,null,amount,null,null,jsonObject.toJSONString(),event.getUserId()),memberIds);



    }



    @EventListener(condition = "event.giftType eq 'score'")
    public void handlerScore(ActivityGiftEvent event){
        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerScore not fount member! activityId={}", activityId);
            return;
        }


        JSONObject jsonObject = JSON.parseObject(event.getGiftJson());
        BigDecimal amount = jsonObject.getBigDecimal("amount");
        if (amount != null && event.getTimes() !=null) {
            amount = amount.multiply(BigDecimal.valueOf(event.getTimes()));
        }
        BatchAddScoreRequest scoreRequest = new BatchAddScoreRequest();
        scoreRequest.setMemberIds(memberIds);
        scoreRequest.setUserId(event.getUserId());
        scoreRequest.setTitle(event.getTitle());
        scoreRequest.setSource("PC");
        scoreRequest.setScore(amount);
        scoreRequest.setTask("");
        memberFeignClient.batchAddMemberScore(scoreRequest);


        sendMessage(event, memberIds);

        //保存记录
        this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),event.getGiftType(),amount,null,null,null,null,null,null,null,jsonObject.toJSONString(),event.getUserId()),memberIds);

    }


    @EventListener(condition = "event.giftType eq 'fraction'")
    public void handlerFraction(ActivityGiftEvent event){

        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerFraction not fount member! activityId={}", activityId);
            return;
        }


        JSONObject jsonObject = JSON.parseObject(event.getGiftJson());
        BigDecimal amount = jsonObject.getBigDecimal("amount");
        if (amount != null && event.getTimes() !=null) {
            amount = amount.multiply(BigDecimal.valueOf(event.getTimes()));
        }

        BatchAddFractionRequest fractionRequest = new BatchAddFractionRequest();
        fractionRequest.setMemberIds(memberIds);
        fractionRequest.setUserId(event.getUserId());
        fractionRequest.setTitle(event.getTitle());
        fractionRequest.setSource("PC");
        fractionRequest.setFraction(amount);
        fractionRequest.setTask(getScoreTask(event.getActivityType()));
        memberFeignClient.batchAddMemberFraction(fractionRequest);

        sendMessage(event, memberIds);

        //保存记录
        this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),event.getGiftType(),null,amount,null,null,null,null,null,null,jsonObject.toJSONString(),event.getUserId()),memberIds);


    }

    @EventListener(condition = "event.giftType eq 'point'")
    public void handlerPoint(ActivityGiftEvent event){

        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerPoint not fount member! activityId={}", activityId);
            return;
        }


        JSONObject jsonObject = JSON.parseObject(event.getGiftJson());
        BigDecimal amount = jsonObject.getBigDecimal("amount");
        if (amount != null && event.getTimes() !=null) {
            amount = amount.multiply(BigDecimal.valueOf(event.getTimes()));
        }

        BatchAddPointRequest request = new BatchAddPointRequest();
        request.setMemberIds(memberIds);
        request.setUserId(event.getUserId());
        request.setTitle(event.getTitle());
        request.setSource("PC");
        request.setPoint(amount);
        if (StringUtils.isNotEmpty(event.getTask()) && PointTaskEnum.fromCode(event.getTask()) != null){
            request.setTask(PointTaskEnum.fromCode(event.getTask()).task());
        }else {
            request.setTask("");
        }

        memberFeignClient.batchAddMemberPoint(request);

        sendMessage(event, memberIds);

        //保存记录
        this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),event.getGiftType(),null,null,null,null,amount,null,null,null,jsonObject.toJSONString(),event.getUserId()),memberIds);


    }

    @EventListener(condition = "event.giftType eq 'coupon'")
    public void handlerCoupon(ActivityGiftEvent event){

        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerCoupon not fount member! activityId={}", activityId);
            return;
        }

        JSONObject object = JSON.parseObject(event.getGiftJson());

        JSONArray jsonArray = object.getJSONArray("couponJson");

        List<CouponActivityRequest.CouponAttr> couponAttrs = new ArrayList<>();
        CouponActivityRequest.CouponAttr couponAttr;

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Long couponId = jsonObject.getLong("couponId");
            Integer num = jsonObject.getInteger("num");
            if (num != null && event.getTimes() !=null) {
                num = num * event.getTimes();
            }
            couponAttr = new CouponActivityRequest.CouponAttr();
            couponAttr.setCouponId(couponId);
            couponAttr.setNum(num);
            couponAttrs.add(couponAttr);

            Coupon coupon = dao.queryById(couponId,Coupon.class);
            BigDecimal primeCostFee = BigDecimal.ZERO;
            if (coupon != null && (CouponPreferentialTypeEnum.S_CPT_LJ.value().equals(coupon.getPreferentialType()) ||CouponPreferentialTypeEnum.S_CPT_MJ.value().equals(coupon.getPreferentialType()) )) {
                primeCostFee = coupon.getBenefit();
            }
            //保存记录
            this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),event.getGiftType(),num,primeCostFee,jsonObject.toJSONString(),event.getUserId()),memberIds);


        }
        couponActivityService.batchIssueCoupon(event.getMemberIds(), couponAttrs, activityId, event.getUserId(), false, getCouponReceiveWay(event.getActivityType()));

        sendMessage(event, memberIds);





    }

    @EventListener(condition = "event.giftType eq 'product'")
    public void handlerProduct(ActivityGiftEvent event) {
        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerProduct not fount member! activityId={}", activityId);
            return;
        }


        JSONObject object = JSON.parseObject(event.getGiftJson());

        JSONArray jsonArray = object.getJSONArray("productJson");

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String name = jsonObject.getString("name");
            String mainImage = jsonObject.getString("mainImage");
            Long spuId = jsonObject.getLong("spuId");
            Long skuId = jsonObject.getLong("skuId");
            Integer amount = jsonObject.getInteger("amount");
            if (amount != null && event.getTimes() !=null) {
                amount = amount * event.getTimes();
            }
            BigDecimal price = jsonObject.getBigDecimal("price");
            BigDecimal primeCostFee = jsonObject.getBigDecimal("primeCostFee");

            if (skuId == null){
                Ssqb querySkuId = Ssqb.create("com.lewei.eshop.app.issue.activity.querySkuId")
                        .setParam("spuId", spuId);
                skuId = dao.findForObj(querySkuId, Long.class);
            }

//            SaasQueryBuilder queryBaseReward = SaasQueryBuilder
//                    .where(Restrictions.eq("spuItemId", spuId))
//                    .and(Restrictions.eq("skuItemId", skuId))
//                    .and(Restrictions.eq("isBase", 1));
//            ProductBoxItemReward reward = dao.query(queryBaseReward, ProductBoxItemReward.class);

            QueryBuilder queryIssueProductInfo = QueryBuilder.create("com.lewei.eshop.app.issue.activity.queryIssueProductInfo")
                    .setParam("spuId", spuId)
                    .setParam("skuId", skuId);
            IssueProductInfoVo productInfoVo = dao.findForObj(queryIssueProductInfo, IssueProductInfoVo.class);
            //查询产品库存

            if (productInfoVo == null) {
                throw new NotFoundException(BizMessageSource.getInstance().getI18nMessage("cem10040",name));
            }
//            if (reward == null) {
//                synchronized (this) {
//                    reward = dao.query(queryBaseReward, ProductBoxItemReward.class);
//                    if (reward == null) {
//                        reward = new ProductBoxItemReward();
//                        reward.setBoxItemId(0L);
//                        reward.setSpuItemId(spuId);
//                        reward.setSkuItemId(skuId);
//                        reward.setIsBase(true);
//                        reward.setScore(BigDecimal.ZERO);
//                        reward.setIsGift(false);
//                        reward.setPriority(0);
//                        reward.setCategoryId(0L);
//                        reward.setQuantity(0);
//                        reward.setLeftQuantity(0);
//                        reward.setPrimeCostFee(primeCostFee);
//                        dao.save(reward);
//                    }
//                }
//            }

            List<MemberReward> memberRewards = new ArrayList<>();
            MemberReward memberReward;

            int stockNum = 0;
            for (Long memberId : memberIds) {
                for (int j = 0; j < amount; j++) {
                    memberReward = new MemberReward();
                    memberReward.setIsGift(false);
                    memberReward.setMainImage(mainImage);
                    memberReward.setName(name);
                    memberReward.setOrderId(0L);
                    memberReward.setMemberId(memberId);
                    memberReward.setBoxItemId(0L);
                    memberReward.setCreated(DateFormatUtils.getNow());
                    memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                    memberReward.setReceiveWay(getRewardReceiveWay(event.getActivityType()));
                    memberReward.setRewardId(0L);
                    memberReward.setRewardType(productInfoVo.getRewardType());
                    memberReward.setSupplySpuId(productInfoVo.getRealSupplySpuId());
                    memberReward.setSupplySkuId(productInfoVo.getRealSupplySkuId());
                    memberReward.setSource("PC");
                    memberReward.setPrimeCostFee(primeCostFee);
                    memberReward.setIsCard(productInfoVo.getIsCard());
                    memberReward.setSpuId(productInfoVo.getSpuId());
                    memberRewards.add(memberReward);
                    stockNum++;
                }
            }
            //更新预占用库存
            QueryBuilder updateIssueProductStock = QueryBuilder.create("com.lewei.eshop.app.issue.activity.updateIssueProductStock")
                    .setParam("spuId", productInfoVo.getSupplySpuId())
                    .setParam("isControlStock", productInfoVo.getIsControlStock())
                    .setParam("num", stockNum)
                    .setParam("quantity", productInfoVo.getQuantity());
            int l = dao.updateByMybatis(updateIssueProductStock);
            if (l < 1) {
                throw new BizCoreRuntimeException(ActivityErrorDef.PRODUCT_STOCK_NOT_ENOUGH, name);
            }
            if (productInfoVo.getIsSupplyProduct()) {
                QueryBuilder updateAgentIssueProdutStock = QueryBuilder.create("com.lewei.eshop.app.issue.activity.updateIssueProductStock")
                        .setParam("spuId", productInfoVo.getSpuId())
                        .setParam("num", stockNum);
                dao.updateByMybatis(updateAgentIssueProdutStock);

            }
            dao.batchSave(memberRewards, MemberReward.class);

            //保存记录
            this.saveActivityRecord(new ActivityRecordRequest(activityId,event.getActivityType(),"product",null,null,null,null,null,amount,price,primeCostFee,jsonObject.toJSONString(),event.getUserId()),memberIds);


        }

        sendMessage(event, memberIds);



    }


    @EventListener(condition = "event.giftType eq 'treasure'")
    public void handlerTreasure(ActivityGiftEvent event) {
        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerTreasure not fount member! activityId={}", activityId);
            return;
        }


        JSONObject object = JSON.parseObject(event.getGiftJson());

        JSONArray jsonArray = object.getJSONArray("treasureJson");

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String name = jsonObject.getString("name");
            String mainImage = jsonObject.getString("mainImage");
            Long spuId = jsonObject.getLong("spuId");
            Long skuId = jsonObject.getLong("skuId");
            Integer amount = jsonObject.getInteger("amount");
            if (amount != null && event.getTimes() !=null) {
                amount = amount * event.getTimes();
            }
            BigDecimal price = jsonObject.getBigDecimal("price");
            BigDecimal primeCostFee = jsonObject.getBigDecimal("primeCostFee");

            if (skuId == null){
                Ssqb querySkuId = Ssqb.create("com.lewei.eshop.app.issue.activity.querySkuId")
                        .setParam("spuId", spuId);
                skuId = dao.findForObj(querySkuId, Long.class);
            }



            QueryBuilder queryIssueProductInfo = QueryBuilder.create("com.lewei.eshop.app.issue.activity.queryIssueProductInfo")
                    .setParam("spuId", spuId)
                    .setParam("skuId", skuId);
            IssueProductInfoVo productInfoVo = dao.findForObj(queryIssueProductInfo, IssueProductInfoVo.class);
            //查询产品库存

            if (productInfoVo == null) {
                throw new NotFoundException(BizMessageSource.getInstance().getI18nMessage("cem10040",name));
            }

            List<MemberReward> memberRewards = new ArrayList<>();
            MemberReward memberReward;

            for (Long memberId : memberIds) {
                for (int j = 0; j < amount; j++) {
                    memberReward = new MemberReward();
                    memberReward.setIsGift(false);
                    memberReward.setMainImage(mainImage);
                    memberReward.setName(name);
                    memberReward.setOrderId(0L);
                    memberReward.setMemberId(memberId);
                    memberReward.setBoxItemId(0L);
                    memberReward.setCreated(DateFormatUtils.getNow());
                    memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNBOXED.value());
                    memberReward.setReceiveWay(getRewardReceiveWay(event.getActivityType()));
                    memberReward.setRewardId(0L);
                    memberReward.setRewardType(productInfoVo.getRewardType());
                    memberReward.setSupplySpuId(productInfoVo.getRealSupplySpuId());
                    memberReward.setSupplySkuId(productInfoVo.getRealSupplySkuId());
                    memberReward.setSource("PC");
                    memberReward.setPrimeCostFee(primeCostFee);
                    memberReward.setIsCard(productInfoVo.getIsCard());
                    memberReward.setSpuId(productInfoVo.getSpuId());
                    memberRewards.add(memberReward);
                }
            }

            dao.batchSave(memberRewards, MemberReward.class);

            ActivityRecordRequest recordRequest = new ActivityRecordRequest();
            recordRequest.setActivityId(activityId);
            recordRequest.setActivityType(event.getActivityType());
            recordRequest.setGiftType("treasure");
            recordRequest.setQuantity(amount);
            recordRequest.setTreasurePrimeCostFee(primeCostFee);
            recordRequest.setExtJson(jsonObject.toJSONString());
            recordRequest.setUserId(event.getUserId());

            //保存记录
            this.saveActivityRecord(recordRequest,memberIds);


        }

        sendMessage(event, memberIds);



    }



    @EventListener(condition = "event.giftType eq 'box_reward'")
    public void handlerBoxReward(ActivityGiftEvent event){

        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerCoupon not fount member! activityId={}", activityId);
            return;
        }

        JSONObject object = JSON.parseObject(event.getGiftJson());
        Integer amount = object.getInteger("amount");
        Long categoryId = object.getLong("categoryId");
        Long boxItemId = object.getLong("boxItemId");
        //查询分类商品信息
        Ssqb query = Ssqb.create("com.lewei.eshop.app.activity.queryBoxRewradProduct")
                .setParam("categoryId",categoryId)
                .setParam("boxItemId",boxItemId);
        List<BoxRewardProductVo> productVos = dao.findForList(query, BoxRewardProductVo.class);
        if (ListUtil.isEmpty(productVos)){
            log.debug("赠送 box_reward 未找到奖品 boxItemId = {},categoryId = {}",boxItemId,categoryId);
            return;
        }
        Random random = new Random();
        Map<Long,Integer> mapping = new HashMap<>();
        for (int i = 0; i < amount; i++) {
            BoxRewardProductVo productVo = productVos.get(random.nextInt(productVos.size()));
            mapping.merge(productVo.getSkuId(), 1, Integer::sum);
        }

        for (BoxRewardProductVo productVo : productVos) {
            productVo.setAmount(mapping.get(productVo.getSkuId()));
        }

        productVos = productVos.stream().filter((a->a.getAmount() != null && a.getAmount() > 0)).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(productVos)){
            Map<String,Object> productMap = new HashMap<>();
            productMap.put("type","product");
            productMap.put("productJson",productVos);

            String giftJson = JSON.toJSONString(productMap);

            event.setGiftJson(giftJson);
            //这个类型置空 否则会重复调用
            event.setGiftType("");

            this.handlerProduct(event);
        }


    }

    private void sendMessage(ActivityGiftEvent event, List<Long> memberIds) {
        log.info("进入小程序弹窗消息" + event);
        if (BooleanUtils.isTrue(event.getIsSendMsg())){
            String messageData = "";
            MaMessageTypeEnum msgType ;

            if (!event.getActivityType().equals(ActivityTypeEnum.S_AT_ISSUE.value())){
                msgType = MaMessageTypeEnum.activity;
                if (event.getRuleJson() != null){
                    Map<String,Object> map = new HashMap<>();
                    //余额 免单转换成比例
                    if (GiftTypeEnum.balance.value().equals(event.getGiftType())  ){
                        ChainConfig chainConfig = publicFeignClient.queryChainConfig();
                        JSONObject gift = JSON.parseObject(event.getGiftJson());
                        gift.put("amount",gift.getBigDecimal("amount").multiply(chainConfig.getMoneyRatio()));
                        map.put("gift",gift);
                    }else if(GiftTypeEnum.free.value().equals(event.getGiftType())){
                        ChainConfig chainConfig = publicFeignClient.queryChainConfig();
                        JSONObject gift = JSON.parseObject(event.getGiftJson());
                        gift.put("price",gift.getBigDecimal("price").multiply(chainConfig.getMoneyRatio()));
                        map.put("gift",gift);
                    } else {
                        map.put("gift",JSON.parseObject(event.getGiftJson()));
                        if (StringUtils.isNotBlank(event.getActivityType()) &&
                                event.getActivityType().equals(ActivityTypeEnum.S_AT_TREASURE_ACTIVITY.value())){
                            map.put("times",event.getTimes());
                        }
                    }
                    map.put("rule",JSON.parseObject(event.getRuleJson()));
                    messageData = JSON.toJSONString(map);
                }else {
                    messageData = event.getGiftJson();
                }
            }else {
                msgType = MaMessageTypeEnum.issue;
                if (GiftTypeEnum.balance.value().equals(event.getGiftType())  ){
                    ChainConfig chainConfig = publicFeignClient.queryChainConfig();
                    JSONObject gift = JSON.parseObject(event.getGiftJson());
                    gift.put("amount",gift.getBigDecimal("amount").multiply(chainConfig.getMoneyRatio()));
                    messageData = gift.toJSONString();
                }else {
                    messageData = event.getGiftJson();
                }
            }
            log.info("小程序弹窗消息" + messageData);
            //小程序弹窗消息
            MaMessageEntity messageEntity = MaMessageEntity.type(msgType)
                    .messageData(messageData)
                    .subTypeStr(event.getMessageSubType())
                    .plId(event.getActivityId())
                    .title(event.getTitle());
            messageService.send(messageEntity, memberIds);
        }

    }

    private void saveActivityRecord(ActivityRecordRequest request, List<Long> memberIds){

        List<ActivityRecord> records = new ArrayList<>();
        ActivityRecord record ;

        for (Long memberId : memberIds) {
            record = new ActivityRecord();
            BeanUtils.copyProperties(request,record);
            record.setMemberId(memberId);
            record.setCreated(DateFormatUtils.getNow());
            record.setCreateBy(request.getUserId());
            records.add(record);
        }
        dao.batchSave(records,ActivityRecord.class);
        BigDecimal primeCost = BigDecimal.ZERO;
//        if (GiftTypeEnum.coupon.value().equals(request.getGiftType())){
//            try {
//                primeCost = request.getCouponFee().multiply(BigDecimal.valueOf(request.getQuantity()));
//            } catch (Exception e) {
//                log.error("保存营销成本错误 couponFee = {},num = {}",request.getCouponFee(),request.getQuantity());
//            }
//        }
        if (GiftTypeEnum.product.value().equals(request.getGiftType()) || GiftTypeEnum.box_reward.value().equals(request.getGiftType())){
            try {
                primeCost = request.getPrimeCostFee().multiply(BigDecimal.valueOf(request.getQuantity()));
            } catch (Exception e) {
                log.error("保存营销成本错误 PrimeCostFee = {},num = {}",request.getPrimeCostFee(),request.getQuantity());
            }
        }
        if (GiftTypeEnum.free.value().equals(request.getGiftType()) ){
            primeCost = request.getFreeFee();
        }
        if (GiftTypeEnum.balance.value().equals(request.getGiftType()) ){
            primeCost = request.getBalance();
        }

        if(primeCost.compareTo(BigDecimal.ZERO) > 0){
            this.handelMemberRecords(memberIds,primeCost,XcrmThreadContext.getChainId(),MDCTraceUtils.getTraceId());
        }

    }

    private String getRewardReceiveWay(String activityType){
        if (ActivityTypeEnum.S_AT_ISSUE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_BUSINESS.value();
        }
        if (ActivityTypeEnum.S_AT_REWARD_GIFT.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_REWARD.value();
        }
        if (ActivityTypeEnum.S_AT_MEMBER_LEVEL.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_MEMBER_LEVEL.value();
        }
        if (ActivityTypeEnum.S_AT_WELFARE_CODE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_WELFARE_CODE.value();
        }
        if (ActivityTypeEnum.S_AT_NEWCOMER_GIFT_BAG.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_NEWCOMER_GIFT_BAG.value();
        }
        if (ActivityTypeEnum.S_AT_REWARD_MINE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_REWARD_MINE.value();
        }
        if (ActivityTypeEnum.S_AT_SIGN.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_SIGN.value();
        }
        if (ActivityTypeEnum.S_AT_POINT_TASK.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_POINT_TASK.value();
        }
        if (ActivityTypeEnum.S_AT_FIGHT.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_FIGHT.value();
        }
        if (ActivityTypeEnum.S_AT_TREASURE_ACTIVITY.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_TREASURE_ACTIVITY.value();
        }
        if (ActivityTypeEnum.S_AT_POSTCARD.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_POSTCARD.value();
        }
        if (ActivityTypeEnum.S_AT_NEWCOMER_ACTIVITY.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_NEWCOMER_ACTIVITY.value();
        }
        if (ActivityTypeEnum.S_AT_LOSS_COMPENSATE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_LOSS_COMPENSATE.value();
        }

        if (ActivityTypeEnum.S_AT_SYNTHESIS.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_SYNTHESIS.value();
        }
        if (ActivityTypeEnum.S_AT_ROLL.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_ROLL.value();
        }
        if (ActivityTypeEnum.S_AT_BOX_BOOK.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_BOX_BOOK.value();
        }
        if (ActivityTypeEnum.S_AT_TURNTABLE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_TURNTABLE.value();
        }
        if(ActivityTypeEnum.S_AT_DOUBLE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_DOUBLE.value();
        }
        if(ActivityTypeEnum.S_AT_OQ_LEVEL.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_OQLEVEL.value();
        }
        if(ActivityTypeEnum.S_AT_COLLISION.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_COLLISION.value();
        }
        if(ActivityTypeEnum.S_AT_DIFFERENT_SPACE.value().equals(activityType)){
            return RewardReceiveWayEnum.S_MRRW_DIFFERENT_SPACE.value();
        }

        return "";
    }

    private CouponReceiveWayEnum getCouponReceiveWay(String activityType){
        if (ActivityTypeEnum.S_AT_MEMBER_LEVEL.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_MEMBER_LEVEL;
        }else if(ActivityTypeEnum.S_AT_WELFARE_CODE.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_WELFARE_CODE;
        }else if(ActivityTypeEnum.S_AT_NEWCOMER_GIFT_BAG.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_NEWCOMER_GIFT_BAG;
        }else if(ActivityTypeEnum.S_AT_SIGN.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_MEMBER_SIGN;
        }else if(ActivityTypeEnum.S_AT_POINT_TASK.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_POINT_TASK;
        }else if(ActivityTypeEnum.S_AT_FIGHT.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_FIGHT;
        }else if(ActivityTypeEnum.S_AT_SYNTHESIS.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_SYNTHESIS;
        }else if(ActivityTypeEnum.S_AT_ROLL.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_ROLL;
        }else if(ActivityTypeEnum.S_AT_TURNTABLE.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_TURNTABLE;
        }else if(ActivityTypeEnum.S_AT_OQ_LEVEL.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_OQLEVEL;
        }else if(ActivityTypeEnum.S_AT_MEMBER_INVITE.value().equals(activityType)){
            return CouponReceiveWayEnum.S_MCRW_MEMBERINVITE;
        } else  {
            return CouponReceiveWayEnum.S_MCRW_EVENT_GIFT;
        }
    }


    private String getScoreTask(String activityType){
        if (ActivityTypeEnum.S_AT_MEMBER_INVITE.value().equals(activityType)){
            return ScoreTaskEnum.S_SST_MEMBER_INVITE.value();
        }else  {
            return "";
        }
    }


    private void handelMemberRecords(List<Long> memberIds,BigDecimal marketingFee,Long chainId,String traceId){
        String lang = XcrmThreadContext.getLocale();

        taskExecutor.execute(()->{
            try {
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                XcrmThreadContext.setChainId(chainId);
                MDCTraceUtils.putTraceId(traceId);
                XcrmThreadContext.setLocale(lang);
                List<MemberRecordReq> recordReqs = new ArrayList<>();
                MemberRecordReq req;
                for (Long memberId : memberIds) {
                    req = new MemberRecordReq();
                    req.setMarketingFee(marketingFee);
                    req.setMemberId(memberId);
                    recordReqs.add(req);
                }
                memberFeignClient.batchInsertOrUpdateMemberRecord(recordReqs);
            } catch (Exception e) {
               log.error("保存记录报错",e);
            } finally {
                XcrmThreadContext.removeChainId();
                XcrmThreadContext.removeAccessType();
                MDCTraceUtils.removeTraceId();
                XcrmThreadContext.removeLocale();
            }

        });

    }

    @EventListener(condition = "event.giftType eq 'tbTicket'")
    public void handlerTbTicket(ActivityGiftEvent event) {

        Long activityId = event.getActivityId();

        List<Long> memberIds = event.getMemberIds();
        if (ListUtil.isEmpty(memberIds)) {
            log.warn("ActivityGiftListener.handlerTbTicket not fount member! activityId={}", activityId);
            return;
        }

        JSONObject jsonObject = JSON.parseObject(event.getGiftJson());
                Object ob = jsonObject.get("tbTicketJson");
               if (null != ob){
                   TbTicketVo vo = JSONObject.parseObject(ob.toString(), TbTicketVo.class);
                   BigDecimal prize = vo.getPrice();
                   Integer num = vo.getNum();
                   String tbSkuId = vo.getTbSkuId();
                   String tbSpuId = vo.getTbSpuId();
                   String tbType = vo.getTbType();
                   String title = vo.getTitle();
                   Long configId = vo.getConfigId();
                   if (tbType.equals("custom")){
                       //查询当前淘宝票是否存在不存在将不发
                       RechargeTbConfig rechargeTbConfig = dao.queryById(configId, RechargeTbConfig.class);
                       if (null == rechargeTbConfig){
                           return;
                       }
                   }
                   if (tbType.equals("regular")){
                       ProductBoxItem productBoxItem = dao.queryById(event.getActivityId(), ProductBoxItem.class);
                       if (productBoxItem == null){
                           return;
                       }
                       SaasQueryBuilder queryBox = SaasQueryBuilder.where(Restrictions.eq("spuId",productBoxItem.getSpuId()));
                       ProductBox box = dao.query(queryBox,ProductBox.class);
                       if (null == box || null == box.getTbConfigId()){
                           return;
                       }
                       SaasQueryBuilder queryRechargeTbConfig = SaasQueryBuilder.where(Restrictions.eq("id",box.getTbConfigId()));
                       RechargeTbConfig rechargeTbConfig = dao.query(queryRechargeTbConfig, RechargeTbConfig.class);
                       if (null == rechargeTbConfig){
                           return;
                       }
                       prize = rechargeTbConfig.getPayMoney();
                       tbSkuId = rechargeTbConfig.getTbSkuId();
                       tbSpuId =  rechargeTbConfig.getTbSpuId();
                       JSONObject j = jsonObject.getJSONObject("tbTicketJson");
                       j.put("tbSkuId",tbSkuId);
                       j.put("tbSpuId",tbSpuId);
                       j.put("price",prize);
                       j.put("configId",rechargeTbConfig.getId());
                       event.setGiftJson(jsonObject.toString());
                   }
                   for (Long memberId:memberIds){
                       MemberTbTicketRequest request = new MemberTbTicketRequest();
                       request.setMemberId(memberId);
                       request.setQuantity(num);
                       request.setTitle(title);
                       request.setType(MemberTbTicketTypeEnum.income.value());
                       request.setTbSkuId(tbSkuId);
                       request.setTbSpuId(tbSpuId);
                       if (StringUtils.isNotBlank(title) && title.equals("抽赏赠送")) {
                           request.setRecordType(ActivityTypeEnum.S_AT_REWARD_GIFT.value());
                       }if (StringUtils.isNotBlank(title) && title.equals("次数赠送")) {
                           request.setRecordType(ActivityTypeEnum.S_AT_ISSUE.value());
                       }
                       memberFeignClient.handelMemberTbTicket(request);
                   }
                   //保存记录
                   ActivityRecordRequest recordRequest = new ActivityRecordRequest();
                   recordRequest.setActivityId(activityId);
                   recordRequest.setActivityType(event.getActivityType());
                   recordRequest.setGiftType("tbTicket");
                   recordRequest.setQuantity(num);
                   recordRequest.setTbTicket(prize);
                   recordRequest.setExtJson(jsonObject.toJSONString());
                   recordRequest.setUserId(event.getUserId());
                   this.saveActivityRecord(recordRequest, memberIds);
                   sendMessage(event, memberIds);
               }
    }
}
