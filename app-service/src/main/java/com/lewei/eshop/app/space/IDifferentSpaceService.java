package com.lewei.eshop.app.space;

import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.space.DeleteDifferentSpaceRequest;
import com.lewei.eshop.common.request.space.DifferentSpaceRequest;
import com.lewei.eshop.common.request.space.DifferentSpaceStatusRequest;
import com.lewei.eshop.common.vo.space.DifferentSpaceDetailVo;
import com.lewei.eshop.common.vo.space.DifferentSpaceRewardRecordDetailVo;
import com.lewei.eshop.common.vo.space.DifferentSpaceRewardRecordTopVo;
import com.xcrm.common.page.Pagination;

import java.util.List;

/**
 * <p>
 * 对对碰 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IDifferentSpaceService {


    /**
     * 保存异空间
     * @param userId
     * @param request
     * @return
     */
    Long saveDifferentSpace(Long userId , DifferentSpaceRequest request, Long chainId);

    /**
     * 编辑异空间
     * @param userId
     * @param request
     * @param differentSpaceId
     */
    void updateDifferentSpace(Long userId, DifferentSpaceRequest request,Long differentSpaceId,Long chainId);

    /**
     * 查询异空间列表
     * @param queryKey
     * @param status
     * @param request
     * @return
     */
    Pagination queryDifferentSpacePage(String queryKey, String status, PageRequest request);

    /**
     * 查询异空间详情
     * @param differentSpaceId
     * @return
     */
    DifferentSpaceDetailVo queryDifferentSpaceDetail(Long differentSpaceId);

    /**
     * 批量删除
     * @param request
     */
    void bathDeleteDifferentSpace(DeleteDifferentSpaceRequest request, Long chainId);

    /**
     * 状态变更
     * @param request
     * @param userId
     */
    void updStatus(DifferentSpaceStatusRequest request, Long userId);

    /**
     * 异空间入场劵记录
     * @param queryKey
     * @param request
     * @return
     */
    Pagination queryDifferentSpaceRecord(Long differentSpaceId,String queryKey,Boolean isRecover,PageRequest request);

    /**
     * 异空间入场劵记录详情
     * @return
     */
    Pagination queryDifferentSpaceRecordDetail(Long differentSpaceId, Long memberId, PageRequest request);

    /**
     * 查询入场劵记录Top数据
     * @param differentSpaceId
     * @return
     */
    DifferentSpaceRewardRecordTopVo queryDifferentSpaceRecordQuantity(Long differentSpaceId,String queryKey,Boolean isRecover);
}
