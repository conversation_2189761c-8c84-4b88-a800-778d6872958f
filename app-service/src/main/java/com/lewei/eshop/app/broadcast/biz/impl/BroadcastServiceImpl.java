package com.lewei.eshop.app.broadcast.biz.impl;

import com.lewei.eshop.app.AppErrorDef;
import com.lewei.eshop.app.broadcast.biz.IBroadcastService;
import com.lewei.eshop.app.client.paas.MessageFeignClient;
import com.lewei.eshop.common.request.broadcast.BroadcastConfigRequest;
import com.lewei.eshop.common.request.broadcast.PubSubConfigRequest;
import com.lewei.eshop.common.vo.broadcast.BroadCastConfigVo;
import com.lewei.eshop.entity.broadcast.BroadCastConfig;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.expression.Restrictions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;

/**
 * 播报
 *
 * <AUTHOR>
 * @since 2023/6/30
 */
@Service
@Transactional
@Slf4j
public class BroadcastServiceImpl implements IBroadcastService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private MessageFeignClient messageFeignClient;


    @Override
    public BroadCastConfigVo queryBroadcastConfig(Long chainId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1));
        BroadCastConfig config = dao.query(query, BroadCastConfig.class);
        BroadCastConfigVo vo = new BroadCastConfigVo();
        if(ObjectUtils.isNotEmpty(config)){
            BeanUtils.copyProperties(config,vo);
        }
        return vo;
    }




    @Override
    public void saveBroadcastConfig(BroadcastConfigRequest request, Long chainId, Long userId) {

//        if(request.getDailyNum()* request.getHourNum() != 24){
//            throw new BizCoreRuntimeException(AppErrorDef.BROADCAST_CONFIG_NUM_ERROR);
//        }


        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1));
        BroadCastConfig config = dao.query(query, BroadCastConfig.class);
        if (config == null) {
            config = new BroadCastConfig();
            config.setCreateBy(userId);
            config.setCreated(DateFormatUtils.getNow());
        }else{
            config.setUpdateBy(userId);
            config.setUpdated(DateFormatUtils.getNow());
        }
        config.setPage(request.getPage());
        config.setIsOpen(request.getIsOpen());
        config.setDailyNum(request.getDailyNum());
        config.setHourNum(request.getHourNum());
        config.setDuration(request.getDuration());
        config.setTitle(request.getTitle());
        config.setMemberIds(request.getMemberIds());
        if(null == config.getId()){
            dao.save(config);
        }else{
            dao.update(config);
        }
    }

    @Override
    public Map queryPubsubConfig() {
        return messageFeignClient.queryPubsubConfig();
    }

    @Override
    public void savePubsubConfig(PubSubConfigRequest request) {
        messageFeignClient.savePubsubConfig(request);
    }


}
