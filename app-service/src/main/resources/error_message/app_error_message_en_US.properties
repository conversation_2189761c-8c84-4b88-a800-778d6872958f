91001={0}
91002=Wrong profit range
91003=The price range of prizes is wrong
91004=Please select a member
91005=The compensation object is duplicated with the existing 0, please adjust the object or profit range
91006=Sweet rule name cannot be repeated
91007=Configuration already exists, please refresh and try again
91008=The applicable object is the same as the existing 0, please adjust the object or the range of the reward amount
91009=The selected blind box can not be repeated every time
91010={0}
91011=Configure ROLL Settings first
91012=The condition of the reward is abnormal
91013=Only the pending audit status can be changed
91014=The ROLL room status is incorrect
91015=The room in progress cannot be deleted. If there is any problem, the room can be closed and deleted
91016=Only the unstarted state can be edited
91017=Only the official ROLL room can be edited
91018=Nicknames cannot be repeated
91019=R<PERSON><PERSON> has expired and cannot be started or approved
91020=Please do not add the selected products again
91021=Cannot be deleted in the participating state
91022=Fight is not enabled
91023=Fight only supports unlimited blind boxes
91027=Enable battle sound effect but not upload sound effect audio
91028=The automatic start time cannot be less than 5 minutes from the current time
91029=When the opening method is automatic, the automatic opening time cannot be empty
91030=The automatic start time cannot be greater than the lottery time
91031=The automatic start time cannot be greater than the close time
91032=Payment failed, the membership number has been revoked
91033=Booster configuration cannot be empty
91034=synthesized product cannot be empty
91035=the synthesized product cannot exceed 1
91036=cannot exceed 10 synthesized products
91037=composite product cannot be empty
91038=total synthesized quantity cannot exceed 2000
91039=composite product quantity cannot be empty
91040=specified composite product recycling price cannot be less than the sales price of the synthesized product
91041=current name is duplicate, please re-enter the name
91042=The product of daily limit times and hourly limit times must be 24
91043=maximum percentage of revenue setting cannot exceed 1
91044=maximum fixed value set for income cannot exceed 999
91045=current blind box already has a treasure bowl rule
91046=current treasure bowl rule does not exist
91047=current treasure trove has been opened and cannot be deleted. Please pause before deleting
91048=The automatic listing time must be 30 minutes after the current time
91049=Can only modify the automatic listing status to not listed
91050=No automatic listing information found
91051=No automatic listing configuration information found
91052=There cannot be duplicate blind boxes in the shelving group
91053=stage reward cannot be empty
91054=applicable range cannot be empty
91055={0} stage reward range cannot be less than {1} stage reward range
91056=applicable range selection blind box quantity cannot exceed 200
91057=applicable range: the number of selected members cannot exceed 200
91058=current flow reversal configuration does not exist
91059=No doll machine information found
91060=Actual winning probability cannot be empty
91061=Maximum box opening probability cannot exceed 80
91062=Minimum box opening probability cannot be less than 0.01%
91063=The actual probability of winning the lottery cannot be reduced by more than 50%
91064=Applicable object is duplicated with existing [{0}], please adjust the object or box opening probability range
91065=cumulative recharge amount cannot be less than zero
91066=The current loss compensation allocation does not exist
91067=cumulative trade amount cannot be less than zero
91068=The unit price within the gift range cannot have intersections
91069=The starting price of the unit price cannot be greater than the ending price
91070=The specified number of users cannot be 0
91071=The amount in the next stage must be greater than the amount in the previous stage
91072=Restricted consumption limit must be greater than the maximum stage amount
91073=Registration restriction setting already exists in the current number segment
91074=The synthetic product limit cannot be empty
91075=Label already exists
91076=Add a minimum of 2 and a maximum of 8 to the turntable grid
91077=The total probability of winning must be equal to 100%
91079=When enabling custom quantity, the selling price and minimum purchase quantity cannot be empty
91080=The range of {0} is {1}- {2} Between
91081=Challenge and collision, level information cannot be empty
91082=Rule type cannot be empty
91083=The content of the renewal package cannot be empty
91084=The number of prizes must be 10
91085=The prize cannot be repeated
91086=This collision no longer exists
91087=This pair of collisions has not been taken down and cannot be edited
91088=The final level must have an empty number of clearances
91089=The number of collision rewards cannot be empty
91090=The number of collision rewards cannot be the same
91091=Only records that are in the pending status can be audited
91092=The event has ended, and no event information was found
91093=Up to 50 prizes can be added
91094=This alternate space no longer exists
91095=This alternate space has not been taken down and cannot be edited
91096=The same blind box can only open one different space
91097=The total probability must be equal to 100%

