<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.app.different.space">


    <resultMap id="queryDifferentSpaceDetailMap" type="com.lewei.eshop.common.vo.space.DifferentSpaceDetailVo">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="boxSpuId" column="boxSpuId"/>
        <result property="progressNum" column="progressNum"/>
        <result property="clearCategoryId" column="clearCategoryId"/>
        <result property="status" column="status"/>
        <result property="boxName" column="boxName"/>
        <result property="created" column="created"/>

        <collection property="rewards" ofType="com.lewei.eshop.common.vo.space.DifferentSpaceDetailVo$Reward">
            <result property="spaceRewardId" column="spaceRewardId"/>
            <result property="spuId" column="spuId"/>
            <result property="skuId" column="skuId"/>
            <result property="categoryId" column="categoryId"/>
            <result property="odds" column="odds"/>
            <result property="priority" column="priority"/>
            <result property="productCategoryName" column="productCategoryName"/>
            <result property="rewardName" column="rewardName"/>
            <result property="mainImage" column="mainImage"/>
            <result property="priceFee" column="priceFee"/>
            <result property="primeCostFee" column="primeCostFee"/>
            <result property="recoveryFee" column="recoveryFee"/>
        </collection>
    </resultMap>

    <select id="queryDifferentSpacePage" resultType="com.lewei.eshop.common.vo.space.DifferentSpacePageVo">
        SELECT
            d.id,
            d.title,
            d.boxSpuId,
            d.progressNum,
            d.clearCategoryId,
            d.status,
            code.sysName AS statusName,
            spu.name AS boxName,
            d.created
        FROM t_t_different_space d
        LEFT JOIN t_t_product_spu spu ON d.boxSpuId = spu.id
        LEFT JOIN t_b_sys_code code on d.status = code.sysCode
        WHERE d.dataStatus = 1
        AND d.chainId = #{chainId}
        <if test="queryKey != null and queryKey != ''">
            <trim prefix="AND (" suffix=")">
                d.title LIKE CONCAT('%', #{queryKey}, '%') OR
                spu.name LIKE CONCAT('%', #{queryKey}, '%')
            </trim>
        </if>
        <if test="status != null and status != ''">
            AND d.status = #{status}
        </if>
        ORDER BY d.created DESC
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>

    <select id="queryDifferentSpaceDetail" resultMap="queryDifferentSpaceDetailMap">
        SELECT
            d.id,
            d.title,
            d.boxSpuId,
            d.progressNum,
            d.clearCategoryId,
            d.status,
            spu.name AS boxName,
            d.created,
            dsr.id AS spaceRewardId,
            dsr.spuId,
            dsr.skuId,
            dsr.categoryId,
            pc.productCategoryName,
            dsr.odds,
            dsr.priority,
            spu2.name AS rewardName,
            spu2.mainImage,
            spu2.priceFee,
            spu2.primeCostFee,
            spu2.recoveryFee
        FROM t_t_different_space d
        LEFT JOIN t_t_product_spu spu ON d.boxSpuId = spu.id
        LEFT JOIN t_t_different_space_reward dsr ON dsr.differentSpaceId = d.id AND dsr.dataStatus = 1
        LEFT JOIN t_t_product_category pc ON dsr.categoryId = pc.id
        LEFT JOIN t_t_product_spu spu2 ON dsr.spuId = spu2.id
        WHERE d.dataStatus = 1
        AND d.chainId = #{chainId}
        AND d.id = #{id}
    </select>

    <select id="queryDifferentSpaceRecord" resultType="com.lewei.eshop.common.vo.space.DifferentSpaceRecordDetailVo">
        SELECT
            dsm.id,
            dsm.memberId,
            dsm.quantity,
            dsm.giftNum,
            m.memberName,
            m.mobile,
            m.headImage
        FROM t_t_different_space_member dsm
        LEFT JOIN t_t_member m ON dsm.memberId = m.id
        WHERE dsm.differentSpaceId = #{differentSpaceId}
        AND dsm.chainId = #{chainId}
        AND dsm.dataStatus = 1
        <if test="queryKey != null and queryKey != ''">
            <trim prefix="AND (" suffix=")">
                m.memberName LIKE CONCAT('%', #{queryKey}, '%') OR
                m.mobile LIKE CONCAT('%', #{queryKey}, '%')
            </trim>
        </if>
        <if test="isRecover != null">
            AND m.isRecover = #{isRecover}
        </if>
        ORDER BY dsm.created DESC
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>

    <select id="queryDifferentSpaceRecordDetail" resultType="com.lewei.eshop.common.vo.space.DifferentSpaceRewardRecordDetailVo">
        SELECT
            id,
            mainImage,
            rewardName,
            categoryName,
            created
        FROM t_t_different_space_reward_record
        WHERE chainId = #{chainId}
        AND differentSpaceId = #{differentSpaceId}
        AND memberId = #{memberId}
        ORDER BY created DESC
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>

    <select id="queryDifferentSpaceUp" resultType="com.lewei.eshop.entity.space.DifferentSpace">
        SELECT
            id
        FROM t_t_different_space
        WHERE boxSpuId in
            <foreach collection="boxSpuIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        AND id not in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        AND status = #{status}
        AND dataStatus = 1
    </select>

    <select id="queryDifferentSpaceRecordQuantity" resultType="com.lewei.eshop.common.vo.space.DifferentSpaceRewardRecordTopVo">
        SELECT
            IFNULL(sum(dsm.quantity),0) AS totalQuantity
        FROM t_t_different_space_member dsm
         LEFT JOIN t_t_member m ON dsm.memberId = m.id
        WHERE dsm.chainId = #{chainId}
        <if test="queryKey != null and queryKey != ''">
            <trim prefix="AND (" suffix=")">
                m.memberName LIKE CONCAT('%', #{queryKey}, '%') OR
                m.mobile LIKE CONCAT('%', #{queryKey}, '%')
            </trim>
        </if>
        <if test="isRecover != null">
            AND m.isRecover = #{isRecover}
        </if>
        AND differentSpaceId = #{differentSpaceId}
    </select>
</mapper>