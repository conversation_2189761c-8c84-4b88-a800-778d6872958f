package com.lewei.eshop.pub.biz.open.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2020/12/7
 */
@Data
public class WxMemberCardRequest {
    /**
     * 品牌名
     */
    @NotEmpty(message = "brandName is required")
    private String brandName;
    /**
     * logo
     */
    @NotEmpty(message = "logo is required")
    private String logo;
    /**
     * 颜色
     */
    @NotEmpty(message = "color is required")
    private String color;
    /**
     * 背景图
     */
    @NotEmpty(message = "backgroundImage is required")
    private String backgroundImage;

    /**
     * 特权说明
     */
    @NotEmpty(message = "prerogative is required")
    private String prerogative;
    /**
     * 使用提醒
     */
    @NotEmpty(message = "notice is required")
    private String notice;
    /**
     * 客服电话
     */
    @NotEmpty(message = "servicePhone is required")
    private String servicePhone;
    /**
     * 卡券使用说明
     */
    @NotEmpty(message = "description is required")
    private String description;


}
