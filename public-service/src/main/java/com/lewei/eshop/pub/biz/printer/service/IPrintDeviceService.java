package com.lewei.eshop.pub.biz.printer.service;


import com.lewei.eshop.common.request.printer.OrderPrintRequest;

import java.util.HashMap;

/**
 * 打印设备Service
 * <AUTHOR>
 */
public interface IPrintDeviceService {

    /**
     * 查询打印信息
     * @param request        订单打印请求
     * @param parameter      返回参数
     * @param accessKeyId    请求访问ID
     */
    void queryPrintInfo(OrderPrintRequest request, HashMap<String, Object> parameter, String accessKeyId);

    /**
     * 组装打印信息
     * @param parameter
     */
    void buildPrintInfo(HashMap<String, Object> parameter);

    /**
     * 发送打印信息
     * @param parameter
     */
    HashMap<String, String> sendPrintInfo(HashMap<String, Object> parameter);
}