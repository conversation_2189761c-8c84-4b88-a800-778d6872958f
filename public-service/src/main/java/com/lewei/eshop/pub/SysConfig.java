package com.lewei.eshop.pub;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 本地资源属性配置
 */
@Data
@Configuration(value = "sysConfig")
//@ConfigurationProperties(prefix = "xcrm")
public class SysConfig {

    /**
     * 环境信息
     */
    @Value("${spring.profiles.active}")
    private String projectProfile;

//    /**
//     * 账号缓存cache的host服务地址
//     */
//    @Value("${cacheAuthHost}")
//    private String cacheAuthHost;
//
//    /**
//     * 账号缓存cache的实现接口
//     */
//    @Value("${cacheAuthProvider}")
//    private String cacheAuthProvider;


    /**
     * 负载情况下服务节点id
     */
    @Value("${xcrm.serverId}")
    private String serverId;

    /**
     * 上下文
     */
    @Value("${server.servlet.context-path}")
    private String contextPath;

    /**
     * 账号服务
     */
    /**
     * 账号服务地址
     */
    @Value("${xcrm.account.host}")
    private String accountServiceHost;

//    /**
//     * 系统默认主页地址
//     */
//    @Value("${xcrm.oauth2.default-url}")
//    private String oauth2DefaultRedirectUrl;
//
//    @Value("${xcrm.oauth2.client-id}")
//    private String clientId;
//
//    @Value("${xcrm.oauth2.client-secret}")
//    private String clientSecret;

    /**
     * 第三方平台appId
     */
    @Value("${wx.open.appId}")
    private String openAppId;
    /**
     * 第三方平台appSecret
     */
    @Value("${wx.open.appSecret}")
    private String openAppSecret;
    /**
     * 微信第三方平台 公众号消息校验Token
     */
    @Value("${wx.open.token}")
    private String wxTPToken;

    /**
     * 微信第三方平台 公众号消息加解密Key
     */
    @Value("${wx.open.encodingAesKey}")
    private String wxTPEncodingAesKey;
    /**
     * 微信第三方平台获取公众号授权后的回调url
     */
    @Value("${wx.open.authCallbackUr}")
    private String wxTPAuthCallbackUrl;

    /**
     * 底层服务地址
     */
    @Value("${xcrm.paas.service}")
    private String paasServiceUrl;

    /**
     * eshop b端服务地址
     */
    @Value("${xcrm.eshop.service}")
    private String eshopService;


    /**
     * 乐纬科技公众号appId
     */
    @Value("${lewei.mp.appId}")
    private String  lwMpAppId;
    /**
     * 乐纬科技公众号appSecret
     */
    @Value("${lewei.mp.appSecret}")
    private String  lwMpAppSecret;
    /**
     * 乐纬科技公众号消息校验Token
     */
    @Value("${lewei.mp.token}")
    private String  lwMpToken;

    /**
     * 乐纬科技公众号 消息加解密Key
     */
    @Value("${lewei.mp.encodingAESKey}")
    private String lwMpEncodingAesKey;


    /**
     * ossAccessKey
     */
    @Value("${ali.oss.autoconfig.access-key}")
    private String ossAccessKey;


    /**
     * ossSecretKey
     */
    @Value("${ali.oss.autoconfig.secret-key}")
    private String ossSecretKey;


    /**
     * timezone
     */
    @Value("${lewei.timezone}")
    private String timezone;
}