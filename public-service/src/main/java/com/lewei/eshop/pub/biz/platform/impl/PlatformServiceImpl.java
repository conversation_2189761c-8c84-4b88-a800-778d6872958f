package com.lewei.eshop.pub.biz.platform.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.pay.PayWxRequest;
import com.lewei.eshop.common.request.pub.PlatformOrderRequest;
import com.lewei.eshop.common.vo.pub.PlatformProductListVO;
import com.lewei.eshop.common.vo.pub.PlatformProductVO;
import com.lewei.eshop.entity.app.AppOrder;
import com.lewei.eshop.entity.app.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.app.types.PlTypeEnum;
import com.lewei.eshop.pub.PubErrorConstants;
import com.lewei.eshop.pub.SysConfig;
import com.lewei.eshop.pub.biz.platform.IPlatformService;
import com.lewei.eshop.pub.biz.platform.PaasHttpProxy;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.page.Pagination;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
*
* <AUTHOR>
* @date 2020/3/30
**/
@Service
@Transactional
public class PlatformServiceImpl implements IPlatformService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private PaasHttpProxy paasHttpProxy;

    private static final String DEFAULT_PAY_APPID = "wxef0156d8ae566409";

    public PlatformProductVO queryPlatformProduct(Long productId) {
        QueryBuilder queryBuilder = QueryBuilder.create("com.lewei.eshop.public.platform.queryPlatformProduct")
                .setParam("productId", productId);
        return dao.findForObj(queryBuilder, PlatformProductVO.class);
    }

    @Override
    public Boolean queryOrderPayStatus(String orderSn) {
         SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("orderSn", orderSn));
        AppOrder order = dao.query(query, AppOrder.class);
        return order != null ? order.getPayStatus() : null;
    }

    @Override
    public List<PlatformProductListVO> queryPlatformProducts(String plType) {
        QueryBuilder queryBuilder = QueryBuilder.create("com.lewei.eshop.public.platform.queryPlatformProducts")
                .setParam("plType", plType);
        return dao.findForList(queryBuilder, PlatformProductListVO.class);    }

    @Override
    public Pagination queryRechargeRecord(String plType,PageRequest request) {
        Ssqb query = Ssqb.create("com.lewei.eshop.public.platform.queryRechargeRecord")
                .setParam("plType",plType)
                .setParam("pageSize",request.getPageSize())
                .setParam("pageNo",request.getPageNo());
        Pagination pagination = dao.findForPage(query);
        return pagination;

    }

    public Map<String, Object> payOrder(PlatformOrderRequest request, Long chainId, Long opUserId) {

        AppOrder order = null;
        if(StringUtils.isNotBlank(request.getOrderSn())) {
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderSn", request.getOrderSn()))
                    .and(Restrictions.eq("dataStatus", 1));
            order = dao.query(queryBuilder, AppOrder.class);
            if(order != null && !OrderEnum.S_OS_UNPAID.value().equals(order.getStatus())) {
                throw new BizCoreRuntimeException(PubErrorConstants.ORDER_IS_PAYED);
            }
        }
        if(order == null) {

            PlatformProductVO product = this.queryPlatformProduct(request.getProductId());

            if(!PlTypeEnum.PL_MICRO_APP.value().equals(product.getPlType()) && !PlTypeEnum.PL_WX_ROBOT.value().equals(product.getPlType())
                    && !PlTypeEnum.PL_SMS_RECHARGE.value().equals(product.getPlType()) && !PlTypeEnum.PL_CLOUD_AUTH_RECHARGE.value().equals(product.getPlType())) {
                throw new BizCoreRuntimeException(PubErrorConstants.ORDER_PRODUCT_NOT_SUPPORT);
            }
            String extParamJson = request.getExtParamJson();
            JSONObject extJson = null;

            if (PlTypeEnum.PL_SMS_RECHARGE.value().equals(product.getPlType()) || PlTypeEnum.PL_CLOUD_AUTH_RECHARGE.value().equals(product.getPlType())){
                extParamJson = product.getProductJson();
            }
            if(StringUtils.isNotBlank(product.getOrderExtParam())) {

                if(StringUtils.isBlank(extParamJson)) {
                    throw new BizCoreRuntimeException(PubErrorConstants.ORDER_EXT_PARAM_IS_REQUIRED);
                }
                String orderExtParam = product.getOrderExtParam();
                String[] orderExtParams = orderExtParam.split(",");
                try {
                    extJson = JSON.parseObject(extParamJson);
                    for(String param : orderExtParams) {
                        if(StringUtils.isBlank(extJson.getString(param))) {
                            throw new BizCoreRuntimeException(PubErrorConstants.ORDER_EXT_PARAM_IS_REQUIRED);
                        }
                    }
                } catch (Exception e) {
                    throw new BizCoreRuntimeException(PubErrorConstants.ORDER_EXT_PARAM_IS_REQUIRED);
                }
            }


            order = new AppOrder();
            order.setCreated(new Timestamp(System.currentTimeMillis()));
            order.setOrderSn(System.currentTimeMillis() + "");
            order.setOrderBizType(OrderBizTypeEnum.PLATFORM.value());
            order.setProductId(product.getProductId());
            order.setProductType(product.getPlType());
            order.setOrderTitle(product.getProductName());
            order.setStatus(OrderEnum.S_OS_UNPAID.value());
            order.setMemberId(opUserId);
            order.setMobile("");
            order.setNickName("");
            order.setOpenId("");
            order.setPaymentMethod("S_OPM_WECHAT");
            order.setOrderMoney(this.getOrderMoney(product, request.getAmount(), extJson));
            order.setPaymentMoney(order.getOrderMoney());
            order.setExtJson(extParamJson);
            order.setAmount(request.getAmount());
            dao.save(order);
        } else {
            order.setUpdated(new Timestamp(System.currentTimeMillis()));
            dao.update(order);
        }
        //调取微信支付
        String payParamJson = this.callWxPayApi(order.getOrderTitle(), order.getPaymentMoney(), order.getOrderSn(), order.getOpenId(), chainId);

        Map<String, Object> retMap = new HashMap<>(2);
        retMap.put("payParam", payParamJson);
        retMap.put("orderSn", order.getOrderSn());
        return retMap;
    }

    @Override
    public Map<String, Object> payRenewOrder(PlatformOrderRequest request, Long chainId, Long opUserId) {

        AppOrder order = null;
        if(StringUtils.isNotBlank(request.getOrderSn())) {
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderSn", request.getOrderSn()))
                    .and(Restrictions.eq("dataStatus", 1));
            order = dao.query(queryBuilder, AppOrder.class);
            if(order != null && !OrderEnum.S_OS_UNPAID.value().equals(order.getStatus())) {
                throw new BizCoreRuntimeException(PubErrorConstants.ORDER_IS_PAYED);
            }
        }
        if(order == null) {

            PlatformProductVO product = this.queryPlatformProduct(request.getProductId());

            if(!PlTypeEnum.PL_WX_ROBOT_RENEW.value().equals(product.getPlType())) {
                throw new BizCoreRuntimeException(PubErrorConstants.ORDER_PRODUCT_NOT_SUPPORT);
            }
            String extParamJson = request.getExtParamJson();
            JSONObject extJson = null;
            if(StringUtils.isNotBlank(product.getOrderExtParam())) {

                if(StringUtils.isBlank(extParamJson)) {
                    throw new BizCoreRuntimeException(PubErrorConstants.ORDER_EXT_PARAM_IS_REQUIRED);
                }
                String orderExtParam = product.getOrderExtParam();
                String[] orderExtParams = orderExtParam.split(",");
                try {
                    extJson = JSON.parseObject(extParamJson);
                    for(String param : orderExtParams) {
                        if(StringUtils.isBlank(extJson.getString(param))) {
                            throw new BizCoreRuntimeException(PubErrorConstants.ORDER_EXT_PARAM_IS_REQUIRED);
                        }
                    }
                } catch (Exception e) {
                    throw new BizCoreRuntimeException(PubErrorConstants.ORDER_EXT_PARAM_IS_REQUIRED);
                }
            }

            String robotCodes = extJson.getString("robotCodes");
            Integer amount = robotCodes.split(",").length;

            order = new AppOrder();
            order.setCreated(new Timestamp(System.currentTimeMillis()));
            order.setOrderSn(System.currentTimeMillis() + "");
            order.setOrderBizType(OrderBizTypeEnum.PLATFORM.value());
            order.setProductId(product.getProductId());
            order.setProductType(product.getPlType());
            order.setOrderTitle(product.getProductName());
            order.setStatus(OrderEnum.S_OS_UNPAID.value());
            order.setMemberId(opUserId);
            order.setMobile("");
            order.setNickName("");
            order.setOpenId("");
            order.setPaymentMethod("S_OPM_WECHAT");
            order.setOrderMoney(this.getOrderMoney(product, amount, extJson));
            order.setPaymentMoney(order.getOrderMoney());
            order.setExtJson(extParamJson);
            order.setAmount(amount);
            dao.save(order);
        } else {
            order.setUpdated(new Timestamp(System.currentTimeMillis()));
            dao.update(order);
        }
        //调取微信支付
        String payParamJson = this.callWxPayApi(order.getOrderTitle(), order.getPaymentMoney(), order.getOrderSn(), order.getOpenId(), chainId);

        Map<String, Object> retMap = new HashMap<>(2);
        retMap.put("payParam", payParamJson);
        retMap.put("orderSn", order.getOrderSn());
        return retMap;
    }

    private BigDecimal getOrderMoney(PlatformProductVO product, Integer amount,  JSONObject extJson) {

        BigDecimal orderMoney = product.getPrice().multiply(BigDecimal.valueOf(amount));
        if(extJson != null && extJson.get("rechargeMonth") != null) {
            orderMoney = orderMoney.multiply(extJson.getBigDecimal("rechargeMonth"));
        }
        return orderMoney;
    }


    private String callWxPayApi(String orderTitle, BigDecimal paymentMoney, String orderCodes, String openId, Long chainId) {

        PayWxRequest payWxRequest = new PayWxRequest();
        payWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/public/pay/wx/cb");
        payWxRequest.setOpenId(openId);
        payWxRequest.setOrderCodes(orderCodes);
        payWxRequest.setOrderTitle(orderTitle);
        payWxRequest.setPaymentMoney(paymentMoney);
        payWxRequest.setPayMethod("wx_user_scan");
        payWxRequest.setWxAppId(DEFAULT_PAY_APPID);

        Map retMap = paasHttpProxy.callWxPay(payWxRequest, chainId);
        return retMap.get("payParam") + "";
    }


}