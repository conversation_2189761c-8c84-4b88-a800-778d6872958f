package com.lewei.eshop.pub.biz.open.service;

import com.lewei.eshop.entity.ma.open.WxAccessToken;

/**
 * <p>
 * 授权第三方平台的token记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-23
 */
public interface IWxAccessTokenService  {

    /**
     * 第三方平台主动获取公众号授权的accessToken
     * <AUTHOR>
     * @since 2020-3-23
     * @param authAppId 公众号appId
     * @param authRefreshToken 第三方获取authToken的 refreshToken
     */
    void wxRefreshAuthToken(String authAppId,String authRefreshToken);

    /**
     * 查询authaccessToken
     * <AUTHOR>
     * @since 2020-3-26
     * @param chainId 总店id
     * @param tenantId 分店id
     * @param isMina 是否为小程序
     * @return WxAccessToken
     */
    WxAccessToken queryAccessToken(Long chainId,Long tenantId,Boolean isMina);
}
