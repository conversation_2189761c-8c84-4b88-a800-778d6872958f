package com.lewei.eshop.pub.biz.platform;

import com.alibaba.fastjson.JSON;
import com.lewei.eshop.auth.AuthConstants;
import com.lewei.eshop.auth.PaasToken;
import com.lewei.eshop.common.request.pay.PayWxRequest;
import com.lewei.eshop.pub.PubErrorConstants;
import com.lewei.eshop.pub.client.request.RobotRenewRequest;
import com.lewei.eshop.pub.client.request.RobotSaveRequest;
import com.lewei.eshop.pub.proxy.AbstractRestProxy;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.http.ExecutionContext;
import com.xcrm.common.http.HttpMethod;
import com.xcrm.common.http.RequestMessage;
import com.xcrm.common.http.ResponseMessage;
import com.xcrm.common.http.utils.SafeUtils;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
* <AUTHOR>
* @date 2020/1/18
**/
@Service
public class PaasHttpProxy extends AbstractRestProxy {



    /**
     * 调用支付平台接口，发起微信支付
     * @param requestObj
     * @param chainId
     * @return
     */
    public Map callWxPay(PayWxRequest requestObj, Long chainId) {

        ResponseMessage response = null;
        Boolean isSucc = false;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(endpoint);
        request.setMethod(HttpMethod.POST);
        request.setResourcePath("/pay/v1/order/platform");

        try
        {

            Map<String,String> headers = new HashMap<>();
            headers.put("x-access-token", PaasToken.build(chainId));
            headers.put("x-app-id", AuthConstants.PAAS_JWT_APPID);
            headers.put("x-mt-date", formatRfc822Date(new Date()));

            // 构造Body
            String body = JSON.toJSONString(requestObj);
            byte[] bodyByte = body.getBytes(StandardCharsets.UTF_8);
            request.setContent(new ByteArrayInputStream(bodyByte));
            request.setContentLength(bodyByte.length);
            headers.put("Content-Length", request.getContentLength() + "");
            headers.put("Content-Type", "application/json");

            request.setHeaders(headers);
            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
            return toGeneralResponse(request, response, Map.class);

        } catch (BizCoreRuntimeException e) {
            throw e;
        }  catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            throw new BizCoreRuntimeException(PubErrorConstants.BACK_END_ERROR, BizMessageSource.getInstance().getMessage("cem700087"));
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
    }

    /**
     * 取消分账设置
     * @param wxAppId               微信小程序id
     * @param chainId               总部id
     * @return                      是否成功
     */
    public Boolean cancelProfitShare(String wxAppId, Long chainId) {

        ResponseMessage response = null;
        Boolean isSucc = false;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(endpoint);
        request.setMethod(HttpMethod.PUT);
        request.setResourcePath("/pay/v1/config/cancel_profit_sharing");

        try
        {

            Map<String,String> headers = new HashMap<>();
            headers.put("x-access-token", PaasToken.build(chainId));
            headers.put("x-app-id", AuthConstants.PAAS_JWT_APPID);
            headers.put("x-mt-date", formatRfc822Date(new Date()));

            Map<String, Object> params = new HashMap<>();
            params.put("appId", wxAppId);
            // 构造Body
            String body = JSON.toJSONString(params);
            byte[] bodyByte = body.getBytes(StandardCharsets.UTF_8);
            request.setContent(new ByteArrayInputStream(bodyByte));
            request.setContentLength(bodyByte.length);
            headers.put("Content-Length", request.getContentLength() + "");
            headers.put("Content-Type", "application/json");

            request.setHeaders(headers);
            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
            toGeneralResponse(request, response, null);
            return true;
        } catch (BizCoreRuntimeException e) {
            throw e;
        }  catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            throw new BizCoreRuntimeException(PubErrorConstants.BACK_END_ERROR, BizMessageSource.getInstance().getMessage("cem700087"));
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
    }

    public void saveRobot(RobotSaveRequest requestObj, Long chainId) {

        ResponseMessage response = null;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(endpoint);
        request.setMethod(HttpMethod.POST);
        request.setResourcePath("/api/v1/robots");

        try
        {

            Map<String,String> headers = new HashMap<String, String>();
            headers.put("x-access-token", PaasToken.build(chainId));
            headers.put("x-app-id", AuthConstants.PAAS_JWT_APPID);
            headers.put("x-mt-date", formatRfc822Date(new Date()));

            // 构造Body
            String body = JSON.toJSONString(requestObj);
            byte[] bodyByte = body.getBytes("UTF-8");
            request.setContent(new ByteArrayInputStream(bodyByte));
            request.setContentLength(bodyByte.length);
            headers.put("Content-Length", request.getContentLength() + "");
            headers.put("Content-Type", "application/json");

            request.setHeaders(headers);
            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
            toGeneralResponse(request, response, null);

        } catch (BizCoreRuntimeException e) {
            throw e;
        }  catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            throw new BizCoreRuntimeException(PubErrorConstants.BACK_END_ERROR, BizMessageSource.getInstance().getMessage("cem700087"));
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
    }

    public void renewRobot(RobotRenewRequest requestObj, Long chainId) {

        ResponseMessage response = null;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(endpoint);
        request.setMethod(HttpMethod.PUT);
        request.setResourcePath("/api/v1/robots");

        try
        {

            Map<String,String> headers = new HashMap<String, String>();
            headers.put("x-access-token", PaasToken.build(chainId));
            headers.put("x-app-id", AuthConstants.PAAS_JWT_APPID);
            headers.put("x-mt-date", formatRfc822Date(new Date()));

            // 构造Body
            String body = JSON.toJSONString(requestObj);
            byte[] bodyByte = body.getBytes("UTF-8");
            request.setContent(new ByteArrayInputStream(bodyByte));
            request.setContentLength(bodyByte.length);
            headers.put("Content-Length", request.getContentLength() + "");
            headers.put("Content-Type", "application/json");

            request.setHeaders(headers);
            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
            toGeneralResponse(request, response, null);

        } catch (BizCoreRuntimeException e) {
            throw e;
        }  catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            throw new BizCoreRuntimeException(PubErrorConstants.BACK_END_ERROR, BizMessageSource.getInstance().getMessage("cem700087"));
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
    }

    /**
     * 获取rest接口需要的标准时间，遵循 RFC 1123 格式，使用 GMT 标准时间，
     * eg：Mon, 3 Jan 2010 08:33:47 GMT
     * @param date
     * @return
     */
    public static String formatRfc822Date(Date date) {
        return getRfc822DateFormat().format(date);
    }

    private static DateFormat getRfc822DateFormat() {
        SimpleDateFormat rfc822DateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);

        rfc822DateFormat.setTimeZone(new SimpleTimeZone(0, "GMT"));

        return rfc822DateFormat;
    }

    @PostConstruct
    public void init() {

        String endpoint = sysConfig.getPaasServiceUrl();
        log.debug("paasServiceUrl host = {}", endpoint);
        super.init(endpoint, 3);
    }
}