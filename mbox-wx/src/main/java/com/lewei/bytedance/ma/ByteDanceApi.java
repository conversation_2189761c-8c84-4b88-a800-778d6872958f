package com.lewei.bytedance.ma;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lewei.wx.ma.ErrorMessageDef;
import com.lewei.wx.ma.response.BaseWxResponse;
import com.lewei.wx.ma.response.Code2SessionResponse;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.http.*;
import com.xcrm.common.http.parser.ResponseParser;
import com.xcrm.common.http.utils.SafeUtils;
import com.xcrm.common.util.InputStreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2021/11/10
 */
@Service
@Slf4j
public class ByteDanceApi implements IByteDanceApi {

    private ServiceClient serviceClient;
    private URI endpoint;


    @Override
    public Code2SessionResponse code2Session(String appId, String appSecret, String code) {

        ResponseMessage response = null;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(endpoint);
        request.setMethod(HttpMethod.POST);
        request.setResourcePath("/api/apps/v2/jscode2session");

        Map<String, String> params = new HashMap<>();
        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("code", code);
//        params.put("grant_type", "authorization_code");

        try
        {

            // 构造Body
            String body = JSON.toJSONString(params);
            byte[] bodyByte = body.getBytes(StandardCharsets.UTF_8);
            request.setContent(new ByteArrayInputStream(bodyByte));
            request.setContentLength(bodyByte.length);
            Map<String,String> headers = new HashMap<>();
            headers.put("Content-Length", request.getContentLength() + "");
            headers.put("Content-Type", "application/json");
            request.setHeaders(headers);

            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
            return toGeneralResponse(request, response, Code2SessionResponse.class);

        } catch (BizCoreRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            throw new BizCoreRuntimeException(ErrorMessageDef.WX_ACCESS_ERROR);
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
    }

    /**
     * 获取响应数据
     *
     * @param request
     * @param response
     * @param responseClass
     * @return <T>
     * @throws Exception
     */
    protected <T> T toGeneralResponse(RequestMessage request, ResponseMessage response, Class<T> responseClass) throws Exception {

        //正常返回
        String json = response.getContent() != null ? InputStreamUtils.InputStreamTOString(response.getContent(), "UTF-8") : "";
        log.info("{} {}{} httpStatus={}, response ={}", request.getMethod().value()
                , endpoint.toString(), request.getResourcePath(), response.getStatusCode(), json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        if(StringUtils.isNotBlank(json) && jsonObject.getInteger("err_no") != null && jsonObject.getInteger("err_no") != 0) {

            throw new BizCoreRuntimeException(ErrorMessageDef.WX_COMMON_ERROR, jsonObject.getString("err_tips") + "(" + jsonObject.getInteger("err_no") + ")");
        }

        if(responseClass == null || StringUtils.isBlank(json)) {
            return null;
        }

        return  jsonObject.getObject("data", responseClass);
    }

    protected ResponseMessage send(RequestMessage request, ExecutionContext context, boolean keepResponseOpen)
            throws ServiceException, ClientException {
        ResponseMessage response = serviceClient.sendRequest(request, context);
        if (!keepResponseOpen) {
            SafeUtils.safeCloseResponse(response);
        }
        return response;
    }

    protected ExecutionContext createDefaultContext(HttpMethod method) {
        ExecutionContext context = new ExecutionContext();
        context.setCharset("utf-8");
        return context;
    }

    @PostConstruct
    public void init() {

        String endpoint = "https://developer.toutiao.com";
        log.debug("api.bytedance host = {}", endpoint);

        try{
            this.endpoint = new URI(endpoint);
        }catch(URISyntaxException e){
            throw new IllegalArgumentException(e);
        }
        ClientConfiguration config = new ClientConfiguration();
        config.setMaxErrorRetry(0);
        serviceClient = new DefaultServiceClient(config);
    }
}
