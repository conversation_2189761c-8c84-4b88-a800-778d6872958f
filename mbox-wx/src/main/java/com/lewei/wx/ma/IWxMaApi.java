package com.lewei.wx.ma;

import com.alibaba.fastjson.JSON;
import com.lewei.wx.ma.response.*;

import java.util.Map;

/**
* 微信api接口
* <AUTHOR>
* @date 2020/3/1
**/

public interface IWxMaApi {


    /**
     * 根据 jsCode 获取微信用户openId，session_key等
     * @param appId
     * @param appSecret
     * @return
     */
    Code2SessionResponse code2Session(String appId, String appSecret, String code);

    /**
     * 获取微信 访问令牌
     * <AUTHOR>
     * @param appId                 appId
     * @param appSecret             appSecret
     * @return
     */
    AccessTokenResponse getWxMaAccessToken(String appId, String appSecret);

    /**
     * 获取自定义小程序二维码   https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/getWXACodeUnlimit.html
     * <AUTHOR>
     * @param accessToken               访问令牌
     * @param scene                     自定义参数
     * @param page                      主页
     * @param width                     二维码的宽度，单位 px，最小 280px，最大 1280px
     * @param autoColor                 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false
     * @param isHyaline                  是否需要透明底色，为 true 时，生成透明底色的小程序
     * @return                          二维码内容
     */
    byte[] generateWxMinaQrcode(String accessToken, String scene, String page, Integer width, Integer autoColor,Integer isHyaline,String envVersion);

    /**
     * 获取自定义小程序二维码, 待个数限制的
     * <AUTHOR>
     * @param accessToken
     * @param page
     * @param width
     * @return
     */
    byte[] generateWxMinaQrcodeWithLimit(String accessToken,  String page, Integer width);


    /**
     * 发送小程序订阅消息
     * <AUTHOR>
     * @param   jsonParam             消息体
     * @param   accessToken           接口调用凭证
     * @return  Map           接口调用凭证
     */
    Map sendMinaTemplateMessage(String jsonParam, String accessToken);

    /**
     * 获取建字符串形式场景值的二维码ticket
     * @param accessToken   访问令牌
     * @param actionName   二维码类型，QR_STR_SCENE为临时的字符串参数值，QR_LIMIT_STR_SCENE为永久的字符串参数值
     * @param expireSeconds   该二维码有效时间，以秒为单位。 最大不超过2592000（即30天），此字段如果不填，则默认有效期为30秒。
     * @param sceneStr   场景值ID（字符串形式的ID），字符串类型，长度限制为1到64
     */
    QrCodeTicketResponse querySceneStrMpQrCodeTicket(String accessToken, Integer expireSeconds, String actionName, String sceneStr);


    /**
     * 发送公众号模板消息
     * <AUTHOR>
     * @param   jsonParam             消息体
     * @param   accessToken           接口调用凭证
     * @return  Map           接口调用凭证
     */
    Map sendwxMpTemplateMessage(String jsonParam, String accessToken);

    /**
     * 添加公众号模板
     * @param accessToken 接口调用凭证
     * @param templateIdShort 模板库中模板的编号，有“TM**”和“OPENTMTM**”等形式
     * @return  AddTemplateResponse
     */
    AddTemplateResponse  addMpTemplate(String accessToken,String templateIdShort);

    /**
     * 删除公众号模板消息
     * @param accessToken 接口调用凭证
     * @param templateId 模板id
     * @return BaseWxResponse
     */
    BaseWxResponse deleteMpTemplate(String accessToken,String templateId);

    /**
     * 通过code换取网页授权access_token
     * @param appId 公众号的唯一标识
     * @param appSecret 公众号的appsecret
     * @param code 填写第一步获取的code参数
     * @return MpOpenIdResponse
     */
    MpOpenIdResponse queryMpAccessTokenByCode(String appId,String appSecret,String code);

    /**
     * 文本安全内容检测接口   'https://api.weixin.qq.com/wxa/msg_sec_check?access_token=ACCESS_TOKEN'
     * <AUTHOR>
     * @param accessToken               访问令牌
     * @param content                     检测内容
     * @return                          二维码内容
     */
    BaseWxResponse textSafeContentDetection(String accessToken, String content);

    /**
     * 照片检测接口
     * @param accessToken
     * @param picture
     * @return
     */
    BaseWxResponse textSafePictureDetection(String accessToken, String picture);
    /**
     * 获取手机号
     * @param accessToken 公众号的唯一标识
     * @param code 公众号的appsecret
     * @return MpOpenIdResponse
     */
    UserPhoneResponse queryUserPhoneNumber(String accessToken, String code);

    /**
     * 获取分享链接
     *
     * @param accessToken 接口调用凭证
     * @param map         消息体
     * @return Map           接口调用凭证
     * <AUTHOR>
     */
    Map<?, ?> generateShareLink(String accessToken, Map<String, Object> map);

    /**
     * 获取分享链接
     *
     * @param accessToken 接口调用凭证
     * @param map         消息体
     * @return Map           接口调用凭证
     * <AUTHOR>
     */
    Map<?, ?> generateSchemeShareLink(String accessToken, Map<String, Object> map);

    /**
     * 获取分享链接
     *
     * @param accessToken 接口调用凭证
     * @param map         消息体
     * @return Map           接口调用凭证
     * <AUTHOR>
     */
    Map<?, ?> getLiveInfo(String accessToken, Map<String, Object> map);


    /**
     * 照片检测接口(异步)
     * @param accessToken
     * @param picture
     * @return
     */
    MediaCheckAsyncResponse mediaCheckAsync(String accessToken,String openId, String picture);

    /**
     * 发送客服消息
     * @param accessToken  微信toekn
     * @param openId    接受消息openId
     * @param messageType   消息类型   text(文本)/image(图片)/link(链接)/miniprogrampage(小程序卡片)
     * @param data   data 参数根据type不同类型传入不同格式参数
     *              "text": {"content": "链接www.baidu.com"},
     *              "image": {"media_id": "媒体Id"},
     *              "link": {"title": "标题",
     *                      "description": "描述",
     *                      "url": "链接 www.baidu.com",
     *                      "thumb_url": "图文链接消息的图片链接，支持 JPG、PNG 格式，较好的效果为大图 640 X 320，小图 80 X 80"},
     *              "miniprogrampage": {
     *                      "title": "消息标题",
     *                      "pagepath": "小程序的页面路径，跟app.json对齐，支持参数，比如pages/index/index?foo=bar",
     *                       "thumb_media_id": "媒体id'"}
     */
    void messageCustomSend(String accessToken, String openId,String messageType, Map<String, Object> data);
}