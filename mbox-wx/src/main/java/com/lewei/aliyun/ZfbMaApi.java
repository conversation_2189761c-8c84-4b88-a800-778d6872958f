package com.lewei.aliyun;

import com.alipay.v3.ApiClient;
import com.alipay.v3.Configuration;
import com.alipay.v3.api.AlipaySystemOauthApi;
import com.alipay.v3.model.AlipaySystemOauthTokenModel;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@Service
public class ZfbMaApi  implements  IZfbMaApi{


    @Override
    public Map getOauthToken(String appId , String authorizationCode) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();

        // 初始化alipay参数（全局设置一次）
        defaultClient.setAlipayConfig(getAlipayConfig());

        // 构造请求参数以调用接口
//        AlipaySystemOauthApi api = new AlipaySystemOauthApi();
        AlipaySystemOauthTokenModel data = new AlipaySystemOauthTokenModel();

        // 设置刷新令牌
        data.setRefreshToken("201208134b203fe6c11548bcabd8da5bb087a83b");

        // 设置授权码
        data.setCode("4b203fe6c11548bcabd8da5bb087a83b");

        // 设置授权方式
        data.setGrantType("authorization_code");


        try {
            AlipaySystemOauthTokenResponseModel response = api.token(data);
        } catch (ApiException e) {
            AlipaySystemOauthTokenDefaultResponse errorObject = (AlipaySystemOauthTokenDefaultResponse) e.getErrorObject();
            System.out.println("调用失败:" + errorObject);
        }

        return Collections.emptyMap();
    }


}
