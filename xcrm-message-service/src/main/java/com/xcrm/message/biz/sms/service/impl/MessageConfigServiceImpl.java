package com.xcrm.message.biz.sms.service.impl;

import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import com.xcrm.message.WxErrorDef;
import com.xcrm.message.biz.sms.service.IMessageConfigService;
import com.xcrm.message.biz.wx.service.IWxMessageService;
import com.xcrm.message.biz.wx.vo.AddTemplateVo;
import com.xcrm.platform.common.message.dto.MessageConfigDTO;
import com.xcrm.platform.common.message.request.MessageConfigRequest;
import com.xcrm.platform.common.message.vo.MessageConfigVo;
import com.xcrm.platform.common.message.vo.MessageOpenVo;
import com.xcrm.platform.common.message.vo.WxMessageTmplVo;
import com.xcrm.platform.entity.message.MessageConfig;
import com.xcrm.platform.entity.message.SysMessageTmpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/20
 */
@Service
@Transactional
@Slf4j
public class MessageConfigServiceImpl implements IMessageConfigService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IWxMessageService wxMessageService;

    @Override
    public MessageConfigDTO queryMessageConfigDTO(String tmplEvent) {
        Ssqb queryMessageConfigDTO = Ssqb.create("com.xcrm.message.sms.config.queryMessageConfigDTO")
                .setParam("tmplEvent",tmplEvent)
                .setParam("lang", XcrmThreadContext.getLocale())
                ;
        return dao.findForObj(queryMessageConfigDTO,MessageConfigDTO.class);

    }

    @Override
    public List<MessageConfigVo> queryMessageConfigVo(String client) {
        Ssqb queryMessageConfigVo = Ssqb.create("com.xcrm.message.sms.config.queryMessageConfigVo")
                .setParam("lang", XcrmThreadContext.getLocale())
                .setParam("client",client)
                ;
        return dao.findForList(queryMessageConfigVo,MessageConfigVo.class);    }

    @Override
    public Long saveMessageConfig(MessageConfigRequest request) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("sysTmplId",request.getSysTmplId()));
        MessageConfig config = dao.query(query,MessageConfig.class);
        if (config != null) {
            BeanUtils.copyProperties(request,config);
            config.setUpdated(DateFormatUtils.getNow());
            dao.update(config);
        }else {
            config = new MessageConfig();
            BeanUtils.copyProperties(request,config);
            config.setCreated(DateFormatUtils.getNow());
            dao.save(config);
        }
        //配置微信消息模板
        if (BooleanUtils.isTrue(request.getIsWxOpen())){

            QueryBuilder querySysTmpl = QueryBuilder.where(Restrictions.eq("id",request.getSysTmplId()));
            SysMessageTmpl sysMessageTmpl = dao.query(querySysTmpl,SysMessageTmpl.class);
            if (sysMessageTmpl == null){
               throw new NotFoundException(BizMessageSource.getInstance().getMessage(WxErrorDef.WX_TMPL_NOT_EXIST));
            }
            if (BooleanUtils.isNotTrue(request.getIsUseSysTmpl())){
                //查询是否已经配置
                if (StringUtils.isEmpty(request.getWxAppId() ) || StringUtils.isEmpty(request.getWxAccessToken())){
                    throw new BizCoreRuntimeException(WxErrorDef.MA_NOT_AUTH);
                }
                //如果没有配置模板  添加模板
                if (!(request.getWxAppId().equals(config.getWxAppId()) && StringUtils.isNotEmpty(config.getTmplId())  && StringUtils.isNotEmpty(config.getDataString()))){

                    AddTemplateVo addTemplateVo =  wxMessageService.addWxMessageTmpl(request.getWxAppId(),request.getWxAccessToken(),sysMessageTmpl.getTmplEvent(),sysMessageTmpl.getTmplName());
                    config.setWxAppId(request.getWxAppId());
                    config.setTmplId(addTemplateVo.getTemplateId());
                    config.setDataString(addTemplateVo.getKeywordStr());
                    dao.update(config);
                }
            }else {
                config.setWxAppId(sysMessageTmpl.getAppId());
                config.setTmplId(sysMessageTmpl.getTmplId());
                config.setDataString(sysMessageTmpl.getDataString());
                dao.update(config);
            }

        }

        return config.getId();
    }



    @Override
    public List<WxMessageTmplVo> queryWxMessageTmpl(String tmplEvents, String appId) {
        List<String> tmplEventList = Arrays.asList(tmplEvents.split(","));

        Ssqb queryWxMessageTmpl = Ssqb.create("com.xcrm.message.sms.config.queryWxMessageTmpls")
                .setParam("appId",appId)
                .setParam("tmplEvents",tmplEventList)
                ;
        return dao.findForList(queryWxMessageTmpl,WxMessageTmplVo.class);      }

    @Override
    public List<MessageOpenVo> queryMessageOpen(String tmplEvents) {
        List<String> tmplEventList = Arrays.asList(tmplEvents.split(","));

        Ssqb queryWxMessageTmpl = Ssqb.create("com.xcrm.message.sms.config.queryMessageOpen")
                .setParam("tmplEvents",tmplEventList)
                ;
        return dao.findForList(queryWxMessageTmpl,MessageOpenVo.class);
    }
}
