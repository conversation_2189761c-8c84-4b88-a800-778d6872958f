<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.ma.box.different.space">


    <select id="queryDifferentSpace" resultType="com.lewei.eshop.common.vo.different.MaDifferentSpaceVo">
        SELECT ds.id,
               ds.title,
               ds.boxSpuId,
               ds.clearCategoryId,
               ds.progressNum,
               IFNULL(dsm.quantity,0) quantity
        FROM t_t_different_space ds
        LEFT JOIN t_t_different_space_member dsm ON ds.id = dsm.differentSpaceId AND dsm.memberId = #{memberId}
        WHERE ds.chainId = #{chainId}
          AND ds.dataStatus = 1
          AND ds.boxSpuId = #{spuId}
        AND ds.status = 'S_DSS_UP'
    </select>

    <update id="addDifferentSpaceMember">
        INSERT INTO t_t_different_space_member (`id`, `chainId`, `differentSpaceId`,`memberId` , `quantity`, `created` , `dataStatus`)
        VALUES (#{id}, #{chainId}, #{differentSpaceId}, #{memberId} , #{num} ,`created`,dataStatus)
        ON duplicate KEY UPDATE quantity  = quantity + #{num},updated = NOW()
    </update>



</mapper>