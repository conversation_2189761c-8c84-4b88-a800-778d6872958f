package com.lewei.ma.box;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.OrderUtil;
import com.lewei.eshop.common.data.TimeTypeEnum;
import com.lewei.eshop.common.data.coupon.MemberCouponStatusEnum;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.data.product.ProductEnum;
import com.lewei.eshop.common.request.activity.ActivityGIftReq;
import com.lewei.eshop.common.request.app.BoxLimitTimeDelayReq;
import com.lewei.eshop.common.request.app.BoxMineDelayReq;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.member.MemberSweetProfitRequest;
import com.lewei.eshop.common.request.member.share.MemberShareCommissionReq;
import com.lewei.eshop.common.request.order.OrderTransPreProfitRequest;
import com.lewei.eshop.common.request.order.TradeFlowReq;
import com.lewei.eshop.common.request.taoBao.MemberTbTicketRequest;
import com.lewei.eshop.common.request.treasure.MemberBoxTicketReq;
import com.lewei.eshop.common.vo.member.AddFractionRequest;
import com.lewei.eshop.common.vo.member.RevertScoreRequest;
import com.lewei.eshop.common.vo.message.MessageCustomConfigVo;
import com.lewei.eshop.common.vo.product.box.BoxMineRecordVo;
import com.lewei.eshop.common.vo.product.box.BoxTbTicketVo;
import com.lewei.eshop.entity.activity.types.ActivityTypeEnum;
import com.lewei.eshop.entity.app.ApplicationConfig;
import com.lewei.eshop.entity.app.coupon.MemberCoupon;
import com.lewei.eshop.entity.app.sweet.SweetConfigLog;
import com.lewei.eshop.entity.app.sweet.SweetRecord;
import com.lewei.eshop.entity.app.sweet.types.SweetConfigTypeEnum;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberMaRef;
import com.lewei.eshop.entity.member.MemberPictureFrame;
import com.lewei.eshop.entity.member.MemberReward;
import com.lewei.eshop.entity.member.types.*;
import com.lewei.eshop.entity.newcomer.NewcomerActivityBuyRecord;
import com.lewei.eshop.entity.order.Order;
import com.lewei.eshop.entity.order.OrderBoxItem;
import com.lewei.eshop.entity.order.types.OrderBizTypeEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.entity.product.ProductBoxItemRewardTmpl;
import com.lewei.eshop.entity.product.ProductBoxUpItemRewardTmpl;
import com.lewei.eshop.entity.product.box.ProductBox;
import com.lewei.eshop.entity.product.box.ProductBoxItem;
import com.lewei.eshop.entity.product.box.ProductBoxMine;
import com.lewei.eshop.entity.product.box.ProductBoxMineRecord;
import com.lewei.eshop.entity.product.box.types.*;
import com.lewei.eshop.entity.product.types.BoxItemSaleStatusEnum;
import com.lewei.eshop.entity.product.types.BoxRewardRuleEnum;
import com.lewei.eshop.entity.product.types.RewardTypeEnum;
import com.lewei.eshop.entity.trade.types.TradeTypeEnum;
import com.lewei.eshop.entity.trade.types.TradeTypeFactory;
import com.lewei.eshop.entity.wx.AccessToken;
import com.lewei.eshop.message.mina.entity.*;
import com.lewei.eshop.message.mina.service.IMaMessageService;
import com.lewei.eshop.message.mina.service.IWxAccessTokenService;
import com.lewei.global.IGlobalHandler;
import com.lewei.log.trace.MDCTraceUtils;
import com.lewei.ma.box.client.AppFeignClient;
import com.lewei.ma.box.client.MemberFeignClient;
import com.lewei.ma.box.client.paas.MessageFeignClient;
import com.lewei.ma.box.client.paas.request.AppMsgRequest;
import com.lewei.ma.box.client.paas.request.SmsRequest;
import com.lewei.ma.box.client.paas.request.WxMsgRequest;
import com.lewei.ma.box.event.*;
import com.lewei.ma.box.service.IMaProductBoxService;
import com.lewei.ma.box.service.ISweetConfigService;
import com.lewei.ma.box.vo.*;
import com.lewei.search.client.entity.OrderBoxItemEsEntity;
import com.lewei.search.client.service.ICommonEsProvider;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.DateZonetimeUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.StringUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.db.saas.IIdWorker;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**、
 * 购买盲盒事件处理
 * <AUTHOR>
 * @since 2021/03/26
 */
@Component
@Transactional
public class BuyBoxEventListener {

    private static final Logger log = LoggerFactory.getLogger("BOX_LOG");

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IMaProductBoxService boxService;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private RedisCacheProvider redisCacheProvider;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private MessageFeignClient messageFeignClient;
    @Autowired
    private AppFeignClient appFeignClient;
    @DubboReference(check = false, version = "1.0.0")
    private ICommonEsProvider commonEsProvider;
    @Autowired
    private IIdWorker idWorker;
    @Autowired
    private IWxAccessTokenService accessTokenService;
    @Autowired
    private BoxConfig boxConfig;
    @Autowired
    private ISweetConfigService sweetConfigService;
    @Autowired
    private IGlobalHandler globalHandler;
    @Autowired
    private IMaMessageService messageService;



    ThreadLocal<Order> orderLocal = new ThreadLocal<>();


    ThreadLocal<Map<MemberBoxVo,List<MaBoxItemRewardVo>>> rewardLocalList = new ThreadLocal<>();

    private final String   BOX_COUNTER = "BOX_COUNTER::";



    /**
     * 购买盲盒处理
     * @param event
     */
    @EventListener(condition = "event.order.orderType eq 'S_OOT_BOX' or event.order.orderType eq 'S_OOT_BOX_CARD'")
    public void handleBuyBoxSuccessListen(BuyBoxSuccessEvent event){
        //暂时用 synchronized 保证单节点 高并发情况安全 多########################synchronized节点部署需要用redis 分布式锁
        JSONObject extJson = event.getOrderExtJson();
        String  boxType = extJson.getString("boxType");
        long begin = System.currentTimeMillis();
        if (ProductBoxTypeEnum.S_BT_LIMIT.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {}, cost {} ms ====> handleBuyBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        } else if (ProductBoxTypeEnum.S_BT_UN_LIMIT.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyUnLimitBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyUnLimitBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {} , cost {} ms ====> handleBuyUnLimitBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        }else if (ProductBoxTypeEnum.S_BT_CARD.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyCardBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyCardBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {} , cost {} ms ====> handleBuyCardBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        }else if (ProductBoxTypeEnum.S_BT_LIMIT_NEW.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyNewBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyNewBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {} , cost {} ms ====> handleBuyNewBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        }else if (ProductBoxTypeEnum.S_BT_CONCATENATE.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyConcatenateBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyConcatenateBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {} , cost {} ms ====> handleBuyConcatenateBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        }else if (ProductBoxTypeEnum.S_BT_ADVENTURE.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyAdventureBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyAdventureBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {} , cost {} ms ====> handleBuyAdventureBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        }else if(ProductBoxTypeEnum.S_BT_UP.value().equals(boxType)){
            log.debug("【开始执行】线程：{}, orderId {} ====> handleBuyUpBoxSuccess", Thread.currentThread().getName(), event.getOrder().getId());
            this.handleBuyUpBoxSuccess(event);
            log.debug("【结束执行】线程：{}, orderId {} , cost {} ms ====> handleBuyUpBoxSuccess"
                    , Thread.currentThread().getName(), event.getOrder().getId(), System.currentTimeMillis() - begin);
        }
        else {
            log.error("盲盒类型未匹配, orderId {} ",event.getOrder().getId());
        }
        //删除支付队列
        redisCacheProvider.delWith(MaCacheKeys.BOX_ITEM_PAY_QUEUE_KEY + extJson.getLong("boxItemId") + "@" + event.getOrder().getMemberId());

    }


    private void handleBuyBoxSuccess(BuyBoxSuccessEvent event){
        Random random = new Random();
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);
        rewardLocalList.set(new ConcurrentHashMap<>());

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        int redisCount = 0;
        JSONObject extJson = event.getOrderExtJson();
        Long  boxItemId = extJson.getLong("boxItemId");
        Integer  num = extJson.getInteger("num");
        Long spuId = extJson.getLong("spuId");



        try {


            Boolean  isAllBuy = extJson.getBoolean("isAllBuy");
            BigDecimal unitPrice = extJson.getBigDecimal("unitPrice");
            String boxType = extJson.getString("boxType");
            Long categoryId = extJson.getLong("categoryId");
            Date now = DateFormatUtils.getNow();
            Long skuId = extJson.getLong("skuId");
            Long newcomerRuleId = extJson.getLong("newcomerRuleId");

            MaBoxItemDetailVo boxDetailVo = boxService.queryBoxItemDetailVo(boxItemId);

            boolean isOddsMode = BoxPrizeModeEnum.S_BPM_ODDS.value().equals(boxDetailVo.getBoxPrizeMode());
            List<MaBoxItemRewardVo> boxItemRewardVos = null;
            List<Double> rewardOddsList = new ArrayList<>();
            List<Double> sortRewardOddsList = new ArrayList<>() ;

            if (isOddsMode){
                 boxItemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId,false);
                 Double sumOdds = boxItemRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();

                Double tempOdds = 0d;
                for (MaBoxItemRewardVo itemRewardVo : boxItemRewardVos) {
                    tempOdds += itemRewardVo.getOdds();
                    rewardOddsList.add(tempOdds / sumOdds);
                }
            }

            Map<Long,MaBoxItemRewardVo> boxItemRewardVoMap = this.queryBoxItemRewardMap(boxItemId);

            MemberBoxVo member =  this.queryMember(order.getMemberId());

            List<BoxStageVo> stageVos = new ArrayList<>();
            if (!StringUtil.isEmpty(boxDetailVo.getStageJson())){
                stageVos = JSON.parseArray(boxDetailVo.getStageJson(),BoxStageVo.class);
            }
            //每个阶段奖品数

            List<BoxStageVo> stageVoItem = null ;

            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(BOX_COUNTER+boxItemId);
            if (currentRewardNum == null || currentRewardNum == 0) {
                currentRewardNum = getCurrentRewardNum(boxDetailVo, order.getId());
                redisCacheProvider.getRedisTemplate().opsForValue().set(BOX_COUNTER+boxItemId,currentRewardNum);
            }
            if (currentRewardNum == 0){
                applicationEventPublisher.publishEvent(new AutoBuyBoxEvent(this,boxDetailVo.getSpuId(),skuId,boxItemId,order.getChainId(),MDCTraceUtils.getTraceId()));
            }
            log.info("currentRewardNum是"+ currentRewardNum);
            //进入序号位置

            List<Long >  winRewardIds  = new ArrayList<>();


            int leftTotalRewardNum = boxDetailVo.getRewardNum() - currentRewardNum + 1;
            log.info("leftTotalRewardNum是"+ leftTotalRewardNum);
            if (leftTotalRewardNum >0){
                for (int i = 0; i < num; i++) {
                    if (currentRewardNum >= boxDetailVo.getRewardNum()){
                        log.debug("【结束抽奖循环】：当前序号{}, boxItemId {}", currentRewardNum, boxItemId);
                        break;
                    }
                    currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(BOX_COUNTER+boxItemId).intValue();
                    redisCount++;
                    //重置阶段为空
                    stageVoItem = new ArrayList<>();
                    //剩余奖品数总和
                    int leftRewardNum = boxDetailVo.getRewardNum() - currentRewardNum + 1;
                    //判断当前处于的阶段
                    if (ListUtil.isNotEmpty(stageVos)){
                        for (BoxStageVo vo : stageVos) {
                            if (currentRewardNum>= vo.getBgStage() && currentRewardNum<= vo.getEndStage()){
                                stageVoItem.add(vo) ;
                            }
                        }
                    }

                    log.debug("【执行】线程：{}, orderId {} ====> 当前抽奖位置={},抽奖次数={},抽奖总次数={},会员id={},订单Id{},阶段={}",
                            Thread.currentThread().getName(), order.getId(),currentRewardNum,i+1,num,order.getMemberId(),order.getId(),stageVoItem);

                    if (isOddsMode){
                        //随机数在哪个概率区间内，则是哪个奖品
                        double randomDouble = Math.random();
                        sortRewardOddsList.addAll(rewardOddsList);
                        //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                        sortRewardOddsList.add(randomDouble);
                        Collections.sort(sortRewardOddsList);
                        int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);
                        MaBoxItemRewardVo  rewardVo = new MaBoxItemRewardVo();
                        //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
                        BeanUtils.copyProperties(boxItemRewardVos.get(lotteryIndex), rewardVo);
                        winRewardIds.add(boxItemRewardVos.get(lotteryIndex).getRewardId());
                        sortRewardOddsList.clear();

                        //更新剩余数量
                        int l  = boxService.updateBoxItemLeftRewardNum(boxItemId,1);
                        if ( l < 1){
                            log.error("handleBuyBoxSuccessListen.updateBoxItemLeftRewardNum fail (orderId = {})",orderLocal.get().getId());
                        }

                    }else {
                        //全随机
                        if (ListUtil.isEmpty(stageVoItem)) {
                            handleAllRandReward(random, boxItemId, winRewardIds,member.getId(),i);

                        }else {
                            boolean isWin = false;
                            //多个极端按照 endStage 排序 先结束的 先处理阶段奖
                            stageVoItem = stageVoItem.stream().sorted(Comparator.comparing(BoxStageVo::getEndStage)).collect(Collectors.toList());

                            List<MaBoxRewardRuleVo> allStageRules= this.queryLeftProductBoxRules(boxItemId,null,null, null,null);
                            Map<Long,List<MaBoxRewardRuleVo>> stageRuleMap = null;
                            if (ListUtil.isNotEmpty(allStageRules)){
                                stageRuleMap = allStageRules.stream().collect(Collectors.groupingBy(MaBoxRewardRuleVo::getStageId));
                            }

                            if (stageRuleMap != null){
                                for (BoxStageVo stageVo : stageVoItem) {
                                    if (isWin){
                                        break;
                                    }
                                    //阶段规则中奖品组装
                                    List<MaBoxRewardRuleVo> stageRules= stageRuleMap.get(stageVo.getStageId());
                                    if (ListUtil.isEmpty(stageRules)){
                                        continue;
                                    }

                                    //阶段剩余奖品总数
                                    int stageLeftReward = stageVo.getEndStage() - currentRewardNum + 1;
                                    //处理奖品逻辑
                                    List<MaBoxRewardRuleVo> randomRewards =  stageRules.stream().filter(a-> BooleanUtils.isFalse(a.getIsGift()) && Objects.equals(BoxRewardRuleEnum.S_PBRR_RANDOM.value(),a.getRuleType())).collect(Collectors.toList());
                                    List<MaBoxRewardRuleVo> lastRewards =  stageRules.stream().filter(a-> BooleanUtils.isFalse(a.getIsGift()) && Objects.equals(BoxRewardRuleEnum.S_PBRR_LAST.value(),a.getRuleType())).collect(Collectors.toList());
                                    //首先判断是否符合last奖条件 last奖个数 等于 阶段剩余奖个数
                                    int lastRewardNum = lastRewards.stream().mapToInt(MaBoxRewardRuleVo::getLeftQuantity).sum();
                                    if (ListUtil.isNotEmpty(lastRewards) && lastRewardNum ==  stageLeftReward){
                                        int randomNum = random.nextInt(lastRewardNum);
                                        int item = 0;
                                        for (MaBoxRewardRuleVo lastReward : lastRewards) {

                                            item += lastReward.getLeftQuantity();
                                            if (randomNum < item){
                                                this.updateReward(lastReward.getId(),lastReward.getRewardId(),1,winRewardIds);
                                                isWin = true;
                                                break;
                                            }
                                        }
                                    }else if (ListUtil.isNotEmpty(randomRewards)){

                                        //阶段奖品不为空 组装全随机奖池和 规则随机奖池
                                        int stageRandomRewardNum = randomRewards.stream().mapToInt(MaBoxRewardRuleVo::getLeftQuantity).sum();
                                        //抽到阶段随机奖品的概率为 stageRandomRewardNum/ stageLeftReward  （阶段剩余奖品/阶段随机奖品）
                                        int randomNum = random.nextInt(stageLeftReward - lastRewardNum);
                                        int item = 0;
                                        MaBoxRewardRuleVo stageRandomReward = null;
                                        for (MaBoxRewardRuleVo randomReward : randomRewards) {
                                            item += randomReward.getLeftQuantity();
                                            if (randomNum < item){
                                                stageRandomReward = randomReward;
                                                isWin = true;
                                                break;
                                            }
                                        }
                                        //如果不为空，则抽到阶段随机奖品
                                        if (stageRandomReward != null) {
                                            this.updateReward(stageRandomReward.getId(),stageRandomReward.getRewardId(),1,winRewardIds);
                                        }

                                        //如果阶段随机奖品为空  则全随机抽奖
//                                if (ListUtil.isEmpty(randomRewards)){
//                                    handleAllRandReward(random, boxItemId, winRewardIds);
//                                    isWin = true;
//                                }else {
//                                    //阶段奖品不为空 组装全随机奖池和 规则随机奖池
//                                    int stageRandomRewardNum = randomRewards.stream().mapToInt(MaBoxRewardRuleVo::getLeftQuantity).sum();
//                                    //抽到阶段随机奖品的概率为 stageRandomRewardNum/ stageLeftReward  （阶段剩余奖品/阶段随机奖品）
//                                    int randomNum = random.nextInt(stageLeftReward - lastRewardNum);
//                                    int item = 0;
//                                    MaBoxRewardRuleVo stageRandomReward = null;
//                                    for (MaBoxRewardRuleVo randomReward : randomRewards) {
//                                        item += randomReward.getLeftQuantity();
//                                        if (randomNum < item){
//                                            stageRandomReward = randomReward;
//                                            isWin = true;
//                                            break;
//                                        }
//                                    }
//                                    //如果不为空，则抽到阶段随机奖品
//                                    if (stageRandomReward != null) {
//                                        this.updateReward(stageRandomReward.getId(),stageRandomReward.getRewardId(),1,winRewardIds);
//                                    }
//                                }
                                    }
                                }
                            }


                            if (!isWin){
                                //如果为空，则没有抽到阶段随机奖品，此时全随机奖池抽奖
                                this.handleAllRandReward(random, boxItemId, winRewardIds,member.getId(),i);

                            }
                        }
                    }

                    this.saveMemberReward(member,order.getId(),boxItemId,now,winRewardIds.get(winRewardIds.size()-1),currentRewardNum,boxItemRewardVoMap);
                    stageGift(boxItemId, stageVoItem, currentRewardNum,boxDetailVo.getRewardNum(),order.getChainId(),order.getId(),now , boxItemRewardVoMap);
                }
            }

            //   List<MaBoxItemRewardVo> rewards = saveMemberRewards(member,order.getId(), boxItemId, now, stageVo, winRewardIds,stageIds,loc);

            //全部奖品剩余为零，更新套盒状态
            if (currentRewardNum >= boxDetailVo.getRewardNum()){
                //处理 全局赏

                List<MaBoxRewardRuleVo> gifts = this.queryLeftProductBoxRules(boxItemId,0L,true, null,null);

                if (ListUtil.isNotEmpty(gifts)){

                    Map<Long,List<Long>> mapping = new HashMap<>();

                    List<MaBoxRewardRuleVo> randomGifts = gifts.stream().filter(a->Objects.equals(a.getRuleType(),BoxRewardRuleEnum.S_PBRR_RANDOM.value())).collect(Collectors.toList());
                    //全局赏
                    if (ListUtil.isNotEmpty(randomGifts)){
                        //查询盒子所有抽奖会员id
                        List<LotteryMemberVo> dblotteryMemberVos = this.queryStageRewardMemberIds(boxItemId);

                        List<LotteryMemberVo> lotteryMemberVos = new ArrayList<>(dblotteryMemberVos);


                        for (MaBoxRewardRuleVo randomGift : randomGifts) {
                            for (int i = 0; i < randomGift.getLeftQuantity(); i++) {
                                int item = random.nextInt(lotteryMemberVos.size());
                                //多个全局赏有问题 暂时不考虑
                                if (randomGift.getBgStage() != null && randomGift.getEndStage() != null ){
                                    item = random.nextInt(randomGift.getEndStage() - randomGift.getBgStage() + 1) + randomGift.getBgStage() - 1;
                                }
                                List<Long> rewardIds = new ArrayList<>();
                                rewardIds.add(randomGift.getRewardId());
                                handleMemberRewardMap(mapping,lotteryMemberVos.get(item).getMemberId(), rewardIds);
                                // memberIds.remove(item);
                                MemberBoxVo memberBoxVo = this.queryMember(lotteryMemberVos.get(item).getMemberId());

                                this.saveMemberReward(memberBoxVo,0L,boxItemId,now,randomGift.getRewardId(),lotteryMemberVos.get(item).getSerialNum(),boxItemRewardVoMap);

                            }
                            this.updateReward(randomGift.getId(),randomGift.getRewardId(),randomGift.getLeftQuantity(),new ArrayList<>());
                        }
                    }

                    handelOtherGitfs(boxItemId, boxItemRewardVoMap, gifts, mapping,null,null);


                    handelAfterGift( XcrmThreadContext.getChainId(), mapping,boxItemRewardVoMap);

                }
                //抽奖赠送处理
                if (currentRewardNum.equals(boxDetailVo.getRewardNum()) && winRewardIds.size() > 0){
                    applicationEventPublisher.publishEvent(new RewardGiftEvent(this,0L,boxItemId,0L,order.getChainId(),spuId,null));
                }

                ProductBoxItem boxItem = new ProductBoxItem();
                boxItem.setId(boxItemId);
                boxItem.setSaleStatus(BoxItemSaleStatusEnum.S_PBSS_SOLD_OUT.value());
                boxItem.setUpdated(now);
                boxItem.setEmptyTime(now);
                dao.update(boxItem);

                redisCacheProvider.getRedisTemplate().delete(BOX_COUNTER+boxItemId);

                applicationEventPublisher.publishEvent(new MaAutoShelfEmptyEvent(this,boxDetailVo.getSpuId(),order.getChainId(),order.getId()));



            }

            int refundNum = num - winRewardIds.size();
            BigDecimal refundFee = handelRefund(order, num, refundNum,spuId, null, null);

            if (BooleanUtils.isTrue(boxDetailVo.getIsProduceFraction())){
                //会员积分处理
                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
            }

            this.handleTradeFlow(order,refundFee);
            if (ListUtil.isNotEmpty(winRewardIds)){

                List<MaBoxItemRewardVo> rewards = getWinRewards(boxItemRewardVoMap, winRewardIds);


                if (!OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(order.getPaymentMethod())){
                    applicationEventPublisher.publishEvent(new RewardScoreEvent(this,order.getMemberId(),order.getChainId(),order.getOrderTitle(),rewards));
                }

//                try {
//                    List<MaBoxItemRewardVo> giftRewards = rewards.stream().filter(a-> BooleanUtils.isTrue(a.getIsGift())).collect(Collectors.toList());
//                    if (ListUtil.isNotEmpty(giftRewards)){
//                        sendSMS(member,giftRewards);
//                    }
//                } catch (Exception e) {
//                    log.error("send reward sms fail:{}",e.getMessage());
//                    e.printStackTrace();
//                }

                this.saveNewcomerBuyRecord(member.getId(), spuId,boxItemId,newcomerRuleId);
                //分销处理

                handleDist(order);

                applicationEventPublisher.publishEvent(new MaAutoShelfTradeEvent(this,boxDetailVo.getSpuId(),order.getChainId(),order.getId()));

                //抽奖赠送处理
                applicationEventPublisher.publishEvent(new RewardGiftEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId()));
                //藏宝阁
                applicationEventPublisher.publishEvent(new TreasureEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),winRewardIds.size()));


                if (OrderUtil.isMoneyPay(order.getPaymentMethod())){
                    //明信片处理
                    applicationEventPublisher.publishEvent(new PostcardEvent(this,order.getMemberId(),order.getChainId(),winRewardIds.size(),boxDetailVo.getPriceFee(),order.getPaymentMethod()));
                    //宝箱活动处理
                    List<Long> orderIds = new ArrayList<Long>();
                    orderIds.add(order.getId());
                    applicationEventPublisher.publishEvent(new TreasureActivityEvent(this,order.getMemberId(),order.getChainId(),orderIds));

                }


                //弹幕/分润
                List<MaBoxItemRewardVo> rewardVoList = new ArrayList<>();
                rewardLocalList.get().forEach((k,v)->{
                    applicationEventPublisher.publishEvent(new BulletChatEvent(this,v,k,order.getChainId(),boxType,boxItemId,spuId));
                    rewardVoList.addAll(v);

                });
                //会员每日成本
                applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,rewardLocalList.get(),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod(),boxType,order.getOrderTitle(),order.getOrderMoney()));
                //IP分润
                applicationEventPublisher.publishEvent(new MemberServiceEvent(order.getId(),order.getMemberId(),this,rewardVoList,order.getChainId(),MDCTraceUtils.getTraceId()));
                //处理盲盒图鉴
                applicationEventPublisher.publishEvent(new BoxBookEvent(this,rewardLocalList.get(),spuId,order.getChainId(),MDCTraceUtils.getTraceId()));


            }

            applicationEventPublisher.publishEvent(new AutoAddBoxItemEvent(this,boxDetailVo.getSpuId(),order.getChainId(),MDCTraceUtils.getTraceId()));



        } catch (Exception e) {
            //退款到余额
            try {
                failHandel(order,boxItemId,spuId,null,num);
            } catch (Exception ex) {
                log.error("盲盒支付异常，退还到余额 失败 orderId = "+order.getId()+",boxItemId = " +boxItemId, ex);
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId,redisCount);
            log.error("盲盒抽奖报错 ",e);
            throw e;
        } finally {
            orderLocal.remove();
            rewardLocalList.remove();
            XcrmThreadContext.removeAccessType();
            XcrmThreadContext.removeChainId();
        }
    }

    private void handelOtherGitfs(Long boxItemId, Map<Long, MaBoxItemRewardVo> boxItemRewardVoMap, List<MaBoxRewardRuleVo> gifts, Map<Long, List<Long>> mapping,Integer bgStage,Integer endStage) {
        MemberRuleVo maxCountMember = null;
        MemberRuleVo maxProfitMember = null;
        MemberRuleVo maxLossMember = null;
        //发数最多 若出现两个玩家购买次数相同且为最大，可根据最终抽赏时间为准，谁先抽谁获得该赏品
        List<MaBoxRewardRuleVo> maxCountGifts = gifts.stream().filter(a->Objects.equals(a.getRuleType(),BoxRewardRuleEnum.S_PBRR_MAX_COUNT.value())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(maxCountGifts)){
            //查询抽奖数量最多用户id
            Ssqb queryMaxCountMemberId = Ssqb.create("com.lewei.ma.box.queryMaxCountMemberId")
                    .setParam("boxItemId", boxItemId)
                    .setParam("bgStage", bgStage)
                    .setParam("endStage", endStage)
                    ;
            maxCountMember= dao.findForObj(queryMaxCountMemberId,MemberRuleVo.class);
        }

        List<MaBoxRewardRuleVo> maxProfitGifts = gifts.stream().filter(a->Objects.equals(a.getRuleType(),BoxRewardRuleEnum.S_PBRR_MAX_PROFIT.value())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(maxProfitGifts)){
            //查询抽奖盈利最多用户id
            Ssqb queryMaxProfitMemberId = Ssqb.create("com.lewei.ma.box.queryMaxProfitMemberId")
                    .setParam("boxItemId", boxItemId)
                    .setParam("bgStage", bgStage)
                    .setParam("endStage", endStage)
                    ;
            maxProfitMember = dao.findForObj(queryMaxProfitMemberId,MemberRuleVo.class);

        }

        List<MaBoxRewardRuleVo> maxLossGifts = gifts.stream().filter(a->Objects.equals(a.getRuleType(),BoxRewardRuleEnum.S_PBRR_MAX_LOSS.value())).collect(Collectors.toList());
        if (ListUtil.isNotEmpty(maxLossGifts)){
            //查询抽奖亏损最多用户id
            Ssqb queryMaxLossMemberId = Ssqb.create("com.lewei.ma.box.queryMaxLossMemberId")
                    .setParam("boxItemId", boxItemId)
                    .setParam("bgStage", bgStage)
                    .setParam("endStage", endStage)
                    ;
            maxLossMember = dao.findForObj(queryMaxLossMemberId,MemberRuleVo.class);
        }
        if (maxCountMember != null){
            handelOtherGifts(boxItemId, boxItemRewardVoMap, mapping, maxCountGifts, maxCountMember);
        }
        if (maxProfitMember != null){
            handelOtherGifts(boxItemId, boxItemRewardVoMap, mapping, maxProfitGifts, maxProfitMember);
        }
        if (maxLossMember != null){
            handelOtherGifts(boxItemId, boxItemRewardVoMap, mapping, maxLossGifts, maxLossMember);
        }
    }

    private void handelOtherGifts(Long boxItemId,  Map<Long, MaBoxItemRewardVo> boxItemRewardVoMap, Map<Long, List<Long>> mapping, List<MaBoxRewardRuleVo> maxCountGifts, MemberRuleVo memberRuleVo) {
        Date now = DateFormatUtils.getNow();
        for (MaBoxRewardRuleVo randomGift : maxCountGifts) {
            for (int i = 0; i < randomGift.getLeftQuantity(); i++) {
                List<Long> rewardIds = new ArrayList<>();
                rewardIds.add(randomGift.getRewardId());
                handleMemberRewardMap(mapping, memberRuleVo.getMemberId(), rewardIds);
                MemberBoxVo memberBoxVo = this.queryMember(memberRuleVo.getMemberId());
                this.saveMemberReward(memberBoxVo,0L, boxItemId, now,randomGift.getRewardId(), memberRuleVo.getSerialNum(), boxItemRewardVoMap);
            }
            this.updateReward(randomGift.getId(),randomGift.getRewardId(),randomGift.getLeftQuantity(),new ArrayList<>());
        }
    }

    private List<MaBoxItemRewardVo> getWinRewards(Map<Long, MaBoxItemRewardVo> boxItemRewardVoMap, List<Long> winRewardIds) {
        log.info("BuyBoxEventListener.getWinRewards = {}",winRewardIds);
        Map<Long,Integer> mapping = new HashMap<>();
        for (Long winRewardId : winRewardIds) {
            if (!mapping.containsKey(winRewardId)){
                mapping.put(winRewardId,1);
            }else {
                mapping.put(winRewardId,mapping.get(winRewardId) + 1);
            }
        }
        List<MaBoxItemRewardVo> rewards =new ArrayList<>();

        log.info("BuyBoxEventListener.getWinRewards.mapping = {}",JSON.toJSONString(mapping));
        mapping.forEach((k,v)->{
            //直接等号复制 地址引用  后续set 有问题
            //  MaBoxItemRewardVo rewardVo = boxItemRewardVoMap.get(k);
            MaBoxItemRewardVo rewardVo = null;
            if (boxItemRewardVoMap.get(k) != null) {
                rewardVo = new MaBoxItemRewardVo();
                BeanUtils.copyProperties(boxItemRewardVoMap.get(k),rewardVo);
            }
            if (rewardVo != null) {
                rewardVo.setNum(v);
                rewards.add(rewardVo);
            }
        });
        return rewards;
    }


    private void handleBuyUnLimitBoxSuccess(BuyBoxSuccessEvent event){
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        JSONObject extJson = event.getOrderExtJson();
        Long   boxItemId = extJson.getLong("boxItemId");

        Integer  num = extJson.getInteger("num");

        Long spuId = extJson.getLong("spuId");

        String boxType = extJson.getString("boxType");

        Long categoryId = extJson.getLong("categoryId");

        Long newcomerRuleId = extJson.getLong("newcomerRuleId");



        Date now = DateFormatUtils.getNow();

        int redisCount = 0;

        List<MaBoxItemRewardVo> itemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId);

        try {

            SaasQueryBuilder queryBox = SaasQueryBuilder.where(Restrictions.eq("spuId",spuId));
            ProductBox productBox = dao.query(queryBox,ProductBox.class);
            ProductBoxMine boxMine = null;
            boolean isMineing = false;
            //矿难出发数量
            BigDecimal mineTheoryCount = null;
            Long mineCount = null;
            //矿难初始化
            if (BooleanUtils.isTrue(productBox.getIsEnableMineDisaster())) {
                Ssqb queryProductBoxMine = Ssqb.create("com.lewei.ma.box.queryBoxMine")
                        .setParam("boxItemId", boxItemId);
                boxMine = dao.findForObj(queryProductBoxMine, ProductBoxMine.class);
                isMineing = false;
                if (boxMine != null && ProductBoxMineStatusEnum.S_PBMS_JXZ.value().equals(boxMine.getMineStatus())) {
                    isMineing = true;
                } else if (boxMine == null) {
                    boxMine = initProductBoxMine(boxItemId, productBox);
                }
                //理论出奖发发数
                if (!isMineing) {
                    List<MaBoxItemRewardVo> mineRewards = itemRewardVos.stream().filter(a -> Objects.equals(a.getCategoryId(), productBox.getMineCategoryId())).collect(Collectors.toList());
                    if (ListUtil.isNotEmpty(mineRewards)) {
                        BigDecimal odds = BigDecimal.ZERO;
                        for (MaBoxItemRewardVo mineReward : mineRewards) {
                            odds = odds.add(BigDecimal.valueOf(mineReward.getOdds()));
                        }
                        mineTheoryCount = BigDecimal.ONE.divide(odds, 2, BigDecimal.ROUND_HALF_UP).multiply(productBox.getMineBase());
                    }
                }
            }

            //抽奖最后位置
            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(BOX_COUNTER+boxItemId);
            if (currentRewardNum == null || currentRewardNum == 0) {
                currentRewardNum = boxService.queryBoxItemCount(boxItemId,null);
                redisCacheProvider.getRedisTemplate().opsForValue().set(BOX_COUNTER+boxItemId,currentRewardNum,1, TimeUnit.DAYS);
            }else {
                //重置过期时间
                redisCacheProvider.getRedisTemplate().expire(BOX_COUNTER+boxItemId,1, TimeUnit.DAYS);
            }

            //查询盲盒奖品

            if (isMineing && ProductBoxMineTypeEnum.S_MT_PROBABILITY_FLUCTUATION.value().equals(productBox.getMineType())){
                for (MaBoxItemRewardVo itemRewardVo : itemRewardVos) {
                    BigDecimal odds = BigDecimal.valueOf(itemRewardVo.getOdds()).add(BigDecimal.valueOf(itemRewardVo.getMineOdds()));
                    itemRewardVo.setOdds(odds.doubleValue());
                }

                //itemRewardVos.forEach(a->a.setOdds(a.getOdds() + a.getMineOdds()));
            }
            //sweet 配置two
           List<MaBoxItemRewardVo> randomRewradList =  this.sweetHandelTwo(order.getPaymentMethod(),order.getMemberId(), itemRewardVos);

            //总概率 可以支持概率总和不等一
            Double sumOdds = randomRewradList.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();

            //计算每个奖品的概率区间
            //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
            List<Double> rewardOddsList = new ArrayList<>();
            List<Double> sortRewardOddsList = new ArrayList<>() ;
            Double tempOdds = 0d;
            for (MaBoxItemRewardVo itemRewardVo : randomRewradList) {
                tempOdds += itemRewardVo.getOdds();
                rewardOddsList.add(tempOdds / sumOdds);
            }

            List<MaBoxItemRewardVo> winRewards = new ArrayList<>();
            MaBoxItemRewardVo rewardVo;
            for (int i = 0; i < num ; i++) {
                //随机数在哪个概率区间内，则是哪个奖品
                double randomDouble = Math.random();
                sortRewardOddsList.addAll(rewardOddsList);
                //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                sortRewardOddsList.add(randomDouble);
                Collections.sort(sortRewardOddsList);
                int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);
                rewardVo = new MaBoxItemRewardVo();
                //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
                BeanUtils.copyProperties(randomRewradList.get(lotteryIndex), rewardVo);

                //控制最大库存 如果库存不足 重复当此循环 直到找到奖品
                if (BooleanUtils.isTrue(rewardVo.getIsMaxQuantity())){
                    int l2 = this.updateRewardLeftQuantity(rewardVo.getRewardId(),1);
                    if (l2 < 1){
                        i --;
                        sortRewardOddsList.clear();
                        continue;
                    }
                }

                currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(BOX_COUNTER+boxItemId).intValue();
                redisCount++;
                rewardVo.setSerialNum(currentRewardNum);
                winRewards.add(rewardVo);

                sortRewardOddsList.clear();
            }

            if (sweetConfigService.querySweetConfigIsOpen()){
                //奖池机制
                this.sweetHandelThree(order.getPaymentMethod(),order.getMemberId(), itemRewardVos, winRewards);
            }else {
                //处理sweet 补偿
                this.sweetHandel(order.getPaymentMethod(),order.getMemberId(), itemRewardVos, winRewards);
            }


            List<OrderBoxItem> orderBoxItems = new ArrayList<>();
            OrderBoxItem orderBoxItem;
            List<MemberReward> memberRewards = new ArrayList<>();
            MemberReward memberReward;
            MemberBoxVo member = this.queryMember(order.getMemberId());

            for (MaBoxItemRewardVo reward : winRewards) {


                orderBoxItem = new OrderBoxItem();
                BeanUtils.copyProperties(reward,orderBoxItem);
                orderBoxItem.setPaymentMethod(orderLocal.get().getPaymentMethod());
                orderBoxItem.setOrderId(order.getId());
                orderBoxItem.setMemberId(order.getMemberId());
                orderBoxItem.setMemberName(member.getMemberName());
                orderBoxItem.setHeadImage(member.getHeadImage());
                orderBoxItem.setMemberTitleImage(member.getMemberTitleImage());
                orderBoxItem.setBoxItemId(boxItemId);
                orderBoxItem.setCreated(now);
                orderBoxItem.setRewardPriority(reward.getRewardPriority());
                orderBoxItem.setId(idWorker.nextId());
                if (reward.getCategoryPic() == null) {
                    orderBoxItem.setCategoryPic("");
                }
                orderBoxItems.add(orderBoxItem);
                //保存会员赏袋
                memberReward = new MemberReward();
                BeanUtils.copyProperties(reward,memberReward);
                memberReward.setOrderId(order.getId());
                memberReward.setMemberId(order.getMemberId());
                memberReward.setBoxItemId(boxItemId);
                memberReward.setCreated(now);
                memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                memberReward.setReceiveWay(getRewardReceiveWay(order.getPaymentMethod()));
                memberReward.setSpuId(reward.getSpuItemId());
                memberRewards.add(memberReward);

                if (BooleanUtils.isTrue(productBox.getIsEnableMineDisaster()) ){

                    mineCount = redisCacheProvider.getRedisTemplate().opsForHash().increment(MaCacheKeys.BOX_MINE_COUNT + order.getChainId(), boxItemId.toString(), 1);
                    if (!isMineing && !Objects.equals(reward.getCategoryId(),productBox.getMineCategoryId())){
                        //保存矿难抽奖次数
                        Ssqb updateMineRecord = Ssqb.create("com.lewei.ma.box.updateMineRecord")
                                .setParam("id",idWorker.nextId())
                                .setParam("boxItemId",boxItemId)
                                .setParam("memberId",order.getMemberId())
                                .setParam("mineId",boxMine.getId())
                                .setParam("num",1)
                                ;
                        dao.updateByMybatis(updateMineRecord);
                        //判断矿难是否触发
                        if (mineTheoryCount != null && BigDecimal.valueOf(mineCount).compareTo(mineTheoryCount) >= 0){
                            isMineing = true;
                            //触发矿难 保存记录
                            LocalDateTime dateNow = LocalDateTime.now();
                            Date endTime = dateNow.plusMinutes(productBox.getMineTime()).toDate();
                            boxMine.setBeTime(dateNow.toDate());
                            boxMine.setEndTime(endTime);
                            boxMine.setMineStatus(ProductBoxMineStatusEnum.S_PBMS_JXZ.value());
                            dao.update(boxMine);

                            //矿主标记
                            Ssqb queryMineMaxMember = Ssqb.create("com.lewei.ma.box.queryMineMaxMember")
                                    .setParam("boxMineId",boxMine.getId());
                            BoxMineRecordVo recordVo = dao.findForObj(queryMineMaxMember,BoxMineRecordVo.class);
                            if (recordVo != null) {
                                ProductBoxMineRecord mineRecord = new ProductBoxMineRecord();
                                mineRecord.setId(recordVo.getRecordId());
                                mineRecord.setMiners(true);
                                dao.update(mineRecord);
                            }

                            BoxMineDelayReq boxMineDelayReq = new BoxMineDelayReq();
                            boxMineDelayReq.setBoxItemId(boxItemId);
                            boxMineDelayReq.setBoxMineId(boxMine.getId());
                            boxMineDelayReq.setEndTime(endTime);
                            boxMineDelayReq.setBoxId(productBox.getId());
                            appFeignClient.handelBoxMineDelayMessage(boxMineDelayReq);
                        }
                    }
                }
                if (isMineing){

                    if (Objects.equals(reward.getCategoryId(),productBox.getMineCategoryId())){
                        //矿难结束
                        isMineing = false;
                        //双倍处理
                        if (ProductBoxMineTypeEnum.S_MT_PROBABILITY_FLUCTUATION.value().equals(productBox.getMineType())){
                            handleMineGift( boxMine.getId(),orderBoxItem.getId(), reward,boxItemId);
                        }
                    }

                }
                if (Objects.equals(reward.getCategoryId(),productBox.getMineCategoryId())){
                    isMineing = false;
                    //清空矿难未出发数
                    redisCacheProvider.getRedisTemplate().opsForHash().delete(MaCacheKeys.BOX_MINE_COUNT+order.getChainId(),boxItemId.toString());
                    //更新矿难状态
                    Ssqb updateMineStatus = Ssqb.create("com.lewei.ma.box.updateMineStatus")
                            .setParam("boxItemId",boxItemId);
                    dao.updateByMybatis(updateMineStatus);
                    //初始化矿难
                    boxMine = initProductBoxMine(boxItemId, productBox);

                }

            }


            dao.batchCreate(orderBoxItems,OrderBoxItem.class);
            dao.batchSave(memberRewards,MemberReward.class);
            ProductBoxItem productBoxItem = dao.queryById(boxItemId,ProductBoxItem.class);
//            //判断是否触发矿难
//            if ( BooleanUtils.isTrue(productBox.getIsEnableMineDisaster()) && ProductBoxMineStatusEnum.S_PBMS_WKS.value().equals(boxMine.getMineStatus())){
//                List<MaBoxItemRewardVo> mineRewards = itemRewardVos.stream().filter(a->Objects.equals(a.getCategoryId(),productBox.getMineCategoryId())).collect(Collectors.toList());
//                if (ListUtil.isNotEmpty(mineRewards) ){
//                    Integer mineCount = (Integer) redisCacheProvider.getRedisTemplate().opsForHash().get(MaCacheKeys.BOX_MINE_COUNT+order.getChainId(),boxItemId.toString());
//                    if (mineCount != null) {
//                        BigDecimal odds = BigDecimal.ZERO;
//                        for (MaBoxItemRewardVo mineReward : mineRewards) {
//                            odds = odds.add(BigDecimal.valueOf(mineReward.getOdds()));
//                        }
//                        BigDecimal totalCount = BigDecimal.ONE.divide(odds,2,BigDecimal.ROUND_HALF_UP).multiply(productBox.getMineBase());
//                        if (BigDecimal.valueOf(mineCount).compareTo(totalCount) >= 0){
//                            isMineing = true;
//                        }
//
//                        if (isMineing){
//                            //触发矿难 保存记录
//                            LocalDateTime dateNow = LocalDateTime.now();
//                            Date endTime = dateNow.plusMinutes(productBox.getMineTime()).toDate();
//                            boxMine.setBeTime(dateNow.toDate());
//                            boxMine.setEndTime(endTime);
//                            boxMine.setMineStatus(ProductBoxMineStatusEnum.S_PBMS_JXZ.value());
//                            dao.update(boxMine);
//
//                            BoxMineDelayReq boxMineDelayReq = new BoxMineDelayReq();
//                             boxMineDelayReq.setBoxItemId(boxItemId);
//                            boxMineDelayReq.setBoxMineId(boxMine.getId());
//                            boxMineDelayReq.setEndTime(endTime);
//                            boxMineDelayReq.setBoxId(productBox.getId());
//                            appFeignClient.handelBoxMineDelayMessage(boxMineDelayReq);
//                        }
//                    }
//                }
//            }


            if (productBox != null && BooleanUtils.isTrue(productBox.getIsProduceFraction())){
                //会员积分处理
                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
            }
            this.handleTradeFlow(order,BigDecimal.ZERO);
            this.saveOrderBoxItem2Es(orderBoxItems);

            if (ListUtil.isNotEmpty(winRewards)){
                if (!OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(order.getPaymentMethod())){
                    applicationEventPublisher.publishEvent(new RewardScoreEvent(this,order.getMemberId(),order.getChainId(),order.getOrderTitle(),winRewards));
                }

//                try {
//                    List<MaBoxItemRewardVo> giftRewards = winRewards.stream().filter(a-> BooleanUtils.isTrue(a.getIsGift())).collect(Collectors.toList());
//                    if (ListUtil.isNotEmpty(giftRewards)){
//                        sendSMS(order.getMemberId(),giftRewards);
//                    }
//                } catch (Exception e) {
//                    log.error("send reward sms fail", e);
//                }
                this.saveNewcomerBuyRecord(member.getId(), spuId,boxItemId,newcomerRuleId);

                //分销处理
                handleDist(order);
                //抽奖赠送处理
                applicationEventPublisher.publishEvent(new RewardGiftEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId()));
                //藏宝阁
                applicationEventPublisher.publishEvent(new TreasureEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),winRewards.size()));
                //弹幕
                applicationEventPublisher.publishEvent(new BulletChatEvent(this,winRewards,member,order.getChainId(),boxType,boxItemId,spuId));
                //会员每日成本
                applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,Collections.singletonMap(member,winRewards),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod(),ProductBoxTypeEnum.S_BT_UN_LIMIT.value(),order.getOrderTitle(),order.getOrderMoney()));
                //双倍狂欢
                applicationEventPublisher.publishEvent(new MemberDoubleEvent(this,winRewards,order.getMemberId(), num, boxItemId, spuId, order.getChainId()));

                if (OrderUtil.isMoneyPay(order.getPaymentMethod())){
                    //明信片处理
                    applicationEventPublisher.publishEvent(new PostcardEvent(this,order.getMemberId(),order.getChainId(),winRewards.size(),productBoxItem.getPriceFee(),order.getPaymentMethod()));
                    //宝箱活动处理
                    List<Long> orderIds = new ArrayList<Long>();
                    orderIds.add(order.getId());
                    applicationEventPublisher.publishEvent(new TreasureActivityEvent(this,order.getMemberId(),order.getChainId(),orderIds));
                }

                //聚宝盆
                if (OrderUtil.isMoneyPay(order.getPaymentMethod())) {
                    //是否全局开启聚宝盆
                    Ssqb queryProductBoxMine = Ssqb.create("com.lewei.ma.box.queryApplicationConfig")
                            .setParam("application", "funds-setting");
                    ApplicationConfig config = dao.findForObj(queryProductBoxMine, ApplicationConfig.class);
                    if (null != config && BooleanUtils.isTrue(config.getIsMaShow())) {
                        applicationEventPublisher.publishEvent(new CornucopiaEvent(this, winRewards, spuId, order.getMemberId(), order.getChainId(), order, boxItemId));
                    }
                }
                //IP分润
                applicationEventPublisher.publishEvent(new MemberServiceEvent(order.getId(),order.getMemberId(),this,winRewards,order.getChainId(), MDCTraceUtils.getTraceId()));
                //处理盲盒图鉴
                applicationEventPublisher.publishEvent(new BoxBookEvent(this,Collections.singletonMap(member,winRewards),spuId,order.getChainId(),MDCTraceUtils.getTraceId()));

                //异空间(仅无限盲盒)
                applicationEventPublisher.publishEvent(new DifferentSpaceEvent(this,Collections.singletonMap(member,winRewards),spuId, order.getMemberId(), order.getChainId(), MDCTraceUtils.getTraceId()));

            }
        } catch (Exception e) {
            e.printStackTrace();
            //退款到余额
            try {
                failHandel(order, boxItemId,spuId, null,num);

            } catch (Exception ex) {
                log.error("无限盲盒支付异常，退还到余额 失败 orderId = {},boxItemId = {},报错信息 = {}",order.getId(),boxItemId,ex.getMessage());
                ex.printStackTrace();
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId,redisCount);
            log.error("无限盲盒抽奖报错 = {}",e.getMessage());
            throw e;

        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            orderLocal.remove();
        }

    }



    private ProductBoxMine initProductBoxMine(Long boxItemId, ProductBox productBox) {
        ProductBoxMine boxMine;
        SaasQueryBuilder queryPriority = SaasQueryBuilder.where(Restrictions.eq("boxItemId", boxItemId))
                .and(Restrictions.isNotNull("beTime"));
        Integer priority = dao.queryForInt(queryPriority, ProductBoxMine.class);
        if (priority == null) {
            priority = 0;
        }
        //触发矿难 保存记录
        boxMine = new ProductBoxMine();
        boxMine.setPriority(priority + 1);
        boxMine.setBoxItemId(boxItemId);
        boxMine.setSpuId(productBox.getSpuId());
        boxMine.setCreated(DateFormatUtils.getNow());
        boxMine.setMineStatus(ProductBoxMineStatusEnum.S_PBMS_WKS.value());
        boxMine.setMineType(productBox.getMineType());
        dao.save(boxMine);
        return boxMine;
    }


    private void handleBuyCardBoxSuccess(BuyBoxSuccessEvent event){
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        JSONObject extJson = event.getOrderExtJson();
        Long   boxItemId = extJson.getLong("boxItemId");
        Integer  num = extJson.getInteger("num");
        Long spuId = extJson.getLong("spuId");
        Boolean isAllBuyCard = extJson.getBoolean("isAllBuyCard");
        String  boxType = extJson.getString("boxType");
        Long categoryId = extJson.getLong("categoryId");


        Date now = DateFormatUtils.getNow();

        int redisCount = 0;

        try {

            SaasQueryBuilder queryBox = SaasQueryBuilder.where(Restrictions.eq("spuId",spuId));
            ProductBox productBox = dao.query(queryBox,ProductBox.class);

            //抽奖最后位置
            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(BOX_COUNTER+boxItemId);
            if (currentRewardNum == null || currentRewardNum == 0) {
                currentRewardNum = boxService.queryBoxItemCount(boxItemId,null);

                redisCacheProvider.getRedisTemplate().opsForValue().set(BOX_COUNTER+boxItemId,currentRewardNum,1, TimeUnit.DAYS);
            }else {
                //重置过期时间
                redisCacheProvider.getRedisTemplate().expire(BOX_COUNTER+boxItemId,1, TimeUnit.DAYS);
            }

            //查询盲盒奖品

            List<MaBoxItemRewardVo> itemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId);
            //总概率 可以支持概率总和不等一
            Double sumOdds = itemRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();

            //计算每个奖品的概率区间
            //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
            List<Double> rewardOddsList = new ArrayList<>();
            Double tempOdds = 0d;
            for (MaBoxItemRewardVo itemRewardVo : itemRewardVos) {
                tempOdds += itemRewardVo.getOdds();
                rewardOddsList.add(tempOdds / sumOdds);
            }

            List<MaBoxItemRewardVo> winRewards  = new ArrayList<>();
            MaBoxItemRewardVo rewardVo;
            //包map  每个序号对应一包
            Map<Integer,List<MaBoxItemRewardVo>> packMap = new HashMap<>();
            for (int i = 0; i < num ; i++) {
                winRewards = new ArrayList<>();
                for (int j = 0; j < productBox.getOnePackCount() ; j++) {
                    rewardVo = getOddsReward(itemRewardVos, rewardOddsList);

                    //控制最大库存 如果库存不足 重复当此循环 直到找到奖品
                    if (BooleanUtils.isTrue(rewardVo.getIsMaxQuantity())){
                        int l2 = this.updateRewardLeftQuantity(rewardVo.getRewardId(),1);
                        if (l2 < 1){
                            j--;
                            continue;
                        }
                    }
                    currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(BOX_COUNTER+boxItemId).intValue();
                    redisCount++;
                    rewardVo.setSerialNum(currentRewardNum);
                    winRewards.add(rewardVo);

                }
                packMap.put(i+1,winRewards);

            }

            //端盒处理
            if (BooleanUtils.isTrue(isAllBuyCard)){
                List<MaBoxItemRewardVo> allBuyItemRewardVos = itemRewardVos.stream().filter(a->BooleanUtils.isTrue(a.getIsAllBuyAppear())).collect(Collectors.toList());
                if (ListUtil.isNotEmpty(allBuyItemRewardVos)){
                    //总概率 可以支持概率总和不等一
                    sumOdds = allBuyItemRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getAllBuyAppearOdds).sum();

                    //计算每个奖品的概率区间
                    //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
                    rewardOddsList = new ArrayList<>();
                    tempOdds = 0d;
                    for (MaBoxItemRewardVo itemRewardVo : allBuyItemRewardVos) {
                        tempOdds += itemRewardVo.getAllBuyAppearOdds();
                        rewardOddsList.add(tempOdds / sumOdds);
                    }
                    rewardVo = getOddsReward(allBuyItemRewardVos, rewardOddsList);
                    //随机去除一包 替换其中一个奖品
                    Random random = new Random();
                    Integer randomNum = random.nextInt(num) + 1;
                    List<MaBoxItemRewardVo>  packRewards = packMap.get(randomNum);
                    int serialNum = packRewards.get(0).getSerialNum();
                    rewardVo.setSerialNum(serialNum);
                    packMap.get(randomNum).set(0,rewardVo);
                }
            }

            List<OrderBoxItem> orderBoxItems = new ArrayList<>();
            List<MemberReward> memberRewards = new ArrayList<>();
            MemberBoxVo member = this.queryMember(order.getMemberId());


            packMap.forEach((k,v)->{
                for (MaBoxItemRewardVo reward : v) {

                    OrderBoxItem orderBoxItem = new OrderBoxItem();
                    BeanUtils.copyProperties(reward,orderBoxItem);
                    orderBoxItem.setPaymentMethod(orderLocal.get().getPaymentMethod());
                    orderBoxItem.setOrderId(order.getId());
                    orderBoxItem.setMemberId(order.getMemberId());
                    orderBoxItem.setMemberName(member.getMemberName());
                    orderBoxItem.setHeadImage(member.getHeadImage());
                    orderBoxItem.setMemberTitleImage(member.getMemberTitleImage());
                    orderBoxItem.setBoxItemId(boxItemId);
                    orderBoxItem.setCreated(now);
                    orderBoxItem.setRewardPriority(reward.getRewardPriority());
                    orderBoxItem.setCategoryName(reward.getLevelName());
                    orderBoxItem.setCategoryPic(reward.getLevelImage());
                    orderBoxItem.setStageId(Long.valueOf(k));
                    orderBoxItem.setRewardType(RewardTypeEnum.S_SRT_CARD.value());
                    orderBoxItems.add(orderBoxItem);
                    //保存会员赏袋
                    MemberReward memberReward = new MemberReward();
                    BeanUtils.copyProperties(reward,memberReward);
                    memberReward.setOrderId(order.getId());
                    memberReward.setMemberId(order.getMemberId());
                    memberReward.setBoxItemId(boxItemId);
                    memberReward.setCreated(now);
                    memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                    memberReward.setReceiveWay(getRewardReceiveWay(order.getPaymentMethod()));
                    memberReward.setIsCard(true);
                    memberReward.setSpuId(reward.getSpuItemId());
                    memberRewards.add(memberReward);
                }
            });




            dao.batchSave(orderBoxItems,OrderBoxItem.class);
            dao.batchSave(memberRewards,MemberReward.class);
            ProductBoxItem productBoxItem = dao.queryById(boxItemId,ProductBoxItem.class);
            if (productBox != null && BooleanUtils.isTrue(productBox.getIsProduceFraction())){
                //会员积分处理
                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
            }
            this.handleTradeFlow(order,BigDecimal.ZERO);
            this.saveOrderBoxItem2Es(orderBoxItems);

            if (ListUtil.isNotEmpty(winRewards)){
                //分销处理
                handleDist(order);
                //抽奖赠送处理
                // applicationEventPublisher.publishEvent(new RewardGiftEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId()));
                //藏宝阁
                // applicationEventPublisher.publishEvent(new TreasureEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),winRewards.size()));
                applicationEventPublisher.publishEvent(new BulletChatEvent(this,winRewards,member,order.getChainId(),boxType,boxItemId,spuId));
                //会员每日成本
                applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,Collections.singletonMap(member,winRewards),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod()));
                if (OrderUtil.isMoneyPay(order.getPaymentMethod())){
                    //明信片处理
                    applicationEventPublisher.publishEvent(new PostcardEvent(this,order.getMemberId(),order.getChainId(),winRewards.size(),productBoxItem.getPriceFee(),order.getPaymentMethod()));
                    //宝箱活动处理
                    List<Long> orderIds = new ArrayList<Long>();
                    orderIds.add(order.getId());
                    applicationEventPublisher.publishEvent(new TreasureActivityEvent(this,order.getMemberId(),order.getChainId(),orderIds));
                }
                //ip分润
                applicationEventPublisher.publishEvent(new MemberServiceEvent(order.getId(),order.getMemberId(),this,winRewards,order.getChainId(), MDCTraceUtils.getTraceId()));

            }
        } catch (Exception e) {
            //退款到余额
            try {
                Order newOrder = new Order();
                newOrder.setId(order.getId());
                newOrder.setChainId(order.getChainId());
                newOrder.setOrderMoney(BigDecimal.ZERO);
                this.updateOrder(newOrder);

                MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                balanceRequest.setGift(BigDecimal.ZERO);
                balanceRequest.setBalance(order.getPaymentMoney());
                balanceRequest.setMemberId(order.getMemberId());
                balanceRequest.setPlId(order.getId());
                balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem12001"));
                balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                memberFeignClient.memberBalanceHandle(balanceRequest);
            } catch (Exception ex) {
                log.error("无限盲盒支付异常，退还到余额 失败 orderId = {},boxItemId = {},报错信息 = {}",order.getId(),boxItemId,ex.getMessage());
                ex.printStackTrace();
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId,redisCount);
            log.error("无限盲盒抽奖报错 = {}",e.getMessage());
            throw e;

        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            orderLocal.remove();
        }

    }

    private void handleBuyNewBoxSuccess(BuyBoxSuccessEvent event){
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);
        rewardLocalList.set(new ConcurrentHashMap<>());

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        JSONObject extJson = event.getOrderExtJson();
        Long   boxItemId = extJson.getLong("boxItemId");
        Long   spuId = extJson.getLong("spuId");
        Long   skuId = extJson.getLong("skuId");
        Integer  num = extJson.getInteger("num");
        Long chainId = order.getChainId();
        List<Integer> serialNums = extJson.getJSONArray("serialNums").toJavaList(Integer.class);
        Long categoryId = extJson.getLong("categoryId");

        Date now = DateFormatUtils.getNow();

        int redisCount = 0;

        String counterKey = BOX_COUNTER+boxItemId;

        try {

            //抽奖最后位置
            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(counterKey);
            if (currentRewardNum == null || currentRewardNum == 0) {
                SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItemId))
                        .and(Restrictions.eq("isGift",0))
                        .and(Restrictions.eq("isAward",1))
                        ;
                currentRewardNum =  dao.queryForInt(query, ProductBoxItemRewardTmpl.class);
                redisCacheProvider.getRedisTemplate().opsForValue().set(counterKey,currentRewardNum,1, TimeUnit.DAYS);
            }else {
                //重置过期时间
                redisCacheProvider.getRedisTemplate().expire(counterKey,1, TimeUnit.DAYS);
            }
            MaBoxItemDetailVo boxDetailVo = boxService.queryBoxItemDetailVo(boxItemId);

            //限制时间类型 发送队列处理
            if (currentRewardNum == 0 ){
                if(BooleanUtils.isTrue(boxDetailVo.getIsLimitTime())){
                    LocalDateTime bgTime = LocalDateTime.now();
                    LocalDateTime endTime ;
                    if (TimeTypeEnum.S_DT_HOUR.value().equals(boxDetailVo.getLimitTimeType())){
                        endTime = bgTime.plusHours(boxDetailVo.getLimitTime());
                    }else {
                        endTime = bgTime.plusMinutes(boxDetailVo.getLimitTime());
                    }
                    Ssqb updateBoxItemTime = Ssqb.create("com.lewei.ma.box.updateBoxItemTime")
                            .setParam("boxItemId",boxItemId)
                            .setParam("bgTime",bgTime.toDate())
                            .setParam("endTime",endTime.toDate());
                    dao.updateByMybatis(updateBoxItemTime);
                    //发送消息队列
                    appFeignClient.handelBoxLimitTimeDelayMessage(new BoxLimitTimeDelayReq(boxItemId,endTime.toDate()));
                }else {
                    ProductBoxItem productBoxItem = new ProductBoxItem();
                    productBoxItem.setId(boxItemId);
                    productBoxItem.setUpdated(now);
                    dao.update(productBoxItem);
                }
                if (currentRewardNum == 0){
                    applicationEventPublisher.publishEvent(new AutoBuyBoxEvent(this,boxDetailVo.getSpuId(),skuId,boxItemId,order.getChainId(),MDCTraceUtils.getTraceId()));
                }
            }

            if (BoxSequenceTypeEnum.S_BST_QUEUE.value().equals(boxDetailVo.getSequenceType())){
                serialNums = new ArrayList<>();
                for (Integer i = 0; i < num; i++) {
                    currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(counterKey).intValue();
                    redisCount++;
                    serialNums.add(currentRewardNum);
                }
            }else if (BoxSequenceTypeEnum.S_BST_RANDOM.value().equals(boxDetailVo.getSequenceType())){
                serialNums = new ArrayList<>();
                Random random = new Random();
                //随机序号处理 查询剩余序号
                Ssqb queryLeftSerNums = Ssqb.create("com.lewei.ma.box.queryLeftSerNums")
                        .setParam("boxItemId",boxItemId);
                List<Integer> leftSerNums = dao.findForList(queryLeftSerNums,Integer.class);
                if (ListUtil.isNotEmpty(leftSerNums)){
                    for (int i = 0; i < num; i++) {
                        if (!leftSerNums.isEmpty()){
                            int index = random.nextInt(leftSerNums.size());
                            serialNums.add(leftSerNums.get(index));
                            leftSerNums.remove(index);
                        }
                    }
                }
                currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(counterKey,num).intValue();
                redisCount = redisCount + num;
            }else {
                currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(counterKey,num).intValue();
                redisCount = redisCount + num;

            }
            List<BoxStageVo> stageVos = new ArrayList<>();
            if (!StringUtil.isEmpty(boxDetailVo.getStageJson())){
                stageVos = JSON.parseArray(boxDetailVo.getStageJson(),BoxStageVo.class);
            }
            Map<Long,BoxStageVo> stageVoMap = stageVos.stream().collect(Collectors.toMap(BoxStageVo::getStageId,c->c));

            MemberBoxVo memberBoxVo = this.queryMember(order.getMemberId());
            Map<Long,MaBoxItemRewardVo> boxItemRewardVoMap = this.queryBoxItemRewardMap(boxItemId);

            Map<Long,List<Long>> memberRewardIdMap = new HashMap<>();
            List<Long> winRewardIds = new ArrayList<>();
            //orderId->memberId
            Map<Long,Long> rewardGiftMap = new HashMap<>();
            //orderId->memberId
            Map<Long,Long> mbOrderIds = new HashMap<>();
            //处理最后购买中奖
            List<MaBoxItemDetailVo.RewardRule> lastGifts = boxDetailVo.getRewardRules().stream().filter(a->BooleanUtils.isTrue(a.getIsGift()) && BoxRewardRuleEnum.S_PBRR_LAST.value().equals(a.getRuleType()) && a.getStageId() != 0L ).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(lastGifts) && !stageVoMap.isEmpty()){
                //查询所有未出位置
                SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItemId))
                        .and(Restrictions.eq("memberId",0))
                        .and(Restrictions.eq("isGift",0));
                List<ProductBoxItemRewardTmpl> rewardTmpls = dao.queryList(query,ProductBoxItemRewardTmpl.class);
                List<Integer> finalSerNums = new ArrayList<>();
                finalSerNums.addAll(serialNums);
                stageVoMap.forEach((stageId,stageVo)->{
                    List<Integer> leftSerNums = rewardTmpls.stream().filter(a->a.getSerialNum()>= stageVo.getBgStage() && a.getSerialNum() <= stageVo.getEndStage()).map(ProductBoxItemRewardTmpl::getSerialNum).collect(Collectors.toList());
                    if (ListUtil.isNotEmpty(leftSerNums) && finalSerNums.containsAll(leftSerNums)){
                        //出最终购买可得
                        List<MaBoxItemDetailVo.RewardRule> stageLastGifts = lastGifts.stream().filter(a->a.getStageId().compareTo(stageId) == 0).collect(Collectors.toList());
                        for (MaBoxItemDetailVo.RewardRule stageLastGift : stageLastGifts) {
                            for (int i = 0; i < stageLastGift.getQuantity(); i++) {
                                ProductBoxItemRewardTmpl rewardTmpl = new ProductBoxItemRewardTmpl();
                                rewardTmpl.setIsAward(false);
                                rewardTmpl.setIsGift(true);
                                rewardTmpl.setCreated(DateFormatUtils.getNow());
                                rewardTmpl.setOrderId(0L);
                                rewardTmpl.setMemberId(order.getMemberId());
                                rewardTmpl.setIsRead(false);
                                rewardTmpl.setBoxItemId(boxItemId);
                                rewardTmpl.setRewardId(stageLastGift.getRewardId());
                                rewardTmpl.setRuleId(stageLastGift.getRuleId());
                                rewardTmpl.setSerialNum(leftSerNums.get(0));
                                rewardTmpl.setStageId(stageId);
                                dao.save(rewardTmpl);
                            }
                        }
                    }
                });
            }
            //更新模板位置
            List<ProductBoxItemRewardTmpl> rewardTmpls = new ArrayList<>();
            if(ListUtil.isNotEmpty(serialNums)){
                rewardTmpls = this.queryProductBoxItemRewardTmpls(boxItemId,false,false,null,serialNums);
            }
            if (ListUtil.isNotEmpty(rewardTmpls)){
                for (ProductBoxItemRewardTmpl rewardTmpl : rewardTmpls) {
                    Ssqb updateRewardTmpl  = Ssqb.create("com.lewei.ma.box.updateRewardTmpl")
                            .setParam("memberId",order.getMemberId())
                            .setParam("serialNum",rewardTmpl.getSerialNum())
                            .setParam("boxItemId",boxItemId)
                            .setParam("orderId",order.getId())
                            ;
                    dao.updateByMybatis(updateRewardTmpl);
                    if (BoxLotteryTypeEnum.S_BLT_IMMEDIATE.value().equals(boxDetailVo.getLotteryType())){
                        this.updateReward(rewardTmpl.getRuleId(),rewardTmpl.getRewardId(),1,winRewardIds);
                        this.saveMemberReward(memberBoxVo,order.getId(),boxItemId,now,rewardTmpl.getRewardId(),rewardTmpl.getSerialNum(),boxItemRewardVoMap);
                        handleMemberRewardMap(memberRewardIdMap,order.getMemberId(), Collections.singletonList(rewardTmpl.getRewardId()));
                        rewardGiftMap.put(order.getId(),order.getMemberId());
                        mbOrderIds.put(order.getId(),order.getMemberId());
                    }
                }
            }

            int refundNum = num - rewardTmpls.size();
            if (refundNum > 0){
                currentRewardNum = redisCacheProvider.getRedisTemplate().opsForValue().decrement(counterKey,refundNum).intValue();
                redisCount = redisCount - refundNum;
            }

            int endTag = 0;

            //立即开奖
            if (BoxLotteryTypeEnum.S_BLT_IMMEDIATE.value().equals(boxDetailVo.getLotteryType())){
                Integer totalNum = currentRewardNum;
                if (totalNum >= boxDetailVo.getRewardNum()){
                    //更新item状态
                    Ssqb updateBoxItemStatus = Ssqb.create("com.lewei.ma.box.updateBoxItemStatus")
                            .setParam("boxItemId",boxItemId)
                            .setParam("status",BoxItemSaleStatusEnum.S_PBSS_SOLD_OUT.value());
                    endTag = dao.updateByMybatis(updateBoxItemStatus);
                }

                //查询 templ 池子满 未出奖
                List<ProductBoxItemRewardTmpl> giftTmpls = this.queryProductBoxItemRewardTmpls(boxItemId,true,false,null,null);
                if (ListUtil.isNotEmpty(giftTmpls)){
                    // List<ProductBoxItemRewardTmpl> stageGiftTmpls = giftTmpls.stream().filter(a->a.getStageId() !=0 ).collect(Collectors.toList());
                    Map<Long,List<ProductBoxItemRewardTmpl>> mapping = giftTmpls.stream().collect(Collectors.groupingBy(ProductBoxItemRewardTmpl::getStageId));
                    mapping.forEach((k,v)->{
                        //全局
                        if (k == 0){
                            if(totalNum >= boxDetailVo.getRewardNum()){
                                for (ProductBoxItemRewardTmpl rewardTmpl : v) {
                                    this.updateReward(rewardTmpl.getRuleId(),rewardTmpl.getRewardId(),1,new ArrayList<>());
                                    this.saveMemberReward(this.queryMember(rewardTmpl.getMemberId()),0L,boxItemId,now,rewardTmpl.getRewardId(),rewardTmpl.getSerialNum(),boxItemRewardVoMap);
                                    rewardTmpl.setIsAward(true);
                                    dao.update(rewardTmpl);
                                    handleMemberRewardMap(memberRewardIdMap,rewardTmpl.getMemberId(), Collections.singletonList(rewardTmpl.getRewardId()));
                                }
                            }
                        }else {
                            //查询阶段已出数量
                            BoxStageVo boxStageVo = stageVoMap.get(k);

                            int count = this.queryProductBoxItemRewardTmplCount(boxItemId,false,true,boxStageVo.getBgStage(),boxStageVo.getEndStage());
                            //出阶段奖
                            if (count == (boxStageVo.getEndStage() - boxStageVo.getBgStage()) + 1){
                                for (ProductBoxItemRewardTmpl rewardTmpl : v) {
                                    this.updateReward(rewardTmpl.getRuleId(),rewardTmpl.getRewardId(),1,new ArrayList<>());
                                    this.saveMemberReward(this.queryMember(rewardTmpl.getMemberId()),0L,boxItemId,now,rewardTmpl.getRewardId(),rewardTmpl.getSerialNum(),boxItemRewardVoMap);
                                    rewardTmpl.setIsAward(true);
                                    dao.update(rewardTmpl);
                                    handleMemberRewardMap(memberRewardIdMap,rewardTmpl.getMemberId(), Collections.singletonList(rewardTmpl.getRewardId()));

                                }
                            }
                        }
                    });
                }

                // 处理积分
                if (BooleanUtils.isTrue(boxDetailVo.getIsProduceFraction())){
                    //会员积分处理
                    applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
                }
            }
            //人满开
            if (BoxLotteryTypeEnum.S_BLT_LAST.value().equals(boxDetailVo.getLotteryType()) && currentRewardNum >= boxDetailVo.getRewardNum() ){
                //更新item状态
                Ssqb updateBoxItemStatus = Ssqb.create("com.lewei.ma.box.updateBoxItemStatus")
                        .setParam("boxItemId",boxItemId)
                        .setParam("status",BoxItemSaleStatusEnum.S_PBSS_SOLD_OUT.value());
                endTag = dao.updateByMybatis(updateBoxItemStatus);
                if (endTag > 0){
                    List<ProductBoxItemRewardTmpl> allRewardTmpls = this.queryProductBoxItemRewardTmpls(boxItemId,false,null,null,null);
                    for (ProductBoxItemRewardTmpl rewardTmpl : allRewardTmpls) {
                        this.updateReward(rewardTmpl.getRuleId(),rewardTmpl.getRewardId(),1,new ArrayList<>());
                        this.saveMemberReward(this.queryMember(rewardTmpl.getMemberId()),rewardTmpl.getOrderId(),boxItemId,now,rewardTmpl.getRewardId(),rewardTmpl.getSerialNum(),boxItemRewardVoMap);
                        handleMemberRewardMap(memberRewardIdMap,rewardTmpl.getMemberId(), Collections.singletonList(rewardTmpl.getRewardId()));
                        rewardGiftMap.put(rewardTmpl.getOrderId(),rewardTmpl.getMemberId());
                        mbOrderIds.put(rewardTmpl.getOrderId(),rewardTmpl.getMemberId());
                    }

                    List<ProductBoxItemRewardTmpl> allGiftTmpls = this.queryProductBoxItemRewardTmpls(boxItemId,true,null,null,null);
                    for (ProductBoxItemRewardTmpl rewardTmpl : allGiftTmpls) {
                        this.updateReward(rewardTmpl.getRuleId(),rewardTmpl.getRewardId(),1,new ArrayList<>());
                        this.saveMemberReward(this.queryMember(rewardTmpl.getMemberId()),0L,boxItemId,now,rewardTmpl.getRewardId(),rewardTmpl.getSerialNum(),boxItemRewardVoMap);
                        handleMemberRewardMap(memberRewardIdMap,rewardTmpl.getMemberId(), Collections.singletonList(rewardTmpl.getRewardId()));

                    }
                    //处理积分
                    if (BooleanUtils.isTrue(boxDetailVo.getIsProduceFraction())){
                        //查询所有订单
                        Ssqb queryRewardTmplOrder = Ssqb.create("com.lewei.ma.box.queryRewardTmplOrder")
                                .setParam("boxItemId",boxItemId);
                        List<RewardTmplOrderVo> rewardTmplOrderVos = dao.findForList(queryRewardTmplOrder,RewardTmplOrderVo.class);
                        if(ListUtil.isNotEmpty(rewardTmplOrderVos)){
                            for (RewardTmplOrderVo rewardTmplOrderVo : rewardTmplOrderVos) {
                                //会员积分处理
                                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,rewardTmplOrderVo.getMemberId(),rewardTmplOrderVo.getOrderMoney(), rewardTmplOrderVo.getOrderType(),rewardTmplOrderVo.getChainId(),rewardTmplOrderVo.getOrderTitle(),rewardTmplOrderVo.getPaymentMethod()));
                            }
                        }

                    }
                }
            }

            //处理退款
            BigDecimal refundFee = this.handelRefund(order,num,refundNum,spuId,null,null);
            this.handleTradeFlow(order,refundFee);

            //处理怒气
            handelAfterGift(chainId,memberRewardIdMap,boxItemRewardVoMap);
            //分销处理
            handleDist(order);

            //弹幕
            List<MaBoxItemRewardVo> rewardVoList = new ArrayList<>();
            rewardLocalList.get().forEach((k,v)->{
                applicationEventPublisher.publishEvent(new BulletChatEvent(this,v,k,order.getChainId(),boxDetailVo.getBoxType(),boxItemId,spuId));
                rewardVoList.addAll(v);

            });
            //会员每日成本
            applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,rewardLocalList.get(),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod()));
            //ip分润
            applicationEventPublisher.publishEvent(new MemberServiceEvent(order.getId(),order.getMemberId(),this,rewardVoList,order.getChainId(), MDCTraceUtils.getTraceId()));
            //处理盲盒图鉴
            applicationEventPublisher.publishEvent(new BoxBookEvent(this,rewardLocalList.get(),spuId,order.getChainId(),MDCTraceUtils.getTraceId()));


            //赠送处理
            if (MapUtils.isNotEmpty(rewardGiftMap)) {
                rewardGiftMap.forEach((k, v) -> {
                    //抽赏赠送
                    applicationEventPublisher.publishEvent(new RewardGiftEvent(this, k, boxItemId, v, order.getChainId()));
                    //藏宝阁
                    applicationEventPublisher.publishEvent(new TreasureEvent(this, k, boxItemId, v, order.getChainId(), 1));
                });
            }
            //宝箱活动,明信片处理
            if (MapUtils.isNotEmpty(mbOrderIds)) {
                Map<Long, List<Long>> result = mbOrderIds.entrySet().stream()
                        .collect(Collectors.groupingBy(Map.Entry::getValue,
                                Collectors.mapping(Map.Entry::getKey, Collectors.toList())));
                    //明信片处理
                    applicationEventPublisher.publishEvent(new PostcardEvent(this, order.getChainId(),boxDetailVo.getPriceFee(),result));
                    //宝箱活动处理
                    applicationEventPublisher.publishEvent(new TreasureActivityEvent(this, order.getChainId(), result));
            }
            if (endTag > 0){
                applicationEventPublisher.publishEvent(new RewardGiftEvent(this,0L,boxItemId,0L,order.getChainId(),spuId,null));
            }


        } catch (Exception e) {
            //退款到余额
            try {
                failHandel(order,boxItemId,spuId,null,num);
            } catch (Exception ex) {
                log.error("无限盲盒支付异常，退还到余额 失败 orderId = {},boxItemId = {},报错信息 = {}",order.getId(),boxItemId,ex.getMessage());
                ex.printStackTrace();
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId,redisCount);
            log.error("无限盲盒抽奖报错 = {}",e.getMessage());
            throw e;

        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            orderLocal.remove();
        }

    }


    private void handleBuyConcatenateBoxSuccess(BuyBoxSuccessEvent event){
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        JSONObject extJson = event.getOrderExtJson();
        Long   boxItemId = extJson.getLong("boxItemId");

        Integer  num = extJson.getInteger("num");

        Long spuId = extJson.getLong("spuId");

        String boxType = extJson.getString("boxType");
        Long stageId = extJson.getLong("stageId");
        Long categoryId = extJson.getLong("categoryId");

        Date now = DateFormatUtils.getNow();

        int redisCount = 0;

        List<MaBoxItemRewardVo> itemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId,stageId);



        try {

            SaasQueryBuilder queryBox = SaasQueryBuilder.where(Restrictions.eq("spuId",spuId));
            ProductBox productBox = dao.query(queryBox,ProductBox.class);
            String serialNumKey = BOX_COUNTER+boxItemId + "@" + stageId;
            //抽奖最后位置
            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(serialNumKey);
            if (currentRewardNum == null || currentRewardNum == 0) {
                currentRewardNum = boxService.queryBoxItemCount(boxItemId,stageId);

                redisCacheProvider.getRedisTemplate().opsForValue().set(serialNumKey,currentRewardNum,1, TimeUnit.DAYS);
            }else {
                //重置过期时间
                redisCacheProvider.getRedisTemplate().expire(serialNumKey,1, TimeUnit.DAYS);
            }

            //总概率 可以支持概率总和不等一
            Double sumOdds = itemRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();

            //计算每个奖品的概率区间
            //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
            List<Double> rewardOddsList = new ArrayList<>();
            List<Double> sortRewardOddsList = new ArrayList<>() ;
            Double tempOdds = 0d;
            for (MaBoxItemRewardVo itemRewardVo : itemRewardVos) {
                tempOdds += itemRewardVo.getOdds();
                rewardOddsList.add(tempOdds / sumOdds);
            }

            List<MaBoxItemRewardVo> winRewards = new ArrayList<>();
            MaBoxItemRewardVo rewardVo;
            for (int i = 0; i < num ; i++) {
                //随机数在哪个概率区间内，则是哪个奖品
                double randomDouble = Math.random();
                sortRewardOddsList.addAll(rewardOddsList);
                //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                sortRewardOddsList.add(randomDouble);
                Collections.sort(sortRewardOddsList);
                int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);
                rewardVo = new MaBoxItemRewardVo();
                //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
                BeanUtils.copyProperties(itemRewardVos.get(lotteryIndex), rewardVo);

                currentRewardNum =  redisCacheProvider.getRedisTemplate().opsForValue().increment(serialNumKey).intValue();
                redisCount++;
                rewardVo.setSerialNum(currentRewardNum);
                winRewards.add(rewardVo);

                sortRewardOddsList.clear();
            }

            List<OrderBoxItem> orderBoxItems = new ArrayList<>();
            OrderBoxItem orderBoxItem;
            List<MemberReward> memberRewards = new ArrayList<>();
            MemberReward memberReward;
            MemberBoxVo member = this.queryMember(order.getMemberId());

            for (MaBoxItemRewardVo reward : winRewards) {


                orderBoxItem = new OrderBoxItem();
                BeanUtils.copyProperties(reward,orderBoxItem);
                orderBoxItem.setPaymentMethod(orderLocal.get().getPaymentMethod());
                orderBoxItem.setOrderId(order.getId());
                orderBoxItem.setMemberId(order.getMemberId());
                orderBoxItem.setMemberName(member.getMemberName());
                orderBoxItem.setHeadImage(member.getHeadImage());
                orderBoxItem.setMemberTitleImage(member.getMemberTitleImage());
                orderBoxItem.setBoxItemId(boxItemId);
                orderBoxItem.setCreated(now);
                orderBoxItem.setRewardPriority(reward.getRewardPriority());
                orderBoxItem.setId(idWorker.nextId());
                if (reward.getCategoryPic() == null) {
                    orderBoxItem.setCategoryPic("");
                }
                orderBoxItems.add(orderBoxItem);
                //保存会员赏袋
                memberReward = new MemberReward();
                BeanUtils.copyProperties(reward,memberReward);
                memberReward.setOrderId(order.getId());
                memberReward.setMemberId(order.getMemberId());
                memberReward.setBoxItemId(boxItemId);
                memberReward.setCreated(now);
                memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                memberReward.setReceiveWay(getRewardReceiveWay(order.getPaymentMethod()));
                memberReward.setSpuId(reward.getSpuItemId());
                memberRewards.add(memberReward);

            }


            dao.batchCreate(orderBoxItems,OrderBoxItem.class);
            dao.batchSave(memberRewards,MemberReward.class);
            ProductBoxItem productBoxItem = dao.queryById(boxItemId,ProductBoxItem.class);


            if (productBox != null && BooleanUtils.isTrue(productBox.getIsProduceFraction())){
                //会员积分处理
                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
            }
            this.handleTradeFlow(order,BigDecimal.ZERO);
            this.saveOrderBoxItem2Es(orderBoxItems);



            if (ListUtil.isNotEmpty(winRewards)){
                if (!OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(order.getPaymentMethod())){
                    applicationEventPublisher.publishEvent(new RewardScoreEvent(this,order.getMemberId(),order.getChainId(),order.getOrderTitle(),winRewards));
                }


                //分销处理
                handleDist(order);
                //抽奖赠送处理
                applicationEventPublisher.publishEvent(new RewardGiftEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),stageId));
                //藏宝阁
                applicationEventPublisher.publishEvent(new TreasureEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),winRewards.size(),stageId));
                //明信片处理
                if (OrderUtil.isMoneyPay(order.getPaymentMethod())){
                    //明信片处理
                    applicationEventPublisher.publishEvent(new PostcardEvent(this,order.getMemberId(),order.getChainId(),winRewards.size(),productBoxItem.getPriceFee(),order.getPaymentMethod()));
                    //宝箱活动处理
                    List<Long> orderIds = new ArrayList<Long>();
                    orderIds.add(order.getId());
                    applicationEventPublisher.publishEvent(new TreasureActivityEvent(this,order.getMemberId(),order.getChainId(),orderIds));
                }

                //弹幕
                applicationEventPublisher.publishEvent(new BulletChatEvent(this,winRewards,member,order.getChainId(),boxType,boxItemId,spuId));
                //会员每日成本
                applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,Collections.singletonMap(member,winRewards),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod()));
                //处理盲盒图鉴
                applicationEventPublisher.publishEvent(new BoxBookEvent(this,Collections.singletonMap(member,winRewards),spuId,order.getChainId(),MDCTraceUtils.getTraceId()));


                //获得闯关票数量
                int ticketNum = winRewards.stream().filter(a->BooleanUtils.isTrue(a.getIsConcatenate())).map(MaBoxItemRewardVo::getTicketNum).reduce(Integer::sum).orElse(0);
                if (ticketNum > 0){
                    //增加闯关票
                    MemberBoxTicketReq ticketReq = new MemberBoxTicketReq();
                    ticketReq.setBoxItemId(boxItemId);
                    ticketReq.setStageId(stageId + 1);
                    ticketReq.setMemberId(member.getId());
                    ticketReq.setPlId(order.getId());
                    ticketReq.setTitle(BizMessageSource.getInstance().getMessage("cem12003"));
                    ticketReq.setType(MemberBoxTicketTypeEnum.income.value());
                    ticketReq.setQuantity(ticketNum);
                    memberFeignClient.handelMemberBoxTicket(ticketReq);
                }



            }
        } catch (Exception e) {
            e.printStackTrace();
            //退款到余额
            try {
                failHandel(order, boxItemId,spuId, stageId,num);
            } catch (Exception ex) {
                log.error("闯关盲盒支付异常，退还到余额 失败 orderId = {},boxItemId = {},报错信息 = {}",order.getId(),boxItemId,ex.getMessage());
                ex.printStackTrace();
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId + "@" + stageId,redisCount);
            log.error("闯关盲盒抽奖报错 = {}",e.getMessage());
            throw e;

        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            orderLocal.remove();
        }

    }


    private void handleBuyAdventureBoxSuccess(BuyBoxSuccessEvent event){
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        JSONObject extJson = event.getOrderExtJson();
        Long   boxItemId = extJson.getLong("boxItemId");

        Integer  num = extJson.getInteger("num");

        Long spuId = extJson.getLong("spuId");

        String boxType = extJson.getString("boxType");
        Long stageId = extJson.getLong("stageId");
        Long categoryId = extJson.getLong("categoryId");

        Date now = DateFormatUtils.getNow();

        int redisCount = 0;

        List<MaBoxItemRewardVo> itemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId,stageId);



        try {

            SaasQueryBuilder queryBox = SaasQueryBuilder.where(Restrictions.eq("spuId",spuId));
            ProductBox productBox = dao.query(queryBox,ProductBox.class);
            String serialNumKey = BOX_COUNTER+boxItemId + "@" + stageId;
            //抽奖最后位置
            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(serialNumKey);
            if (currentRewardNum == null || currentRewardNum == 0) {
                currentRewardNum = boxService.queryBoxItemCount(boxItemId,stageId);

                redisCacheProvider.getRedisTemplate().opsForValue().set(serialNumKey,currentRewardNum,1, TimeUnit.DAYS);
            }else {
                //重置过期时间
                redisCacheProvider.getRedisTemplate().expire(serialNumKey,1, TimeUnit.DAYS);
            }

            //总概率 可以支持概率总和不等一
            Double sumOdds = itemRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();

            //计算每个奖品的概率区间
            //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
            List<Double> rewardOddsList = new ArrayList<>();
            List<Double> sortRewardOddsList = new ArrayList<>() ;
            Double tempOdds = 0d;
            for (MaBoxItemRewardVo itemRewardVo : itemRewardVos) {
                tempOdds += itemRewardVo.getOdds();
                rewardOddsList.add(tempOdds / sumOdds);
            }
            List<MaBoxItemRewardVo> winRewards = new ArrayList<>();
            MaBoxItemRewardVo rewardVo;
                //随机数在哪个概率区间内，则是哪个奖品
            double randomDouble = Math.random();
            sortRewardOddsList.addAll(rewardOddsList);
            //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
            sortRewardOddsList.add(randomDouble);
            Collections.sort(sortRewardOddsList);
            int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);
            rewardVo = new MaBoxItemRewardVo();
            //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
            BeanUtils.copyProperties(itemRewardVos.get(lotteryIndex), rewardVo);

            currentRewardNum = redisCacheProvider.getRedisTemplate().opsForValue().increment(serialNumKey).intValue();
            redisCount++;
            rewardVo.setSerialNum(currentRewardNum);
            sortRewardOddsList.clear();
            winRewards.add(rewardVo);


            OrderBoxItem orderBoxItem;
            MemberReward memberReward;
            MemberBoxVo member = this.queryMember(order.getMemberId());
            orderBoxItem = new OrderBoxItem();
            BeanUtils.copyProperties(rewardVo,orderBoxItem);
            orderBoxItem.setPaymentMethod(orderLocal.get().getPaymentMethod());
            orderBoxItem.setOrderId(order.getId());
            orderBoxItem.setMemberId(order.getMemberId());
            orderBoxItem.setMemberName(member.getMemberName());
            orderBoxItem.setHeadImage(member.getHeadImage());
            orderBoxItem.setMemberTitleImage(member.getMemberTitleImage());
            orderBoxItem.setBoxItemId(boxItemId);
            orderBoxItem.setCreated(now);
            orderBoxItem.setRewardPriority(rewardVo.getRewardPriority());
            orderBoxItem.setId(idWorker.nextId());
            if (rewardVo.getCategoryPic() == null) {
                    orderBoxItem.setCategoryPic("");
            }
            dao.save(orderBoxItem);
                //保存会员赏袋
            memberReward = new MemberReward();
            BeanUtils.copyProperties(rewardVo,memberReward);
            memberReward.setOrderId(order.getId());
            memberReward.setMemberId(order.getMemberId());
            memberReward.setBoxItemId(boxItemId);
            memberReward.setCreated(now);
            memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
            memberReward.setReceiveWay(getRewardReceiveWay(order.getPaymentMethod()));
            memberReward.setSpuId(rewardVo.getSpuItemId());
            dao.save(memberReward);

            ProductBoxItem productBoxItem = dao.queryById(boxItemId,ProductBoxItem.class);


            if (productBox != null && BooleanUtils.isTrue(productBox.getIsProduceFraction())){
                //会员积分处理
                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
            }
            this.handleTradeFlow(order,BigDecimal.ZERO);
            this.saveOrderBoxItem2Es(Arrays.asList(orderBoxItem));

            Long nextStageId = stageId  ;
            boolean isAdventure = false;
            if (AdventureTypeEnum.S_AP_FIRST_ONE.value().equals(rewardVo.getMechanism())){
                nextStageId = 1L;
            }else if (AdventureTypeEnum.S_AP_FORWARD_ONE.value().equals(rewardVo.getMechanism())){
                isAdventure =true;
                nextStageId = nextStageId + 1L;
            }else if (AdventureTypeEnum.S_AP_FORWARD_STAY.value().equals(rewardVo.getMechanism())){
                log.info("停留本关 关卡不动");
            }else if (AdventureTypeEnum.S_AP_STEP_BACK_ONE.value().equals(rewardVo.getMechanism()) && stageId.compareTo(1L) > 0){
                nextStageId = nextStageId - 1L;
            }
            //更新关卡数
            Ssqb updateMemberBoxStage = Ssqb.create("com.lewei.ma.box.updateMemberBoxStage")
                    .setParam("boxItemId",boxItemId)
                    .setParam("memberId",member.getId())
                    .setParam("stageId",nextStageId);
            dao.updateByMybatis(updateMemberBoxStage);

            //增加到达关卡数
           if (isAdventure){
               MemberBoxTicketReq ticketReq = new MemberBoxTicketReq();
               ticketReq.setBoxItemId(boxItemId);
               ticketReq.setStageId(nextStageId);
               ticketReq.setMemberId(member.getId());
               ticketReq.setPlId(order.getId());
               ticketReq.setTitle(BizMessageSource.getInstance().getMessage("cem12003"));
               ticketReq.setType(MemberBoxTicketTypeEnum.income.value());
               ticketReq.setQuantity(1);
               memberFeignClient.handelMemberBoxTicket(ticketReq);
           }

            if (ListUtil.isNotEmpty(winRewards)){
                if (!OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(order.getPaymentMethod())){
                    applicationEventPublisher.publishEvent(new RewardScoreEvent(this,order.getMemberId(),order.getChainId(),order.getOrderTitle(),winRewards));
                }


                //分销处理
                handleDist(order);
                //抽奖赠送处理
                applicationEventPublisher.publishEvent(new RewardGiftEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),stageId));
                //藏宝阁
                applicationEventPublisher.publishEvent(new TreasureEvent(this,order.getId(),boxItemId,order.getMemberId(),order.getChainId(),winRewards.size(),stageId));
                //明信片处理
                if (OrderUtil.isMoneyPay(order.getPaymentMethod())){
                    //明信片处理
                    applicationEventPublisher.publishEvent(new PostcardEvent(this,order.getMemberId(),order.getChainId(),winRewards.size(),productBoxItem.getPriceFee(),order.getPaymentMethod()));
                    //宝箱活动处理
                    List<Long> orderIds = new ArrayList<Long>();
                    orderIds.add(order.getId());
                    applicationEventPublisher.publishEvent(new TreasureActivityEvent(this,order.getMemberId(),order.getChainId(),orderIds));
                }

                //弹幕
                applicationEventPublisher.publishEvent(new BulletChatEvent(this,winRewards,member,order.getChainId(),boxType,boxItemId,spuId));
                //会员每日成本
                applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,Collections.singletonMap(member,winRewards),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod()));
                //处理盲盒图鉴
                applicationEventPublisher.publishEvent(new BoxBookEvent(this,Collections.singletonMap(member,winRewards),spuId,order.getChainId(),MDCTraceUtils.getTraceId()));

            }
        } catch (Exception e) {
            e.printStackTrace();
            //退款到余额
            try {
                failHandel(order, boxItemId,spuId, stageId,num);
            } catch (Exception ex) {
                log.error("闯关盲盒支付异常，退还到余额 失败 orderId = {},boxItemId = {},报错信息 = {}",order.getId(),boxItemId,ex.getMessage());
                ex.printStackTrace();
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId + "@" + stageId,redisCount);
            log.error("闯关盲盒抽奖报错 = {}",e.getMessage());
            throw e;

        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            orderLocal.remove();
        }

    }



    private MaBoxItemRewardVo getOddsReward(List<MaBoxItemRewardVo> itemRewardVos, List<Double> rewardOddsList) {
        MaBoxItemRewardVo rewardVo;
        List<Double> sortRewardOddsList = new ArrayList<>() ;

        //随机数在哪个概率区间内，则是哪个奖品
        double randomDouble = Math.random();
        sortRewardOddsList.addAll(rewardOddsList);
        //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
        sortRewardOddsList.add(randomDouble);
        Collections.sort(sortRewardOddsList);
        int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);
        rewardVo = new MaBoxItemRewardVo();
        //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
        BeanUtils.copyProperties(itemRewardVos.get(lotteryIndex), rewardVo);
        sortRewardOddsList.clear();
        return rewardVo;
    }

    private MaBoxItemRewardVo saveMemberReward(MemberBoxVo member, Long orderId, Long boxItemId, Date now, Long winRewardId,Integer loc,Map<Long,MaBoxItemRewardVo> boxItemRewardVoMap) {
        Long memberId = member.getId();
        MaBoxItemRewardVo reward = boxItemRewardVoMap.get(winRewardId);

        OrderBoxItem orderBoxItem;
        MemberReward memberReward;

        orderBoxItem = new OrderBoxItem();
        BeanUtils.copyProperties(reward, orderBoxItem);
        orderBoxItem.setPaymentMethod(orderLocal.get().getPaymentMethod());
        orderBoxItem.setOrderId(orderId);
        orderBoxItem.setMemberId(memberId);
        orderBoxItem.setHeadImage(member.getHeadImage());
        orderBoxItem.setMemberName(member.getMemberName());
        orderBoxItem.setMemberTitleImage(member.getMemberTitleImage());
        orderBoxItem.setBoxItemId(boxItemId);
        orderBoxItem.setRewardPriority(reward.getRewardPriority());
        orderBoxItem.setSerialNum(loc);

        orderBoxItem.setCreated(now);

        if (ProductEnum.S_ST_TREASURE.value().equals(reward.getSpuType())){
            orderBoxItem.setPrimeCostFee(BigDecimal.ZERO);
        }
        dao.save(orderBoxItem);

        //保存会员赏袋
        memberReward = new MemberReward();
        BeanUtils.copyProperties(reward, memberReward);
        memberReward.setOrderId(orderId);
        memberReward.setMemberId(memberId);
        memberReward.setBoxItemId(boxItemId);
        memberReward.setCreated(now);
        if (ProductEnum.S_ST_TREASURE.value().equals(reward.getSpuType())){
            memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNBOXED.value());
        }else {
            memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
        }
        memberReward.setReceiveWay(getRewardReceiveWay(orderLocal.get().getPaymentMethod()));
        memberReward.setSpuId(reward.getSpuItemId());
        dao.save(memberReward);

        this.saveOrderBoxItem2Es(Collections.singletonList(orderBoxItem));
        if (rewardLocalList.get().get(member) == null) {
            List<MaBoxItemRewardVo> rewardVoList = new ArrayList<>();
            rewardVoList.add(reward);
            rewardLocalList.get().put(member, rewardVoList);
        }else {
            rewardLocalList.get().get(member).add(reward);
        }

        return reward;
    }




    private void stageGift(Long boxItemId, List<BoxStageVo> stageVos, int currentRewardNum,int rewardTotalNum,Long chainId, Long orderId,Date now ,Map<Long,MaBoxItemRewardVo> boxItemRewardVoMap) {

        Random random = new Random();
        log.debug("【执行】线程：{}, orderId {} ====> stageGift  stageVos={}, currentRewardNum={}, rewardTotalNum={}"
                , Thread.currentThread().getName(), orderId
                ,stageVos,currentRewardNum,rewardTotalNum);
        //阶段结束派奖
        if (ListUtil.isNotEmpty(stageVos)){

            stageVos = stageVos.stream().filter(a->a.getEndStage() == currentRewardNum).collect(Collectors.toList());
            if (ListUtil.isNotEmpty(stageVos)){

                log.debug("【执行】线程：{}, orderId {} ====>  stageGift  开始执行 ");


                for (BoxStageVo stageVo : stageVos) {
                    int leftRewardNum = rewardTotalNum - stageVo.getBgStage() + 1;

                    int k = -1;


                    //查询当前阶段所有抽奖人
                    List<LotteryMemberVo> memberVos = this.queryStageRewardMemberIdsV2(boxItemId,stageVo);

                    int stageLeftReward = stageVo.getEndStage() - stageVo.getBgStage() + 1;

                    List<MaBoxRewardRuleVo> stageRules= this.queryLeftProductBoxRules(boxItemId,stageVo.getStageId(),true, null,null);
                    List<MaBoxRewardRuleVo> randomGifts =  stageRules.stream().filter(a-> BooleanUtils.isTrue(a.getIsGift()) && Objects.equals(BoxRewardRuleEnum.S_PBRR_RANDOM.value(),a.getRuleType())).collect(Collectors.toList());
                    List<MaBoxRewardRuleVo> lastGifts =  stageRules.stream().filter(a-> BooleanUtils.isTrue(a.getIsGift()) && Objects.equals(BoxRewardRuleEnum.S_PBRR_LAST.value(),a.getRuleType())).collect(Collectors.toList());

                    Map<Long,List<Long>> mapping =  new HashMap<>();

                    for (int i1 = 0; i1 < memberVos.size(); i1++) {

                        boolean getGift = false;

                        //处理赠品逻辑
                        List<Long> rewardIds = new ArrayList<>();
                        int lastGiftNum = lastGifts.stream().mapToInt(MaBoxRewardRuleVo::getLeftQuantity).sum();

                        randomGifts = randomGifts.stream().filter(a->a.getLeftQuantity() > 0).collect(Collectors.toList());

                        if (lastGiftNum > 0 && lastGiftNum ==  stageLeftReward){
                            int randomNum = random.nextInt(lastGiftNum);
                            int item = 0;
                            for (MaBoxRewardRuleVo lastGift : lastGifts) {

                                item += lastGift.getLeftQuantity();
                                if (randomNum < item){
                                    this.updateReward(lastGift.getId(),lastGift.getRewardId(),1,rewardIds);
                                    lastGift.setLeftQuantity(lastGift.getLeftQuantity() - 1);
                                    handleMemberRewardMap(mapping,memberVos.get(i1).getMemberId(),rewardIds);
                                    getGift = true;
                                    break;
                                }
                            }
                        }else {
                            //如果阶段随机赠品不为空，抽取赠品


                            if (ListUtil.isNotEmpty(randomGifts)){
                                int randomNum = random.nextInt(stageLeftReward - lastGiftNum);
                                int item = 0;
                                for (MaBoxRewardRuleVo randomGift : randomGifts) {
                                    //不在中奖序号内直接跳出循环
                                    if (randomGift.getBgStage() != null && randomGift.getEndStage() != null){
                                        //当前位置
                                        int  locNum  =  i1+stageVo.getBgStage();
                                        //不在阶段初始化k 跳出循环
                                        if (!(randomGift.getBgStage() <= locNum && locNum <= randomGift.getEndStage())){
                                            k = -1;
                                            break;
                                        }

                                        randomNum = random.nextInt(randomGift.getEndStage() - locNum + 1);
                                        k++;
                                        //小阶段结束 初始化k
//                                        if (i1 == randomGift.getEndStage() -1){
//                                            k = -1;
//                                        }

                                    }
                                    item += randomGift.getLeftQuantity();
                                    if (randomNum < item){
                                        this.updateReward(randomGift.getId(),randomGift.getRewardId(),1,rewardIds);
                                        randomGift.setLeftQuantity(randomGift.getLeftQuantity() - 1);
                                        handleMemberRewardMap(mapping,memberVos.get(i1).getMemberId(),rewardIds);
                                        getGift  = true;
                                        break;
                                    }
                                }
                            }
                        }
                        stageLeftReward --;
                        leftRewardNum --;

                        if (getGift){
                            MemberBoxVo memberBoxVo = this.queryMember(memberVos.get(i1).getMemberId());
                            this.saveMemberReward(memberBoxVo,0L,boxItemId,now,rewardIds.get(0),memberVos.get(i1).getSerialNum(),boxItemRewardVoMap);
                        }
                    }


                    handelOtherGitfs(boxItemId, boxItemRewardVoMap, stageRules, mapping,stageVo.getBgStage(),stageVo.getEndStage());

                    handelAfterGift( chainId, mapping,boxItemRewardVoMap);
                }
            }

        }
    }

    private void handelAfterGift(Long chainId, Map<Long, List<Long>> mapping, Map<Long,MaBoxItemRewardVo> boxItemRewardVoMap) {
        if (MapUtils.isNotEmpty(mapping)){
            mapping.forEach((k,rewardIds)->{
                MemberBoxVo member = this.queryMember(k);
                //保存奖品信息
                List<MaBoxItemRewardVo> rewards =   getWinRewards(boxItemRewardVoMap,rewardIds);
                if (ListUtil.isNotEmpty(rewards)){

                    try {
                        String title = "";
                        if (rewards.size() == 1){
                            title = rewards.get(0).getName();
                        }else {
                            title = rewards.get(0).getName() ;
                            if (title != null) {
                                title = title + "...";
                            }
                        }
                        applicationEventPublisher.publishEvent(new RewardScoreEvent(this,k, chainId,title,rewards));

                    } catch (Exception e1) {
                        log.error("stageGift member score  fail", e1);
                    }

                    try {
                        sendSMS(member,rewards);
                    } catch (Exception e) {
                        log.error("send reward sms fail", e);
                    }
                }
            });
        }
    }


    private void handleAllRandReward(Random random, Long boxItemId, List<Long> winRewardIds,Long memberId,Integer num) {
        //查询全随机随机池中奖品
        List<MaBoxRewardRuleVo> randomRewards = this.queryLeftProductBoxRules(boxItemId,null,false, BoxRewardRuleEnum.S_PBRR_RANDOM.value(),null);

            if (ListUtil.isNotEmpty(randomRewards)){

                boolean isSweet = this.sweetHandelFour(memberId,randomRewards,num,winRewardIds);
                if (!isSweet){
                    
                   randomRewards = randomRewards.stream().filter(a->a.getStageId() == 0L).collect(Collectors.toList());
                   if (ListUtil.isNotEmpty(randomRewards)){
                       //可能获取的奖品总数
                       int randomRewardNum = randomRewards.stream().mapToInt(MaBoxRewardRuleVo::getLeftQuantity).sum();
                       int randomNum = random.nextInt(randomRewardNum);
                       int item = 0;
                       for (MaBoxRewardRuleVo rule : randomRewards) {
                           item += rule.getLeftQuantity();
                           if (randomNum < item){
                               this.updateReward(rule.getId(),rule.getRewardId(),1,winRewardIds);
                               break;
                           }
                       }
                   }
                }
            }
        }


    private  int getCurrentRewardNum(MaBoxItemDetailVo detailVo, Long orderId) {

        log.debug("【执行】线程：{}, orderId {} ====> 盲盒详情数据={}", Thread.currentThread().getName(),
                orderId, JSON.toJSONString(detailVo));
        if (StringUtils.isNotBlank(detailVo.getBoxPrizeMode()) && detailVo.getBoxPrizeMode().equals(BoxPrizeModeEnum.S_BPM_ODDS.value())) {
            return detailVo.getRewardNum() - detailVo.getLeftRewardNum();
        }
        int leftRewardNum = detailVo.getRewards().stream().filter(a -> !a.getIsGift()).mapToInt(MaBoxItemDetailVo.BoxItemRewardVo::getLeftQuantity).sum();
        return detailVo.getRewardNum() - leftRewardNum;

    }

    private List<MaBoxRewardRuleVo> queryLeftProductBoxRules(Long boxItemId,Long stageId,Boolean isGift,String ruleType, Long neStageId){

        Ssqb queryLeftProductBoxRules = Ssqb.create("com.lewei.ma.box.queryLeftProductBoxRules")
                .setParam("boxItemId",boxItemId)
                .setParam("stageId",stageId)
                .setParam("neStageId",neStageId)
                .setParam("isGift",isGift)
                .setParam("ruleType",ruleType)
                ;
        return dao.findForList(queryLeftProductBoxRules, MaBoxRewardRuleVo.class);
    }


    private int updateRewardLeftQuantity(Long rewardId,int subtractNum){
        Ssqb updateRewardLeftQuantity = Ssqb.create("com.lewei.ma.box.updateBoxItemRewardLeftQuantity")
                .setParam("rewardId",rewardId)
                .setParam("num",subtractNum);
        return dao.updateByMybatis(updateRewardLeftQuantity);

    }

    private int updateRewardRuleLeftQuantity(Long ruleId,int subtractNum){
        Ssqb updateRewardRuleLeftQuantity = Ssqb.create("com.lewei.ma.box.updateBoxItemRewardRuleLeftQuantity")
                .setParam("ruleId",ruleId)
                .setParam("num",subtractNum);
        return dao.updateByMybatis(updateRewardRuleLeftQuantity);

    }

    private boolean handleAllRandomGift(Long boxItemId, int leftRewardNum, Random random,List<Long> winRewardIds){

        //查询全随机随机池中赠品
        List<MaBoxRewardRuleVo> randomGifts = this.queryLeftProductBoxRules(boxItemId,0L,true, BoxRewardRuleEnum.S_PBRR_RANDOM.value(),null);

        if (ListUtil.isNotEmpty(randomGifts)){
            List<MaBoxRewardRuleVo> stageGifts = this.queryLeftProductBoxRules(boxItemId,null,true, null,0L);

            int stageGiftNum = 0;
            if (ListUtil.isNotEmpty(stageGifts)){
                stageGiftNum = stageGifts.stream().mapToInt(MaBoxRewardRuleVo::getLeftQuantity).sum();
            }

            //随机规则为 剩余奖品数 - 阶段赠品数

            int r = random.nextInt(leftRewardNum - stageGiftNum);
            int item = 0;
            for (MaBoxRewardRuleVo gift : randomGifts) {
                item += gift.getLeftQuantity();
                if (item > r){
                    return  this.updateReward(gift.getId(),gift.getRewardId(),1,winRewardIds);
                }
            }
        }
        return false;
    }



    private boolean updateReward(Long ruleId,Long rewardId,int subtractNum,List<Long> winRewardIds){
        //更新 剩余数量
        int l1 = this.updateRewardRuleLeftQuantity(ruleId,subtractNum);
        int l2 = this.updateRewardLeftQuantity(rewardId,subtractNum);
        if (l1 > 0 && l2 > 0){
            //获得奖品id
            winRewardIds.add(rewardId);
            return true;
        }else {
            if (l1 <= 0){
                log.error("handleBuyBoxSuccessListen.updateRewardRuleLeftQuantity fail (ruleId = {},orderId = {})",ruleId,orderLocal.get().getId());
            }
            if (l2 <= 0){
                log.error("handleBuyBoxSuccessListen.updateRewardLeftQuantity fail (rewardId = {},orderId = {})", rewardId,orderLocal.get().getId());
            }
        }
        return false;
    }

    private void sendSMS(MemberBoxVo member, List<MaBoxItemRewardVo> rewards){

        String traceId = MDCTraceUtils.getTraceId();
        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                XcrmThreadContext.setChainId(member.getChainId());
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                MDCTraceUtils.putTraceId(traceId);
                long begin = System.currentTimeMillis();
                try {
                    List<Long> applyCategoryIds = queryLotteryRemindConfigCategoryIdList(member.getChainId());
                    List<Long> categoryIds = rewards.stream().map(MaBoxItemRewardVo::getCategoryId).collect(Collectors.toList());
                    if (applyCategoryIds.contains(0L) || !Collections.disjoint(applyCategoryIds,categoryIds)){

                        String productNames = rewards.stream().map(MaBoxItemRewardVo::getName).collect(Collectors.joining("，"));
                        if (StringUtils.isNotEmpty(member.getMobile())){
                            try {

                                List<String> paramList = new ArrayList<>();
                                paramList.add(member.getMemberName());
                                paramList.add(productNames);
                                SmsRequest request = new SmsRequest();
                                request.setMobiles(member.getMobile());
                                request.setParam(Joiner.on(",").skipNulls().join(paramList));
                                request.setTmplEvent(TmplEvent.win_lottery_remind.value());
                                request.setTmplType(TmplTypeEnum.consume.value());
                                messageFeignClient.sendMessage(request);
                            } catch (Exception e) {
                                log.error("sendSMS error", e);
                            }
                        }

                        Member member1 = dao.queryById(member.getId(), Member.class);
                         if (member1 != null && StringUtils.isNotBlank(member1.getAppChannel())){
                            try {
                                log.info("当前是APP消息推送");
                                AppMsgRequest request = new AppMsgRequest();
                                request.setEvent(TmplEvent.win_lottery_remind.value());
                                request.setChannels(member1.getAppChannel());
                                List<String> msgBodyParams = new ArrayList<>();
                                msgBodyParams.add(member.getMemberName());
                                StringBuffer stringBuffer = new StringBuffer();
                                if (!CollectionUtils.isEmpty(rewards)){
                                    stringBuffer.append(rewards.get(0).getName());
                                        if (rewards.size() > 1) {
                                            stringBuffer.append("等多件商品");
                                        }
                                        if (rewards.size() == 1) {
                                            stringBuffer.append("商品");
                                        }
                                    msgBodyParams.add(stringBuffer.toString());
                                }
                                request.setMsgBodyParams(msgBodyParams);
                                Map<String,String> map2 = Collections.emptyMap();
                                request.setContent(map2);
                                messageFeignClient.sendAppMessage(request);
                            } catch (Exception e) {
                                log.error("sendAppMessage error", e);
                            }
                        }

                        long cost = System.currentTimeMillis() - begin;
                        if(cost > boxConfig.getMaxSubThreadCostMs()) {
                            log.info("【抽奖服务子流程缓慢】{}ms sendSMS", cost);
                        }

                        if(boxConfig.isEnableWxMessage()) {
                            long beginWxMessage = System.currentTimeMillis();
                            try {
                                AccessToken accessToken = accessTokenService.queryDefaultToken(member.getChainId());
                                if (accessToken != null) {
                                    MemberMaRef memberMaRef = accessTokenService.queryMemberRef(member.getId(),accessToken.getAppId());
                                    if (memberMaRef != null) {
                                        WxMsgRequest builder = WxMsgRequest.event(TmplEvent.win_lottery_remind.value())
                                                .appId(accessToken.getAppId())
                                                .accessToken(accessToken.getToken())
                                                .openId(memberMaRef.getOpenId())
                                                .put("result","恭喜您中奖了")
                                                .put("rewardName",productNames)
                                                .put("date",DateZonetimeUtils.formatDate(new Date(),"yyyy-MM-dd hh:mm:ss",globalHandler.getGlobalData().getZone()))
                                                .page("pages/start/start?pageKey=bag")
                                                ;
                                        messageFeignClient.sendWxMessage(builder);
                                    }
                                }
                            } catch (Exception e) {
                                log.error("sendWxMessage error", e);
                            }

                            cost = System.currentTimeMillis() - beginWxMessage;
                            if(cost > boxConfig.getMaxSubThreadCostMs()) {
                                log.info("【抽奖服务子流程缓慢】{}ms sendWxMessage", cost);
                            }
                        }

                        log.info("sendMessage cost {} ms", System.currentTimeMillis() - begin);
                    }

                } catch (Exception e) {
                    log.error("sms error ",e);
                } finally {
                    XcrmThreadContext.removeChainId();
                    XcrmThreadContext.removeAccessType();
                    MDCTraceUtils.removeTraceId();
                }

            }
        });
    }

    private List<Long> queryLotteryRemindConfigCategoryIdList(Long chainId) {
        String config= (String) redisCacheProvider.getRedisTemplate().opsForHash().get(MaCacheKeys.MESSAGE_CUSTOM_KEY,chainId.toString());
        if (config == null){
            //查询数据库的值
            MessageCustomConfigVo customConfigVo = appFeignClient.queryCustomMessageConfigVo(TmplEvent.win_lottery_remind.value());
            if (customConfigVo == null || customConfigVo.getPropJsonStr() == null){
                Map<String,Object> map = new HashMap<>();
                map.put("categoryIds",0);
                config = JSON.toJSONString(map);
                redisCacheProvider.getRedisTemplate().opsForHash().put(MaCacheKeys.MESSAGE_CUSTOM_KEY,chainId.toString(),config);
            }else {
                config = customConfigVo.getPropJsonStr();
                redisCacheProvider.getRedisTemplate().opsForHash().put(MaCacheKeys.MESSAGE_CUSTOM_KEY,chainId.toString(),config);
            }
        }
        JSONObject configObj = JSON.parseObject(config);
        String categoryIdStr = configObj.getString("categoryIds");
        //配置为空 默认全部适用
        if (StringUtils.isEmpty(categoryIdStr)){
            return Collections.singletonList(0L);
        }
        return Arrays.stream(categoryIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
    }


    private List<LotteryMemberVo> queryStageRewardMemberIds(Long boxItemId){
        Ssqb ssqb = Ssqb.create("com.lewei.ma.box.queryStageRewardMemberIds")
                .setParam("boxItemId",boxItemId);
        return dao.findForList(ssqb,LotteryMemberVo.class);
    }


    private List<LotteryMemberVo> queryStageRewardMemberIdsV2(Long boxItemId,BoxStageVo stageVo){
        Ssqb ssqb = Ssqb.create("com.lewei.ma.box.queryStageRewardMemberIdsV2")
                .setParam("boxItemId",boxItemId)
                .setParam("bgStage",stageVo.getBgStage())
                .setParam("endStage",stageVo.getEndStage())
                ;
        return dao.findForList(ssqb,LotteryMemberVo.class);
    }

    private MemberBoxVo queryMember(Long memberId) {
        MemberBoxVo memberBoxVo;
        QueryBuilder queryBuilder = QueryBuilder.create("com.lewei.ma.box.queryMemberInBox").setParam("memberId", memberId);
        memberBoxVo = dao.findForObj(queryBuilder, MemberBoxVo.class);
        if (memberBoxVo != null && memberBoxVo.getFrameId() != null) {
            SaasQueryBuilder queryFrame = SaasQueryBuilder.where(Restrictions.eq("id", memberBoxVo.getFrameId()))
                    .and(Restrictions.eq("dataStatus", 1));
            MemberPictureFrame frame = dao.query(queryFrame, MemberPictureFrame.class);
            if (frame == null) {
                memberBoxVo.setFrameImage("");
            }
        }
        return memberBoxVo;
    }


    public void handleMemberRewardMap(Map<Long,List<Long>> map, Long memberId,List<Long> rewardIds){
        List<Long> rewardIdlist = new ArrayList<>(rewardIds);
        if (map.get(memberId) == null){
            map.put(memberId,rewardIdlist);
        }else {
            map.get(memberId).addAll(rewardIdlist);
        }

    }

    private Map<Long,MaBoxItemRewardVo> queryBoxItemRewardMap(Long boxItemId){
        List<MaBoxItemRewardVo> boxItemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId);
        return boxItemRewardVos.stream().collect(Collectors.toMap(MaBoxItemRewardVo::getRewardId, c->c));
    }

    private void updateOrder(Order order){

        String lang = XcrmThreadContext.getLocale();


        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                XcrmThreadContext.setChainId(order.getChainId());
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                XcrmThreadContext.setLocale(lang);

                try {
                    dao.update(order);
                } catch (Exception e) {
                    log.error("updateOrder", e);
                } finally {
                    XcrmThreadContext.removeChainId();
                    XcrmThreadContext.removeAccessType();
                    XcrmThreadContext.getLocale();
                }

            }
        });
    }

    private void handleDist(Order order) {

        String traceId = MDCTraceUtils.getTraceId();

        taskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    long begin = System.currentTimeMillis();
                    XcrmThreadContext.setTenantId(order.getTenantId());
                    XcrmThreadContext.setChainId(order.getChainId());
                    XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                    MDCTraceUtils.putTraceId(traceId);
                    try {
                        if(OrderBizTypeEnum.S_OBZ_DIST.value().equals(order.getOrderBizType())) {
                            OrderTransPreProfitRequest profitRequest = new OrderTransPreProfitRequest();
                            profitRequest.setOrderId(order.getId());
                            //发起分账核算处理
                            memberFeignClient.preHandleOrderTransProfit(profitRequest);
                            long cost = System.currentTimeMillis() - begin;
                            log.info("handleDist  cost = {} ms", cost);
                            if(cost > boxConfig.getMaxSubThreadCostMs()) {
                                log.info("【抽奖服务子流程缓慢】{}ms handleDist, orderId = {}", cost, order.getId());
                            }
                        }
                    } catch (Exception e) {
                        log.error("handleDist error", e);
                    }

                    try {
                        begin = System.currentTimeMillis();
                        memberFeignClient.handelMemberShareCommission(new MemberShareCommissionReq(order.getId()));
                        long cost = System.currentTimeMillis() - begin;
                        log.info("handle member commission cost = {} ms", cost);
                        if(cost > boxConfig.getMaxSubThreadCostMs()) {
                            log.info("【抽奖服务子流程缓慢】{}ms handle member commission, orderId = {}", cost, order.getId());
                        }
                    } catch (Exception e) {
                        log.error("ma member commission error", e);
                    }

                } catch (Exception e) {
                    log.error("ma dist error", e);
                } finally {
                    XcrmThreadContext.removeChainId();
                    XcrmThreadContext.removeTenantId();
                    XcrmThreadContext.removeAccessType();
                    MDCTraceUtils.removeTraceId();
                }
            }
        });
    }

    private void handleTradeFlow(Order order,BigDecimal refundFee) {

        String traceId = MDCTraceUtils.getTraceId();
        taskExecutor.execute(new Runnable() {

            @Override
            public void run() {
                try {
                    long begin = System.currentTimeMillis();
                    XcrmThreadContext.setTenantId(order.getTenantId());
                    XcrmThreadContext.setChainId(order.getChainId());
                    XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                    MDCTraceUtils.putTraceId(traceId);

                    TradeFlowReq tradeFlow = new TradeFlowReq();
                    tradeFlow.setTradeContent(order.getOrderTitle());
                    tradeFlow.setOrderId(order.getId());
                    tradeFlow.setMemberId(order.getMemberId());
                    tradeFlow.setPaymentMethod(order.getPaymentMethod());
                    tradeFlow.setTradeAmount(order.getOrderMoney());
                    tradeFlow.setTradeType(TradeTypeFactory.getTradeType(order.getOrderType()));
                    tradeFlow.setTradeSource(order.getPlatform());
                    JSONObject jsonObject = JSONObject.parseObject(order.getExtJson());
                    tradeFlow.setInviterId(jsonObject.getLong("inviterId"));
                    //流水记录抽赏的时候将赏品的spuId传入
                    if (OrderUtil.isRefundMoneyPay(order.getPaymentMethod()) &&
                            StringUtils.isNotBlank(order.getOrderType()) &&
                            order.getOrderType().equals(OrderEnum.S_OOT_BOX.value())) {
                        if (StringUtils.isNotEmpty(order.getExtJson())) {
                            JSONObject extJsonMap = JSON.parseObject(order.getExtJson());
                            tradeFlow.setSpuId(extJsonMap.getLong("spuId"));
                            tradeFlow.setRefundFee(refundFee);
                        }
                    }

                    memberFeignClient.saveTradeFlow(tradeFlow);
                    //退款金额 保存充值流水
                    if (refundFee != null && refundFee.compareTo(BigDecimal.ZERO) > 0){
                        tradeFlow = new TradeFlowReq();
                        tradeFlow.setTradeContent(BizMessageSource.getInstance().getMessage("cem12007"));
                        tradeFlow.setOrderId(order.getId());
                        tradeFlow.setMemberId(order.getMemberId());
                        tradeFlow.setPaymentMethod(order.getPaymentMethod());
                        tradeFlow.setTradeAmount(refundFee);
                        tradeFlow.setTradeType(TradeTypeEnum.S_TT_RECHARGE.value());
                        tradeFlow.setTradeSource(order.getPlatform());
                        memberFeignClient.saveTradeFlow(tradeFlow);
                    }
                    long cost = System.currentTimeMillis() - begin;
                    log.info("handleTradeFlow  cost = {} ms", cost);
                    if(cost > boxConfig.getMaxSubThreadCostMs()) {
                        log.info("【抽奖服务子流程缓慢】{}ms handleTradeFlow, orderId = {}", cost, order.getId());
                    }
                } catch (Exception e) {
                    log.error("handleTradeFlow error", e);
                } finally {
                    XcrmThreadContext.removeChainId();
                    XcrmThreadContext.removeTenantId();
                    XcrmThreadContext.removeAccessType();
                    MDCTraceUtils.removeTraceId();
                }
            }
        });
    }

    private String getRewardReceiveWay(String payType){
        if (OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(payType)){
            return RewardReceiveWayEnum.S_MRRW_SCORE.value();
        }
        if (OrderPaymentTypeEnum.S_OPM_FRACTION.value().equals(payType)){
            return RewardReceiveWayEnum.S_MRRW_FRACTION.value();
        }
        return RewardReceiveWayEnum.S_MRRW_BUY.value();
    }

    /**
     * 将记录异步存入es
     * @param list
     */
    private void saveOrderBoxItem2Es(List<OrderBoxItem> list) {

        if(list != null && list.size() > 0) {
            taskExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        List<OrderBoxItemEsEntity> orderBoxItemEsEntities = new ArrayList<>();
                        for(OrderBoxItem orderBoxItem : list) {
                            OrderBoxItemEsEntity orderBoxItemEsEntity = new OrderBoxItemEsEntity();
                            BeanUtils.copyProperties(orderBoxItem, orderBoxItemEsEntity);
                            orderBoxItemEsEntity.setDataStatus(true);
                            orderBoxItemEsEntities.add(orderBoxItemEsEntity);
                        }

                        commonEsProvider.save(orderBoxItemEsEntities);

                    } catch (Exception e) {
                        log.error("saveOrderBoxItem2Es error", e);
                    }
                }
            });
        }

    }

    private void handleMineGift( Long boxMineId,Long orderBoxItemId, MaBoxItemRewardVo reward,Long boxItemId) {
        try {
            //查询此矿难抽取最多玩家 次数一样 按选抽到计算
            Ssqb queryMineMaxMember = Ssqb.create("com.lewei.ma.box.queryMineMaxMember")
                    .setParam("boxMineId",boxMineId);
            BoxMineRecordVo recordVo = dao.findForObj(queryMineMaxMember,BoxMineRecordVo.class);
            if (recordVo != null) {
                List<Long> memberIds = new ArrayList<>();
                memberIds.add(recordVo.getMemberId());
                //双倍标记
                Ssqb saveOrderBoxItemDoubleMark = Ssqb.create("com.lewei.ma.box.saveOrderBoxItemDoubleMark")
                        .setParam("orderBoxItemId",orderBoxItemId)
                        .setParam("doubleMark",1);
                dao.updateByMybatis(saveOrderBoxItemDoubleMark);
                Map<String,Object> giftMap= new HashMap<>();
                giftMap.put("name",reward.getName());
                giftMap.put("mainImage",reward.getMainImage());
                giftMap.put("categoryName",reward.getCategoryName());
                giftMap.put("spuId",reward.getSpuItemId());
                giftMap.put("skuId",reward.getSkuItemId());
                giftMap.put("amount",1);
                giftMap.put("primeCostFee",reward.getPrimeCostFee());
                giftMap.put("price",reward.getPriceFee());


                Map<String,Object> productMap = new HashMap<>();

                productMap.put("type","product");
                productMap.put("productJson",Collections.singletonList(giftMap));

                ActivityGIftReq req = new ActivityGIftReq();
                req.setActivityId(boxItemId);
                req.setActivityType(ActivityTypeEnum.S_AT_REWARD_MINE.value());
                req.setGiftJson(JSON.toJSONString(productMap));
                req.setGiftType("product");
                req.setTitle(BizMessageSource.getInstance().getMessage("cem12005"));
                req.setMemberIds(memberIds);
                req.setMessageSubType(MaMessageSubTypeEnum.activity_mine.value());
                req.setIsSendMsg(true);

                appFeignClient.handleActivityGift(req);

                //更新矿主奖励
                ProductBoxMineRecord mineRecord = new ProductBoxMineRecord();
                mineRecord.setId(recordVo.getRecordId());
                mineRecord.setGift(JSON.toJSONString(productMap));
                dao.update(mineRecord);


            }
        } catch (Exception e) {
            log.error("handleBuyUnLimitBoxSuccess.handleMineGift error = {}",e.getMessage());
        }
    }

    private Map<String,Object> initMine(ProductBox productBox, Long boxItemId) {
        Map<String,Object> map = new HashMap<>();
        //矿难处理
        if (BooleanUtils.isTrue(productBox.getIsEnableMineDisaster())) {
            Ssqb queryProductBoxMine = Ssqb.create("com.lewei.ma.box.queryBoxMine")
                    .setParam("boxItemId", boxItemId);
            ProductBoxMine boxMine = dao.findForObj(queryProductBoxMine, ProductBoxMine.class);
            boolean isMineing = false;
            if (boxMine != null && ProductBoxMineStatusEnum.S_PBMS_JXZ.value().equals(boxMine.getMineStatus())) {
                 isMineing = true;
            } else if (boxMine == null) {
                SaasQueryBuilder queryPriority = SaasQueryBuilder.where(Restrictions.eq("boxItemId", boxItemId));
                Integer priority = dao.queryForInt(queryPriority, ProductBoxMine.class);
                if (priority == null) {
                    priority = 0;
                }
                //触发矿难 保存记录
                LocalDateTime dateNow = LocalDateTime.now();
                Date endTime = dateNow.plusMinutes(productBox.getMineTime()).toDate();
                ProductBoxMine productBoxMine = new ProductBoxMine();
                productBoxMine.setPriority(priority + 1);
//                productBoxMine.setBeTime(dateNow.toDate());
//                productBoxMine.setEndTime(endTime);
                productBoxMine.setBoxItemId(boxItemId);
                productBoxMine.setSpuId(productBox.getSpuId());
                productBoxMine.setCreated(dateNow.toDate());
                productBoxMine.setMineStatus(ProductBoxMineStatusEnum.S_PBMS_WKS.value());
                productBoxMine.setMineType(productBox.getMineType());
                dao.save(productBoxMine);
            }

            map.put("boxMine", boxMine);
            map.put("isMineing", isMineing);

        }
        return map;
    }

    private List<ProductBoxItemRewardTmpl> queryProductBoxItemRewardTmpls(Long boxItemId,Boolean isGift,Boolean isAward,Long stageId,List<Integer> serialNums){
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItemId));
        if (isGift != null) {
            query.and(Restrictions.eq("isGift",isGift));
        }
        if (isAward != null) {
            query.and(Restrictions.eq("isAward",isAward));
        }
        if (stageId != null) {
            query.and(Restrictions.eq("stageId",stageId));
        }
        if (ListUtil.isNotEmpty(serialNums)) {
            query.and(Restrictions.in("serialNum",serialNums));
        }

        return dao.queryList(query,ProductBoxItemRewardTmpl.class);
    }

    private int queryProductBoxItemRewardTmplCount(Long boxItemId,Boolean isGift,Boolean isAward,Integer bgSerialNum,Integer endSerialNum){
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItemId));
        if (isGift != null) {
            query.and(Restrictions.eq("isGift",isGift));
        }
        if (isAward != null) {
            query.and(Restrictions.eq("isAward",isAward));
        }

        if (bgSerialNum != null) {
            query.and(Restrictions.ge("serialNum",bgSerialNum));
        }
        if (endSerialNum != null) {
            query.and(Restrictions.le("serialNum",endSerialNum));
        }

        return dao.queryForInt(query,ProductBoxItemRewardTmpl.class);
    }

    private BigDecimal handelRefund(Order order, Integer num, int refundNum,Long spuId, Long boxItemId, Long stageId) {
        //下单购买数量和获取奖品数量不一致 则需要退款到零钱
        BigDecimal refundFee = BigDecimal.ZERO;
        if(refundNum > 0){
            if (OrderUtil.isRefundMoneyPay(order.getPaymentMethod())){

                if (refundNum == num){
                    refundFee = order.getPaymentMoney();
                    //退优惠券
                    if (order.getMemberCouponId() != null){
                        MemberCoupon coupon = new MemberCoupon();
                        coupon.setId(order.getMemberCouponId());
                        coupon.setMemberCouponStatus(MemberCouponStatusEnum.S_MCDS_WSY.value());
                        dao.update(coupon);
                    }

                }else {
                    if (order.getPaymentMoney().compareTo(BigDecimal.ZERO) > 0){
                        refundFee = order.getPaymentMoney().divide(BigDecimal.valueOf(num),2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(refundNum));
                    }
                }

                if (refundFee.compareTo(BigDecimal.ZERO) > 0){
                    MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                    balanceRequest.setGift(BigDecimal.ZERO);
                    balanceRequest.setBalance(refundFee);
                    balanceRequest.setMemberId(order.getMemberId());
                    balanceRequest.setPlId(order.getId());
                    balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem12001"));
                    balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                    memberFeignClient.memberBalanceHandle(balanceRequest);
                }

                order.setOrderMoney(order.getOrderMoney().subtract(refundFee));
                dao.update(order);
            }else if(OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(order.getPaymentMethod())){
                BigDecimal refundScore =order.getPayScore().divide(BigDecimal.valueOf(num),2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(refundNum));
                memberFeignClient.revertMemberScore(new RevertScoreRequest(order.getMemberId(),"",refundScore,order.getOrderTitle()+"抽奖失败退还", ScoreSourceTypeEnum.MA.value(),false));
            }else if(OrderPaymentTypeEnum.S_OPM_FRACTION.value().equals(order.getPaymentMethod())){
                BigDecimal refundFraciton =order.getPayFraction().divide(BigDecimal.valueOf(num),2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(refundNum));
                memberFeignClient.addMemberFraction(new AddFractionRequest(order.getMemberId(),"",refundFraciton,order.getOrderTitle()+"抽奖失败退还","ma"));
            }else if (OrderPaymentTypeEnum.S_OPM_TB_TICKET.value().equals(order.getPaymentMethod())){
                //查询淘宝票信息
                Ssqb queryBoxTbTicketBySpuId = Ssqb.create("com.lewei.ma.box.queryBoxTbTicketBySpuId")
                    .setParam("spuId",spuId);
                BoxTbTicketVo boxTbTicketVo = dao.findForObj(queryBoxTbTicketBySpuId, BoxTbTicketVo.class);
                if (boxTbTicketVo != null) {
                    MemberTbTicketRequest request = new MemberTbTicketRequest();
                    request.setMemberId(order.getMemberId());
                    request.setQuantity(refundNum);
                    request.setTitle(order.getOrderTitle()+"抽奖失败退还");
                    request.setType(MemberTbTicketTypeEnum.income.value());
                    request.setPlId("r"+order.getId());
                    request.setTbSkuId(boxTbTicketVo.getTbSkuId());
                    request.setTbSpuId(boxTbTicketVo.getTbSpuId());
                    memberFeignClient.handelMemberTbTicket(request);
                    refundFee = boxTbTicketVo.getPrice().multiply(BigDecimal.valueOf(refundNum));
                }

            }else if(OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(order.getPaymentMethod())){
                MemberBoxTicketReq ticketReq = new MemberBoxTicketReq();
                ticketReq.setBoxItemId(boxItemId);
                ticketReq.setStageId(stageId);
                ticketReq.setMemberId(order.getMemberId());
                ticketReq.setPlId(order.getId());
                ticketReq.setTitle(order.getOrderTitle());
                ticketReq.setType(MemberBoxTicketTypeEnum.income.value());
                ticketReq.setQuantity(num);
                memberFeignClient.handelMemberBoxTicket(ticketReq);
            }

            //更新退回数量
            Ssqb updateOrderProductRefundNum = Ssqb.create("com.lewei.ma.box.updateOrderProductRefundNum")
                    .setParam("orderId",order.getId())
                    .setParam("refundNum",refundNum);
            dao.updateByMybatis(updateOrderProductRefundNum);

        }
        return refundFee;
    }

    private void failHandel(Order order, Long boxItemId, Long spuId, Long stageId,Integer num) {

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        Order newOrder = new Order();
        newOrder.setId(order.getId());
        newOrder.setChainId(order.getChainId());
        newOrder.setOrderMoney(BigDecimal.ZERO);
        this.updateOrder(newOrder);
        if (OrderPaymentTypeEnum.S_OPM_BOX_TICKET.value().equals(order.getPaymentMethod())){
            MemberBoxTicketReq ticketReq = new MemberBoxTicketReq();
            ticketReq.setBoxItemId(boxItemId);
            ticketReq.setStageId(stageId);
            ticketReq.setMemberId(order.getMemberId());
            ticketReq.setPlId(order.getId());
            ticketReq.setTitle(BizMessageSource.getInstance().getMessage("cem12002"));
            ticketReq.setType(MemberBoxTicketTypeEnum.income.value());
            ticketReq.setQuantity(1);
            memberFeignClient.handelMemberBoxTicket(ticketReq);
        }else if(OrderPaymentTypeEnum.S_OPM_SCORE.value().equals(order.getPaymentMethod())){
            BigDecimal refundScore = order.getPayScore();
            memberFeignClient.revertMemberScore(new RevertScoreRequest(order.getMemberId(),"",refundScore, order.getOrderTitle() + BizMessageSource.getInstance().getMessage("cem12006"), ScoreSourceTypeEnum.MA.value(),false));
        }else if(OrderPaymentTypeEnum.S_OPM_FRACTION.value().equals(order.getPaymentMethod())){
            BigDecimal refundFraciton = order.getPayFraction();
            memberFeignClient.addMemberFraction(new AddFractionRequest(order.getMemberId(),"",refundFraciton, order.getOrderTitle() + BizMessageSource.getInstance().getMessage("cem12006"),"ma"));
        }else if (OrderPaymentTypeEnum.S_OPM_TB_TICKET.value().equals(order.getPaymentMethod())){
            //查询淘宝票信息
            Ssqb queryBoxTbTicketBySpuId = Ssqb.create("com.lewei.ma.box.queryBoxTbTicketBySpuId")
                    .setParam("spuId",spuId);
            BoxTbTicketVo boxTbTicketVo = dao.findForObj(queryBoxTbTicketBySpuId, BoxTbTicketVo.class);
            if (boxTbTicketVo != null) {
                MemberTbTicketRequest request = new MemberTbTicketRequest();
                request.setMemberId(order.getMemberId());
                request.setQuantity(num);
                request.setTitle(order.getOrderTitle()+"抽奖失败退还");
                request.setType(MemberTbTicketTypeEnum.income.value());
                request.setPlId("r"+order.getId());
                request.setTbSkuId(boxTbTicketVo.getTbSkuId());
                request.setTbSpuId(boxTbTicketVo.getTbSpuId());
                memberFeignClient.handelMemberTbTicket(request);
            }

        }else {
            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
            balanceRequest.setGift(BigDecimal.ZERO);
            balanceRequest.setBalance(order.getPaymentMoney());
            balanceRequest.setMemberId(order.getMemberId());
            balanceRequest.setPlId(order.getId());
            balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem12001"));
            balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
            memberFeignClient.memberBalanceHandle(balanceRequest);
        }
    }

    /**
     * sweet 补偿处理
     * @param memberId
     * @param itemRewardVos
     * @param winRewards
     */
    private void sweetHandel(String paymentMethod,Long  memberId, List<MaBoxItemRewardVo> itemRewardVos, List<MaBoxItemRewardVo> winRewards) {
        try {

            if (OrderUtil.isMoneyPay(paymentMethod)){
                //查询记录 每天补偿一次

                Date date = globalHandler.getChainLocalDate();
                Ssqb queryMemberDayProfit = Ssqb.create("com.lewei.ma.box.queryMemberDayProfit")
                        .setParam("memberId", memberId);
                BigDecimal profit = dao.findForObj(queryMemberDayProfit, BigDecimal.class);
                if (profit != null && profit.compareTo(BigDecimal.ZERO) > 0){
                    Ssqb querySweetConfig = Ssqb.create("com.lewei.ma.box.querySweetConfig")
                            .setParam("memberId", memberId)
                            .setParam("configType", SweetConfigTypeEnum.S_SWT_ONE.value())
                            .setParam("profit",profit)
                            ;
                    BoxSweetConfigVo configVo =  dao.findForObj(querySweetConfig,BoxSweetConfigVo.class);
                    if (configVo != null) {

                        SaasQueryBuilder querySweetRecord = SaasQueryBuilder.where(Restrictions.eq("memberId", memberId))
                                .and(Restrictions.eq("configId",configVo.getId()))
                                .and(Restrictions.eq("createDate",DateFormatUtils.formatDate(date,"yyyy-MM-dd")));
                        SweetRecord sweetRecord = dao.query(querySweetRecord, SweetRecord.class);
                        if (sweetRecord == null) {
                            //查询符合条件奖品
                            List<MaBoxItemRewardVo> sweetRewardVos = itemRewardVos.stream().filter(a->a.getRecoveryFee().compareTo(configVo.getRecoveryMin()) >= 0 &&
                                    a.getRecoveryFee().compareTo(configVo.getRecoveryMax()) <= 0).collect(Collectors.toList());
                            if (ListUtil.isNotEmpty(sweetRewardVos)){
                                //奖品排序 取回收价最小的
                                sweetRewardVos = sweetRewardVos.stream() .sorted(Comparator.comparing(MaBoxItemRewardVo::getRecoveryFee,Comparator.nullsLast(BigDecimal::compareTo))).collect(Collectors.toList());
                                MaBoxItemRewardVo sweetReward = sweetRewardVos.get(0);
                                MaBoxItemRewardVo sweetReplaceReward = winRewards.get(winRewards.size() - 1);
                                winRewards.remove(winRewards.size() - 1);
                                sweetReward.setSerialNum(sweetReplaceReward.getSerialNum());
                                winRewards.add(sweetReward);

                                sweetRecord = new SweetRecord();
                                sweetRecord.setConfigId(configVo.getId());
                                sweetRecord.setMemberId(memberId);
                                sweetRecord.setCreated(DateFormatUtils.getNow());
                                sweetRecord.setCreateDate(date);
                                dao.save(sweetRecord);

                            }
                        }
                    }
                }

            }

        } catch (Exception e) {
            log.error("sweet handel error",e);
        }
    }


    private void sweetHandelThree(String paymentMethod,Long  memberId, List<MaBoxItemRewardVo> itemRewardVos, List<MaBoxItemRewardVo> winRewards) {
        try {

            if (OrderUtil.isMoneyPay(paymentMethod)){

                BigDecimal profit = sweetConfigService.querySweetProfit(memberId,SweetConfigTypeEnum.S_SWT_THREE.value());
                if (profit != null && profit.compareTo(BigDecimal.ZERO) > 0){

                    BoxSweetConfigVo configVo =  sweetConfigService.queryBoxSweetConfigVo(memberId,SweetConfigTypeEnum.S_SWT_THREE.value(),profit);
                    if (configVo != null) {
                        //查询记录
                        SweetConfigLog configLog = sweetConfigService.querySweetConfigLog(memberId,configVo.getId());

                        if (configLog == null) {
                            //查询符合条件奖品
                            List<MaBoxItemRewardVo> sweetRewardVos = new ArrayList<>();

                            for (SweetRuleVo sweetRuleVo : configVo.getSweetRuleVos()) {
                                List<MaBoxItemRewardVo> sweetRewardVosSubs = new ArrayList<>();

                                sweetRewardVosSubs = itemRewardVos.stream().filter(a->a.getRecoveryFee().compareTo(sweetRuleVo.getRecoveryMin()) >= 0 &&
                                        a.getRecoveryFee().compareTo(sweetRuleVo.getRecoveryMax()) <= 0).collect(Collectors.toList());

                                if (ListUtil.isNotEmpty(sweetRewardVosSubs)){
                                    sweetRewardVosSubs.forEach(a->a.setOdds(sweetRuleVo.getOdds()));
                                    sweetRewardVos.addAll(sweetRewardVosSubs);
                                }

                            }

                            if (ListUtil.isNotEmpty(sweetRewardVos)){
                                //奖品排序 取回收价最小的
                                List<Double> rewardOddsList = new ArrayList<>();
                                List<Double> sortRewardOddsList = new ArrayList<>() ;
                                //总概率 可以支持概率总和不等一
                                Double sumOdds = sweetRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();
                                Double tempOdds = 0d;
                                for (MaBoxItemRewardVo itemRewardVo : sweetRewardVos) {
                                    tempOdds += itemRewardVo.getOdds();
                                    rewardOddsList.add(tempOdds / sumOdds);
                                }


                                MaBoxItemRewardVo rewardVo = new MaBoxItemRewardVo();
                                //随机数在哪个概率区间内，则是哪个奖品
                                double randomDouble = Math.random();
                                sortRewardOddsList.addAll(rewardOddsList);
                                //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                                sortRewardOddsList.add(randomDouble);
                                Collections.sort(sortRewardOddsList);
                                int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);

                                //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
                                BeanUtils.copyProperties(sweetRewardVos.get(lotteryIndex), rewardVo);

                                MaBoxItemRewardVo sweetReplaceReward = winRewards.get(winRewards.size() - 1);
                                winRewards.remove(winRewards.size() - 1);
                                rewardVo.setSerialNum(sweetReplaceReward.getSerialNum());
                                winRewards.add(rewardVo);

                                sweetConfigService.saveSweetConfigLog(memberId,configVo.getId());
                            }
                        }

                        if (BooleanUtils.isTrue(configVo.getIsEnd())){
                            configLog = sweetConfigService.querySweetConfigLog(memberId,configVo.getId());
                            if (configLog != null) {
                                //删除记录
                                sweetConfigService.deleteSweetConfigLog(memberId);
                                //重置奖池
                                MemberSweetProfitRequest sweetProfitRequest = new MemberSweetProfitRequest();
                                sweetProfitRequest.setMemberId(memberId);
                                sweetProfitRequest.setCzProfit(BigDecimal.ZERO);
                                sweetProfitRequest.setType("init");
                                sweetProfitRequest.setTitle(BizMessageSource.getInstance().getMessage("cem12009"));
                                sweetProfitRequest.setSweetType(SweetConfigTypeEnum.S_SWT_THREE.value());
                                memberFeignClient.memberMemberSweetHandler(sweetProfitRequest);
                            }
                        }
                    }
                }

            }

        } catch (Exception e) {
            log.error("sweet handel error",e);
        }
    }



    private boolean sweetHandelFour(Long  memberId,  List<MaBoxRewardRuleVo> randomRewards,Integer num,List<Long> winRewardIds) {

        boolean isSweet = false;
        if (num == 0 && sweetConfigService.querySweetConfigFourIsOpen()){
            try {
                BigDecimal profit = sweetConfigService.querySweetProfit(memberId,SweetConfigTypeEnum.S_SWT_FOUR.value());
                if (profit != null && profit.compareTo(BigDecimal.ZERO) > 0){

                    BoxSweetConfigVo configVo =  sweetConfigService.queryBoxSweetConfigVo(memberId,SweetConfigTypeEnum.S_SWT_FOUR.value(),profit);
                    if (configVo != null) {
                        //查询记录
                        SweetConfigLog configLog = sweetConfigService.querySweetConfigLog(memberId,configVo.getId());

                        if (configLog == null) {
                            //查询符合条件奖品
                            List<MaBoxRewardRuleVo> sweetRewardVos = new ArrayList<>();

                            for (SweetRuleVo sweetRuleVo : configVo.getSweetRuleVos()) {
                                List<MaBoxRewardRuleVo> sweetRewardVosSubs = new ArrayList<>();

                                sweetRewardVosSubs = randomRewards.stream().filter(a->a.getRecoveryFee().compareTo(sweetRuleVo.getRecoveryMin()) >= 0 &&
                                        a.getRecoveryFee().compareTo(sweetRuleVo.getRecoveryMax()) <= 0).collect(Collectors.toList());

                                if (ListUtil.isNotEmpty(sweetRewardVosSubs)){
                                    sweetRewardVosSubs.forEach(a->a.setOdds(sweetRuleVo.getOdds()));
                                    sweetRewardVos.addAll(sweetRewardVosSubs);
                                }

                            }

                            if (ListUtil.isNotEmpty(sweetRewardVos)){
                                //奖品排序 取回收价最小的
                                List<Double> rewardOddsList = new ArrayList<>();
                                List<Double> sortRewardOddsList = new ArrayList<>() ;
                                //总概率 可以支持概率总和不等一
                                Double sumOdds = sweetRewardVos.stream().mapToDouble(MaBoxRewardRuleVo::getOdds).sum();
                                Double tempOdds = 0d;
                                for (MaBoxRewardRuleVo itemRewardVo : sweetRewardVos) {
                                    tempOdds += itemRewardVo.getOdds();
                                    rewardOddsList.add(tempOdds / sumOdds);
                                }

                                //随机数在哪个概率区间内，则是哪个奖品
                                double randomDouble = Math.random();
                                sortRewardOddsList.addAll(rewardOddsList);
                                //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                                sortRewardOddsList.add(randomDouble);
                                Collections.sort(sortRewardOddsList);
                                int lotteryIndex = sortRewardOddsList.indexOf(randomDouble);

                                MaBoxRewardRuleVo rewardVo =  sweetRewardVos.get(lotteryIndex);

                                this.updateReward(rewardVo.getId(),rewardVo.getRewardId(),1,winRewardIds);

                                sweetConfigService.saveSweetConfigLog(memberId,configVo.getId());

                                isSweet = true;
                            }
                        }

                        if (BooleanUtils.isTrue(configVo.getIsEnd())){
                            configLog = sweetConfigService.querySweetConfigLog(memberId,configVo.getId());
                            if (configLog != null) {
                                //删除记录
                                sweetConfigService.deleteSweetConfigLog(memberId);
                                //重置奖池
                                MemberSweetProfitRequest sweetProfitRequest = new MemberSweetProfitRequest();
                                sweetProfitRequest.setMemberId(memberId);
                                sweetProfitRequest.setCzProfit(BigDecimal.ZERO);
                                sweetProfitRequest.setType("init");
                                sweetProfitRequest.setSweetType(SweetConfigTypeEnum.S_SWT_FOUR.value());
                                sweetProfitRequest.setTitle(BizMessageSource.getInstance().getMessage("cem12009"));
                                memberFeignClient.memberMemberSweetHandler(sweetProfitRequest);
                            }
                        }
                    }
                }



            } catch (Exception e) {
                isSweet = false;
                log.error("sweet handel error",e);
            }
        }


        return isSweet;
    }

    /**
     * sweet 补偿处理
     * @param memberId
     * @param itemRewardVos
     */
    private List<MaBoxItemRewardVo> sweetHandelTwo(String paymentMethod, Long memberId, List<MaBoxItemRewardVo> itemRewardVos) {
        try {

            if (OrderUtil.isMoneyPay(paymentMethod)) {
                Ssqb queryMemberDayProfit = Ssqb.create("com.lewei.ma.box.queryMemberDayTradeMoney")
                        .setParam("memberId", memberId);
                BigDecimal dayTradeMoney = dao.findForObj(queryMemberDayProfit, BigDecimal.class);
                if (dayTradeMoney != null) {
                    Ssqb querySweetConfig = Ssqb.create("com.lewei.ma.box.querySweetConfig")
                            .setParam("memberId", memberId)
                            .setParam("configType", SweetConfigTypeEnum.S_SWT_TWO.value())
                            .setParam("profit", dayTradeMoney);
                    BoxSweetConfigVo configVo = dao.findForObj(querySweetConfig, BoxSweetConfigVo.class);
                    if (configVo != null) {
                        //查询符合条件奖品
                        List<MaBoxItemRewardVo> sweetList = itemRewardVos.stream().filter(a -> a.getRecoveryFee().compareTo(configVo.getRecoveryMin()) >= 0 &&
                                a.getRecoveryFee().compareTo(configVo.getRecoveryMax()) <= 0).collect(Collectors.toList());
                        if (ListUtil.isNotEmpty(sweetList)){
                            return sweetList;
                        }
                    }
                }
            }
            return itemRewardVos;
        } catch (Exception e) {
            log.error("sweet handel two error", e);
            return itemRewardVos;
        }
    }

    private void saveNewcomerBuyRecord(Long memberId,Long spuId,Long boxItemId,Long newcomerRuleId){
        try {
            if (newcomerRuleId != null && newcomerRuleId != 0L) {
               SaasQueryBuilder queryBuyRecord = SaasQueryBuilder.where(Restrictions.eq("memberId",memberId))
                       .and(Restrictions.eq("spuId",spuId));
                NewcomerActivityBuyRecord buyRecord = dao.query(queryBuyRecord, NewcomerActivityBuyRecord.class);
                if (buyRecord == null) {
                    buyRecord = new NewcomerActivityBuyRecord();
                    buyRecord.setMemberId(memberId);
                    buyRecord.setBoxItemId(boxItemId);
                    buyRecord.setSpuId(spuId);
                    buyRecord.setRuleId(newcomerRuleId);
                    buyRecord.setCreated(DateFormatUtils.getNow());
                    dao.save(buyRecord);
                }
            }
        } catch (Exception e) {
          log.error("saveNewcomerBuyRecord error ",e);
        }
    }

    /**
     * 保存会员盲盒图鉴
     */
    private void saveMemberBoxBook(Long spuId , Long memberId , Long spuItemId){
        log.info("saveMemberBoxBook gift , spuId = {} , memberId = {} , spuItemId = {}",spuId,memberId,spuItemId);
        Ssqb ssqb = Ssqb.create("com.lewei.ma.box.saveMemberBoxBook")
                .setParam("id",idWorker.nextId())
                .setParam("spuId", spuId)
                .setParam("memberId", memberId)
                .setParam("spuItemId", spuItemId);
        dao.updateByMybatis(ssqb);
    }

    private void handleBuyUpBoxSuccess(BuyBoxSuccessEvent event){
        Order order = event.getOrder();
        //订单id放到上下文使用
        orderLocal.set(order);

        XcrmThreadContext.setChainId(order.getChainId());
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

        JSONObject extJson = event.getOrderExtJson();
        Long boxItemId = extJson.getLong("boxItemId");

        Integer num = extJson.getInteger("num");

        Long spuId = extJson.getLong("spuId");

        String boxType = extJson.getString("boxType");
        Long stageId = extJson.getLong("stageId");
        Long categoryId = extJson.getLong("categoryId");
        // 选择的商品id
        Long selectSkuId = extJson.getLong("selectSkuId");
        // 指定序号
        JSONArray serialArray = extJson.getJSONArray("serialNums");
        List<Integer> serialNums = new ArrayList<>();
        if(serialArray != null) {
            serialNums = serialArray.toJavaList(Integer.class);
        }
        // 子箱子id
        Long subBoxItemId = extJson.getLong("subBoxItemId");

        Date now = DateFormatUtils.getNow();

        int redisCount = 0;

        try {

            SaasQueryBuilder queryBox = SaasQueryBuilder.where(Restrictions.eq("spuId",spuId));
            ProductBox productBox = dao.query(queryBox,ProductBox.class);
            String serialNumKey = BOX_COUNTER+boxItemId + "@" + stageId;
            //抽奖最后位置
            Integer currentRewardNum = (Integer) redisCacheProvider.getRedisTemplate().opsForValue().get(serialNumKey);
            if (currentRewardNum == null || currentRewardNum == 0) {
                currentRewardNum = boxService.queryBoxItemCount(boxItemId,stageId);

                redisCacheProvider.getRedisTemplate().opsForValue().set(serialNumKey,currentRewardNum,1, TimeUnit.DAYS);
            }else {
                //重置过期时间
                redisCacheProvider.getRedisTemplate().expire(serialNumKey,1, TimeUnit.DAYS);
            }

            List<MaBoxItemRewardVo> itemRewardVos = boxService.queryMaBoxItemRewardList(boxItemId,stageId);
            MemberBoxVo member = this.queryMember(order.getMemberId());
            List<MaBoxItemRewardVo> winRewards = new ArrayList<>();
            List<OrderBoxItem> orderBoxItems = new ArrayList<>();
            List<MemberReward> memberRewards = new ArrayList<>();
            ProductBoxItem productBoxItem = dao.queryById(boxItemId,ProductBoxItem.class);

            // 第一关 || 第二关（闯关盲盒）
            if(Objects.equals(stageId, 1L) || Objects.equals(stageId, 2L)) {
                // 查询开奖次数
                Integer openNum = 0;
                JSONArray jsonArray = JSONObject.parseArray(productBoxItem.getStageJson());
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (Objects.equals(jsonObject.getLong("stageId"), stageId)) {
                        openNum = jsonObject.getInteger("openNum");
                        break;
                    }
                }

                //总概率 可以支持概率总和不等一
                Double sumOdds = itemRewardVos.stream().mapToDouble(MaBoxItemRewardVo::getOdds).sum();
                //计算每个奖品的概率区间
                //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
                List<Double> rewardOddsList = new ArrayList<>();
                List<Double> sortRewardOddsList = new ArrayList<>();
                Double tempOdds = 0d;
                for (MaBoxItemRewardVo itemRewardVo : itemRewardVos) {
                    tempOdds += itemRewardVo.getOdds();
                    rewardOddsList.add(tempOdds / sumOdds);
                }

                Set<Integer> checkRepeatSet = new HashSet<>();
                MaBoxItemRewardVo rewardVo;
                for (int i = 0; i < num; i++) {
                    for (int j = 0; j < openNum; j++) {
                        int lotteryIndex;
                        // 保证不重复抽奖
                        do {
                            //随机数在哪个概率区间内，则是哪个奖品
                            double randomDouble = Math.random();
                            //重试前清空
                            sortRewardOddsList.clear();
                            sortRewardOddsList.addAll(rewardOddsList);
                            //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
                            sortRewardOddsList.add(randomDouble);
                            Collections.sort(sortRewardOddsList);
                            lotteryIndex = sortRewardOddsList.indexOf(randomDouble);
                        } while (checkRepeatSet.contains(lotteryIndex));
                        checkRepeatSet.add(lotteryIndex);

                        rewardVo = new MaBoxItemRewardVo();
                        //这里直接=号赋值 地址引用同样的奖品 会导致序号有问题
                        BeanUtils.copyProperties(itemRewardVos.get(lotteryIndex), rewardVo);

                        currentRewardNum = redisCacheProvider.getRedisTemplate().opsForValue().increment(serialNumKey).intValue();
                        redisCount++;
                        rewardVo.setSerialNum(currentRewardNum);
                        winRewards.add(rewardVo);

                        sortRewardOddsList.clear();
                    }
                }

            }else if(Objects.equals(stageId, 3L)){
                // 第三关（序号盲盒）
                //更新模板位置
                List<ProductBoxUpItemRewardTmpl> rewardTmpls = new ArrayList<>();
                SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("boxItemId",boxItemId))
                        .and(Restrictions.eq("subBoxItemId", subBoxItemId))
                        .and(Restrictions.in("serialNum",serialNums))
                        .and(Restrictions.eq("isAward", 0));
                rewardTmpls = dao.queryList(query, ProductBoxUpItemRewardTmpl.class);
                if(ListUtil.isNotEmpty(rewardTmpls)) {
                    for (ProductBoxUpItemRewardTmpl rewardTmpl : rewardTmpls) {
                        Ssqb updateRewardTmpl = Ssqb.create("com.lewei.ma.box.updateUpBoxRewardTmpl")
                                .setParam("memberId", order.getMemberId())
                                .setParam("serialNum", rewardTmpl.getSerialNum())
                                .setParam("boxItemId", boxItemId)
                                .setParam("orderId", order.getId())
                                .setParam("subBoxItemId", subBoxItemId);
                        dao.updateByMybatis(updateRewardTmpl);

                        Ssqb updateRewardLeftQuantity = Ssqb.create("com.lewei.ma.box.updateBoxItemRewardLeftQuantity")
                                .setParam("rewardId",rewardTmpl.getRewardId())
                                .setParam("num",1);
                        dao.updateByMybatis(updateRewardLeftQuantity);

                        MaBoxItemRewardVo reward = boxService.queryMaBoxItemRewardById(rewardTmpl.getRewardId());

                        currentRewardNum = redisCacheProvider.getRedisTemplate().opsForValue().increment(serialNumKey).intValue();
                        reward.setSerialNum(currentRewardNum);
                        winRewards.add(reward);
                    }
                }


                // 退款处理
                int refundNum = num - rewardTmpls.size();
//                if (refundNum > 0){
//                    currentRewardNum = redisCacheProvider.getRedisTemplate().opsForValue().decrement(serialNumKey, refundNum).intValue();
//                    redisCount = redisCount - refundNum;
//                }
                BigDecimal refundFee = this.handelRefund(order,num,refundNum,spuId,boxItemId,stageId);
                this.handleTradeFlow(order,refundFee);

            }

            this.handelReward(winRewards, orderBoxItems, memberRewards, order, member, boxItemId, now);
            dao.batchCreate(orderBoxItems,OrderBoxItem.class);
            dao.batchSave(memberRewards, MemberReward.class);

            if (productBox != null && BooleanUtils.isTrue(productBox.getIsProduceFraction())){
                //会员积分处理
                applicationEventPublisher.publishEvent(new MemberFractionEvent(this,order.getMemberId(),order.getOrderMoney(), order.getOrderType(),order.getChainId(),order.getOrderTitle(),order.getPaymentMethod()));
            }
            this.handleTradeFlow(order,BigDecimal.ZERO);
            this.saveOrderBoxItem2Es(orderBoxItems);

            if (ListUtil.isNotEmpty(winRewards)){

                //分销处理
                handleDist(order);
                //弹幕
                applicationEventPublisher.publishEvent(new BulletChatEvent(this,winRewards,member,order.getChainId(),boxType,boxItemId,spuId));
                //会员每日成本
                applicationEventPublisher.publishEvent(new MemberCategoryDataEvent(this,Collections.singletonMap(member,winRewards),order.getChainId(), MDCTraceUtils.getTraceId(),categoryId,order.getPaymentMethod()));

                //获得闯关票数量
                boolean flag = false;
                if(Objects.equals(stageId, 1L)) {
                    flag = winRewards.stream().anyMatch(a -> Objects.equals(selectSkuId, a.getSkuItemId()));
                }else if(Objects.equals(stageId, 2L)){
                    flag = winRewards.stream().anyMatch(a -> BooleanUtils.isTrue(a.getIsConcatenate()));
                }
                if (flag){
                    //增加闯关票
                    MemberBoxTicketReq ticketReq = new MemberBoxTicketReq();
                    ticketReq.setBoxItemId(boxItemId);
                    ticketReq.setStageId(stageId + 1);
                    ticketReq.setMemberId(member.getId());
                    ticketReq.setPlId(order.getId());
                    ticketReq.setTitle(BizMessageSource.getInstance().getMessage("cem12003"));
                    ticketReq.setType(MemberBoxTicketTypeEnum.income.value());
                    ticketReq.setQuantity(1);
                    memberFeignClient.handelMemberBoxTicket(ticketReq);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            //退款到余额
            try {
                failHandel(order, boxItemId,spuId, stageId,num);
            } catch (Exception ex) {
                log.error("up赏盲盒支付异常，退还到余额 失败 orderId = {},boxItemId = {},报错信息 = {}",order.getId(),boxItemId,ex.getMessage());
                ex.printStackTrace();
            }

            redisCacheProvider.getRedisTemplate().opsForValue().decrement(BOX_COUNTER+boxItemId + "@" + stageId,redisCount);
            log.error("up赏盲盒抽奖报错 = {}",e.getMessage());
            throw e;

        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            orderLocal.remove();
        }

    }

    private void handelReward(List<MaBoxItemRewardVo> winRewards, List<OrderBoxItem> orderBoxItems, List<MemberReward> memberRewards,
                              Order order, MemberBoxVo member, Long boxItemId, Date now){
        for (MaBoxItemRewardVo reward : winRewards) {
            OrderBoxItem orderBoxItem = new OrderBoxItem();
            BeanUtils.copyProperties(reward, orderBoxItem);
            orderBoxItem.setPaymentMethod(orderLocal.get().getPaymentMethod());
            orderBoxItem.setOrderId(order.getId());
            orderBoxItem.setMemberId(order.getMemberId());
            orderBoxItem.setMemberName(member.getMemberName());
            orderBoxItem.setHeadImage(member.getHeadImage());
            orderBoxItem.setMemberTitleImage(member.getMemberTitleImage());
            orderBoxItem.setBoxItemId(boxItemId);
            orderBoxItem.setCreated(now);
            orderBoxItem.setRewardPriority(reward.getRewardPriority());
            orderBoxItem.setId(idWorker.nextId());
            if (reward.getCategoryPic() == null) {
                orderBoxItem.setCategoryPic("");
            }
            orderBoxItems.add(orderBoxItem);

            //保存会员赏袋
            MemberReward memberReward = new MemberReward();
            BeanUtils.copyProperties(reward, memberReward);
            memberReward.setOrderId(order.getId());
            memberReward.setMemberId(order.getMemberId());
            memberReward.setBoxItemId(boxItemId);
            memberReward.setCreated(now);
            memberReward.setStatus(MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
            memberReward.setReceiveWay(getRewardReceiveWay(order.getPaymentMethod()));
            memberReward.setSpuId(reward.getSpuItemId());
            memberRewards.add(memberReward);
        }
    }
}
