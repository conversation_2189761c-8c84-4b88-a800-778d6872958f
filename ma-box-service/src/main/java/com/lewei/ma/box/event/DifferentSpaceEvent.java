package com.lewei.ma.box.event;

import com.lewei.ma.box.vo.MaBoxItemRewardVo;
import com.lewei.ma.box.vo.MemberBoxVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Map;

/**
 * XXXX
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@Getter
public class DifferentSpaceEvent extends ApplicationEvent {


    private Long chainId;

    private final Map<MemberBoxVo,List<MaBoxItemRewardVo>> rewardVos;

    private Long spuId;

    private Long memberId;

    private String traceId;




    public DifferentSpaceEvent(Object source,Map<MemberBoxVo,List<MaBoxItemRewardVo>> rewardVos, Long spuId,Long memberId, Long chainId, String traceId) {
        super(source);
        this.rewardVos = rewardVos;
        this.spuId = spuId;
        this.memberId = memberId;
        this.chainId = chainId;
        this.traceId = traceId;
    }
}
