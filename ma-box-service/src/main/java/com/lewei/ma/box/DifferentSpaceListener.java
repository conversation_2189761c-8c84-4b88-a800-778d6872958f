package com.lewei.ma.box;

import com.alibaba.nacos.common.utils.MapUtils;
import com.lewei.eshop.cache.MaCacheKeys;
import com.lewei.eshop.cache.RedisCacheProvider;
import com.lewei.eshop.common.vo.different.MaDifferentSpaceVo;
import com.lewei.eshop.entity.app.ApplicationConfig;
import com.lewei.eshop.entity.product.box.ProductBox;
import com.lewei.log.trace.MDCTraceUtils;
import com.lewei.ma.box.client.AppFeignClient;
import com.lewei.ma.box.event.BoxBookEvent;
import com.lewei.ma.box.event.DifferentSpaceEvent;
import com.lewei.ma.box.vo.MaBoxItemRewardVo;
import com.lewei.ma.box.vo.MemberBoxVo;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.db.saas.IIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 异空间会员抽奖进度处理
 *
 * <AUTHOR>
 * @since 2020/8/14
 */
@Component
@Transactional
@Slf4j
public class DifferentSpaceListener {

    @Autowired
    private BaseDaoSupport dao;

    @Autowired
    private AppFeignClient appFeignClient;
    @Autowired
    private RedisCacheProvider redisCacheProvider;
    @Autowired
    private IIdWorker idWorker;

    @EventListener
    public void handleDifferentSpaceEvent(DifferentSpaceEvent event) {
        log.info("handleDifferentSpaceEvent , event = {} ", event);
        try {
            XcrmThreadContext.setChainId(event.getChainId());
            XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
            MDCTraceUtils.putTraceId(event.getTraceId());

            //判断开关
            ApplicationConfig config = appFeignClient.queryApplicationConfigInfo("differentSpaceCfg");
            if (Objects.nonNull(config) && config.getIsMaShow()) {
                Long spuId = event.getSpuId();
                Map<MemberBoxVo, List<MaBoxItemRewardVo>> rewardVos = event.getRewardVos();
                rewardVos.forEach((member, v) -> {
                    Long memberId = member.getId();
                    for (MaBoxItemRewardVo rewardVo : v) {
                        log.info("handleDifferentSpaceEvent , rewardVo = {}", rewardVo);
                        if (BooleanUtils.isFalse(rewardVo.getIsGift())) {
                            //判断是否正在开启异空间
                            Ssqb querySpace = Ssqb.create("com.lewei.eshop.ma.box.different.space.queryDifferentSpace")
                                    .setParam("spuId", spuId)
                                    .setParam("memberId", memberId);
                            MaDifferentSpaceVo vo = dao.findForObj(querySpace, MaDifferentSpaceVo.class);
                            log.info("handleDifferentSpaceEvent , MaDifferentSpaceVo = {}", vo);
                            if (Objects.nonNull(vo)) {
                                String key = MaCacheKeys.DIFFERENT_SPACE_KEY + vo.getId();
                                //判断是否清空
                                if (vo.getClearCategoryId().equals(rewardVo.getCategoryId())) {
                                    redisCacheProvider.getRedisTemplate().opsForHash().delete(key, memberId.toString());
                                } else {
                                    //抽奖成功进度+1
                                    //+1后达到阈值 发放入场券
                                    int currentNum = redisCacheProvider.getRedisTemplate().opsForHash().increment(key, memberId.toString(), 1L).intValue();
                                    log.info("handleDifferentSpaceEvent , currentNum = {}", currentNum);
                                    if (currentNum >= vo.getProgressNum()) {
                                        //发放完成 清空进度
                                        Ssqb addMemberQuantity = Ssqb.create("com.lewei.eshop.ma.box.different.space.addDifferentSpaceMember")
                                                .setParam("id", idWorker.nextId())
                                                .setParam("num", 1)
                                                .setParam("differentSpaceId", vo.getId())
                                                .setParam("memberId", memberId);
                                        dao.updateByMybatis(addMemberQuantity);
                                        redisCacheProvider.getRedisTemplate().opsForHash().delete(key, memberId.toString());
                                    }

                                }
                            }
                        }
                    }
                });


            }
        } catch (Exception e) {
            log.error("handleDifferentSpaceEvent error", e);
        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
            MDCTraceUtils.removeTraceId();
        }


    }

}
