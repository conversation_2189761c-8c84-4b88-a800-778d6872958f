package com.lewei.eshop.cache;

/**
 * <AUTHOR>
 * @description 小程序端 缓存key定义
 * @date 2021/12/8
 */
public interface MaCacheKeys {

    /**
     * 奖品围观人数
     */
    String MA_PRODUCT_LOOKER_NUM_PLUS_KEY = "lw::ma::looker::plus::";

    /**
     * 转发有礼分享记录
     */
    String GIFT_SHARE_RECORD_LOCK_KEY = "lw::ma::gift::record::lock::";
    /**
     * 清除活动配置缓存
     */
    String MA_GIFT_CONFIG_KEY = "lw::ma::gift::config::";

    /**
     * 商品列表缓存
     */
    String MA_PRODUCTS_VIEW_KEY = "lw::ma::products::views::";
    /**
     * 卡盒列表缓存
     */
    String MA_BOX_CARD_VIEW_KEY = "lw::ma::box::card::views::";

//    /**
//     * 商品自定义列表缓存（商品数据)
//     */
//    String MA_PRODUCTS_CUSTOM_VIEW_KEY = "lw::ma::products::custom::views::";

    /**
     * 商品自定义列表缓存（页面key值）
     */
    String MA_PRODUCTS_CUSTOM_VIEW_HASH_KEY = "lw::ma::products::custom::views::";
    /**
     * 手机验证码缓存
     */
    String MOBILE_VERIFY_CODE_KEY = "lw::ma::mobile::verify::code::";
    /**
     * 抽赏赠送配置
     */
     String REWARD_GIFT_CONFIG = "lw::reward::gift::config::";
    /**
     * 盲盒序号数
     */
    String PRODUCT_BOX_SERIAL_NUMBER = "lw::product::box::serial::number::";
    /**
     * 自动上架配置
     */
    String AUTO_SHELF_CONFIG = "lw::auto::shelf::config::";

    /**
     * 自动上架配置
     */
    String AUTO_SHELF_SPU_ID = "lw::auto::shelf::spuid::";

    /**
     * 删除社区配置缓存
     */
    String MEDIA_CONFIG_KEY = "lw::media::config::key::";
    /**
     * 手机验证码缓存(pc)
     */
    String MOBILE_VERIFY_CODE_KEY_PC = "lw::pc::mobile::verify::code::";
    /**
     * 藏宝阁获取配置
     */
    String TREASURE_OBTAIN_CONFIG = "lw::treasure::obtain::config::";

    /**
     * 防止app登录并发key
     */
    String APP_LOGIN_CONCURRENCY_CODE_KEY = "lw::ma::app::login::concurrency::code::";

    /**
     * 弹幕配置key
     */
    String BULLET_CHAT_CONFIG_KEY = "lw::bullet::chat::config::";

    /**
     * 弹幕队列key
     */
    String BULLET_CHAT_QUEUE_KEY = "lw::bullet::chat::queue::";

    /**
     * 换换宇宙配置key
     */
    String CHANGE_UNIVERSE_CONFIG_KEY = "lw::change::universe::config::";

    /**
     * 订单支付队列queue
     */
    String ORDER_PAY_QUEUE_KEY = "lw::order::pay::queue::key";
    /**
     * 盲盒队列queue
     */
    String BOX_ITEM_PAY_QUEUE_KEY = "lw::box::item::pay::queue::key::";
    /**
     * 支付抵扣配置key
     */
    String PAY_DEDUCT_CONFIG_KEY = "lw::pay::deduct::config::key::";
    /**
     * 锁箱key
     */
    String LOCK_BOX_KEY = "lw::lock::box::key::";
    /**
     * 锁箱队列key
     */
    String LOCK_BOX_QUEUE_KEY = "lw::lock::box::queue::key::";
    /**
     * 消息自定义key
     */
    String MESSAGE_CUSTOM_KEY = "lw::message::custom::key::";
    /**
     * 会员活跃度
     */
    String MEMBER_ACTIVE_VALUE_KEY = "lw::member::active::value::key::";
    /**
     * 会员领取活跃度key
     */
    String MEMBER_RECEIVE_ACTIVE_VALUE_KEY = "lw::member::receive::active::value::key::";
    /**
     * 试玩次数
     */
    String MEMBER_TRY_LUCKY = "lw::member::try::lucky";
    /**
     * 乐享赚基础配置
     */
    String ENJOY_EARNING_BASIC_CONFIGURATION = "lw::member::share::config";
    /**
     * 重复提交限制
     */
    String REPEATED_SUBMIT_RESTRICT = "lw::repeated::submit::restrict::";
    /**
     * 矿难计数器
     */
    String BOX_MINE_COUNT = "lw::box::mine::count::";


    /**
     * 签到天数
     */
    String SIGN_DAY_KEY = "lw::sign::day::count::";
    /**
     * 抽赏赠送计数器
     */
    String REWARD_GIFT_COUNTER = "lw::reward::gift::count::";

    /**
     * 回收队列queue
     */
    String RECYCLE_QUEUE_KEY = "lw::recycle::queue::key::";

    /**
     * 概率赠送计数器
     */
    String REWARD_GIFT_PROBABILITY_COUNTER = "lw::reward::gift::probability::count::";


    /**
     * app自定义首页
     */
    String APP_TPL_LEY = "lw::app::tpl::";


    /**
     *  备用api域名
     */
        String SEC_BKS_APIS = "lw::sec::bks-apis::";
    /**
     *  备用api域名()
     */
    String SEC_BKS_SPECIAL_APIS = "lw::sec::spe-bks-apis::";


    String AUTO_BUY_BOX_MEMBER_ID = "lw::auto::buy::box_member::id::";

    /**
     * 会员邀请记录key
     */
    String MEMBER_INVITE_KEY = "lw::member::invite::key::";

    /**
     * 抽赏赠送空车计数器
     */
    String REWARD_GIFT_EIGHT_COUNT = "lw::reward::gift::eight::count::";

    /**
     * 支付宝手机h5支付参数数据
     */
    String MA_ALIPAY_H5_ORDER_CACHE_KEY = "lw::ma::order::pay::";

    /**
     * 站点海外配置
     */
    String CHAIN_GLOBAL_CACHE_KEY = "lw::chain::global::";

    /**
     * 自定义首页轮播
     */
    String MAIN_TPL_LEY = "lw::main::tpl::";

    /**
     * 红包计数器
     */
    String RED_PACKAGE_COUNT = "lw::red::package::count::";


    /**
     * 会员进场播报(小时)
     */
    String BROADCAST_HOURS_MEMBER_ENTRY = "lw::broadcast::entry::hours::";
    /**
     * 会员进场播报(每天)
     */
    String BROADCAST_DAILY_MEMBER_ENTRY = "lw::broadcast::entry::daily::";

    /**
     * 会员每日购买商品计数器
     */
    String MEMBER_PRODUCT_QUEUE_KEY = "lw::member::product::queue::key";
    /**
     * 聚宝盆获取配置
     */
    String CORNUCOPIA_RULE_CONFIG = "lw::cornucopia::rule::config::";
    /**
     * 聚宝盆补仓上限
     */
    String CORNUCOPIA_COVER_NUM = "lw::cornucopia::cover::num::";
    /**
     * 自动上架多套配置
     */
    String AUTO_SHELVES_CONFIG = "lw::auto::shelves::config";

    /**
     * 娃娃机中奖结果
     */
    String DOLL_RESULT = "lw::doll::result::";

    /**
     * 指定赠送计数
     */
    String REWARD_GIFT_RGCT_SEVEN = "lw::reward::gift::rgctseven::";

    /**
     * 是否发放亏损补偿
     */
    String SEND_LOSS_COMPENSATE = "lw::send::loss::compensate::";

    /**
     * 会员编号补发处理
     */
    String MEMBER_CODE_HANDLE = "lw::member::code::handle::";
    /**
     * 获取消费提醒配置
     */
    String CONSUMER_REMINDER_CONFIG = "lw::consumer::reminder::config::";

    /**
     * 锁箱计数器
     */
    String LOCK_BOX_BUY_COUNT = "lw::lock::buy::count::";

    /**
     * 流水转盘限制
     */
    String TURNTABLE_LIMIT = "lw::turntable::limit::";

    /**
     * 对对碰阶段规则
     */
    String COLLISION_STAGE_RULE = "lw::collision::rule::";

    /**
     * 乐享赚邀请配置
     */
    String ENJOY_MEMBER_INVITE_CONFIG = "lw::member::invite::config";

    /**
     * 乐享赚邀请会员访问人数
     */
    String ENJOY_MEMBER_INVITE_VISIT = "lw::member::invite::visit::";

    /**
     * 乐享赚邀请会员消费人数
     */
    String ENJOY_MEMBER_INVITE_BUY = "lw::member::invite::buy::";
    /**
     * 分享有礼锁
     */
    String GIFT_SHARE_LOCK_KEY = "lw::gift::share::lock::";

    /**
     * 异度空间缓存进度
     */
    String DIFFERENT_SPACE_KEY = "lw::different::space::";
}
