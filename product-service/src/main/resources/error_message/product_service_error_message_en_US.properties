02001=Commodity category names cannot be repeated
02002=Product name cannot be repeated
02003=Shipping fee cannot be empty
02004=No product selected
02005=The product {0} has an unfinished order and cannot be deleted
02006=There are products under the category, operation is not allowed
02007=There are sub-categories under the category, please remove the sub-categories first
02008=The added and deleted categories are not allowed to be repeated
02009=Self is not allowed in superior category
02010=The minimum self-pickup time cannot be greater than the maximum self-pickup time
02011=Freight template rules cannot be empty
02012=Freight template region cannot be empty
02013=The freight template is in use and cannot be deleted
02014=Freight template does not exist
02015=The optional quantity cannot be greater than the service quantity in the card
02016=The number of options cannot be less than 1
02017=The type of storage and storage does not exist
02018=The goods in and out of the warehouse have been updated, please refresh and re-operate
02019=Inventory update failed
02020=There is an inventory locked commodity, which cannot be operated
02021=Cannot change store
02022=Only the document in progress can be modified
02023=Inventory document status error
02024=There is an inventory document in progress, which cannot be deleted
02025=Insufficient inventory of the calling party
02026=The status of the transfer document is wrong
02027=The actual delivery quantity cannot exceed the application transfer quantity
02028=The transferred product has been taken off the shelf or does not exist, please put it on the shelf or apply again
02029=Stage number [{0}] start stage cannot be greater than or equal to end stage
02030=The phase numbers [{0}] and [{1}] cannot intersect
02031=The maximum number of stage sales cannot be greater than the total number of prizes
02032=Blind box prize classification is not configured
02033=The classification of blind box prizes has been updated, please select the classification again
02034=The number of prizes in the blind box [{0}] does not match the total
02035=Blind box stage [{0}] quantity overflow
02036=The number of prizes [{0}] is inconsistent with the number in the rules
02037=Failed to generate the box, please do not click continuously or operate in other clients at the same time!
02038=The category of blind box prizes is in use and cannot be deleted!
02039=Please choose export data
02040=Import failed, please try again
02041=The product {0} is stored in the reward bag of the member, and cannot be deleted
02042=The product {0} exists in the blind box and cannot be deleted
02043=The winning start number of prize {0} cannot be greater than the end number in the middle
02044=The prize {0} winning serial number range cannot be less than the number of prizes
02045=The winning end sequence number of the prize {0} cannot be greater than the stage end sequence number
02046=The winning serial numbers of the same stage cannot overlap
02047=The winning start sequence number of the prize {0} cannot be less than the stage start sequence number
02048=There is no cooperative relationship with the supplier, and the product cannot be synchronized
02049=Insufficient inventory of prize {0}, unable to operate
02050=The supply price cannot be less than the cost price
02051=Blind box configuration already exists
02052=Blind box configuration does not exist
02053=The product code already exists
02054=A store can only have one reward control setting
02055=A shop can only have one kind of scrap set off the shelf
02056=Failed to put on the shelves. In order to reduce the risk of violations, the system detected that there are different prices for multiple blind boxes that need to be put on the shelves. Please adjust them to be consistent.
02057=Failed to put on the shelves. In order to reduce the risk of violation, the system detects that there are different prices for the blind box that needs to be put on the shelf and the blind box that has already been put on the shelf. Please adjust it to be the same
02058=The number of lucky draws allowed by this set shall not be greater than the number of prizes
02059=The number of all received is equal to 1, and the number of draws can be set in the case of single draw mode
02060=Probability sum is not equal to 1
02061=The blind box has been put on the shelf, please operate after taking it off the shelf
02062=This type of blind box cannot add sets
02063=The blind box {0} already exists in the "Automatic Shelf" list, please delete it in the "Automatic Shelf" list if you need to put it on the shelf in advance.
02064=Global does not support the last buyer to win the prize
02065=【{0}】The remaining quantity is insufficient
02066=【{0}】It is not listed in this shop/warehouse
02067=Only those that are not stored in the storage can be deleted
02068=It has been stored in the database and cannot be edited
02069=The reduced quantity of storage cannot be smaller than the remaining quantity
02070=The sum of display probability is not equal to 1
02071=The card level is the same as the existing level, saving failed
02072=Card level does not exist
02073=The card level is the same as the existing level, update failed
02074=Card book does not exist
02075=The sum of probabilities that an end box must be produced is not equal to 1
02076=The maximum inventory control cannot be greater than three
02077=At least one does not control the maximum value of the prize
02078=Maximum inventory cannot be empty
02079=The probability that an end box must come out cannot be empty
02080=The selling price cannot be less than the cost price
02081=The probability of non-controlled inventory prizes cannot be lower than 0.5
02082=Inventory max cannot be reduced
02083=The prize [{0}] cannot exist more than one
02084=Card level cannot be empty
02085=At least one prize must come out of end box
02086=There are products under this card level, which cannot be deleted
02087=This card book cannot be deleted if the customer already has cards
02088=The name of the card cannot exceed 50 characters
02089=Please select the product again
02090=The blind box has been put on the shelf, please take it off the shelf
02091=When the remaining quantity is less than the total, it cannot be checked
02092=This blind box doesn't exist
02093=The information does not match, please try again
02094=Failed to save, the unit price must be less than or equal to the minimum price of the prizes in the set
02095=The location information is used by prizes and cannot be deleted
02096=Mining disaster rewards cannot be empty
02097=The sum of mine disaster prizes is not equal to 0
02098=The total probability of mining disaster prizes must be greater than 0
02099=The mine disaster is not set, please set the mine disaster first
02100=The blind box has been taken off the shelf, please operate the mine disaster after putting it on the shelf
02101=The name of the prize is not allowed to be repeated
02102=The prize id cannot be repeated
02103=The lottery method of the specified serial number field is empty
02104=Countdown method cannot be empty
02105=The total number of prizes cannot exceed {0}
02106=The number of time type cannot be empty
02107=The number of stage【{0}】gifts cannot be greater than the number of stage prizes
02108=The sum of the probabilities of the [{0}] level is not equal to 1
02109=The sum of display probabilities of level【{0}】is not equal to 1
02110=The number of [{0}] tickets cannot be empty
02111=Stage id cannot be repeated
02112=The value range of stage id is 1~{0}
02114=The transfer item details cannot be empty
02115=Taobao prices cannot be empty when Taobao is displayed
02116=Reward only support gift type and none stage
02117=Please operate after removal from the shelf
02118=Inventory input range 1-10
02119=The modified inventory cannot be less than the sum of sold and gifted inventory
02120=Cost price cannot be greater than selling price
02121=The card book cannot be empty
02122=The Bestiary prize information cannot be left blank
02123=There are expired prizes in the Pokédex
02124=The card number already exists, please re-enter it
02125=The total amount of prizes added in level {0} is 10
02126=The total range for adding prizes in level {0} is from 1 to {1}
02127=The range of stage ID values is 1-3
02128=Incorrect box data
02129=Clearance prizes and regular prizes cannot be duplicated
02130=Clearance prizes and regular prizes cannot be empty
02131=The third level of the UP reward blind box does not have a box set and cannot be put on shelves
