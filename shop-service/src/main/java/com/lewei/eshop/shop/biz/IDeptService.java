package com.lewei.eshop.shop.biz;

import com.lewei.eshop.common.request.shop.DeptMoveRequest;
import com.lewei.eshop.common.request.shop.DeptRequest;
import com.lewei.eshop.common.vo.shop.DeptTreeVO;
import com.lewei.eshop.entity.user.Dept;

/**
 * 部门服务
 *
 * <AUTHOR>
 * @date 2019/12/19
 */
public interface IDeptService {

    /**
     * 显示部门树形
     *
     * @param deptId
     * @param chainId
     * @param includeShopDept 是否包含shop/店铺类型的部门
     * @return
     */
    DeptTreeVO queryDeptTree(Long deptId, Long chainId, Boolean includeShopDept);

    /**
     * 新增部门
     *
     * @param request
     * @param createBy
     * @param tenantId
     * @param chainId
     * @return
     */
    Dept saveDept(DeptRequest request, Long createBy, Long tenantId, String deptType, Long chainId);

    /**
     * 更新部门
     *
     * @param request
     * @param deptId
     * @param updateBy
     * @return
     */
    Dept updateDept(DeptRequest request, Long deptId, Long updateBy);

    /**
     * 删除部门
     *
     * @param deptId
     * @param chainId
     * @return
     */
    boolean deleteDept(Long deptId, Long chainId);

    /**
     * 移动部门
     *
     * @param deptId
     * @param request
     * @return
     */
    Dept moveDept(Long deptId, DeptMoveRequest request);

    /**
     * 部门详情
     *
     * @param deptId
     * @return
     */
    Dept queryDeptById(Long deptId);
}
