package com.lewei.eshop.shop.biz;

import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.shop.ShopErrorConstants;
import com.lewei.eshop.shop.SysConfig;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.http.*;
import com.xcrm.common.http.parser.ResponseParser;
import com.xcrm.common.http.utils.SafeUtils;
import com.xcrm.common.json.JsonHandler;
import com.xcrm.common.util.InputStreamUtils;
import com.xcrm.core.jersey.exception.ErrorMessage;
import com.xcrm.log.Logger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
* 访问门庭各系统rest接口抽象代理
* <AUTHOR>
* @create 2018-07-05 12:53
**/

public abstract class AbstractRestProxy {

    protected static Logger log = Logger.getLogger(AbstractRestProxy.class);

    protected ServiceClient serviceClient;
    protected URI endpoint;

    @Autowired
    protected JsonHandler jsonHandler;

    @Autowired
    protected SysConfig sysConfig;
    /**
     * 判断接口是否调用成功
     *
     * @param request  请求
     * @param response 响应
     * @return 是否成功
     */
    protected Boolean isSuccess(RequestMessage request, ResponseMessage response) {
        Boolean isSuccess = false;
        int httpCode = response.getStatusCode();
        if (request.getMethod() == HttpMethod.GET && httpCode == 200) {
            isSuccess = true;
        } else if (request.getMethod() == HttpMethod.POST && httpCode == 201) {
            isSuccess = true;
        } else if (request.getMethod() == HttpMethod.PUT && httpCode == 201) {
            isSuccess = true;
        } else if (request.getMethod() == HttpMethod.DELETE && httpCode == 204) {
            isSuccess = true;
        }
        return isSuccess;
    }

    /**
     * 获取响应数据
     *
     * @param request
     * @param response
     * @param responseClass
     * @return <T>
     * @throws Exception
     */
    protected <T> T toGeneralResponse(RequestMessage request, ResponseMessage response, Class<T> responseClass) throws Exception {

        Boolean isSuccess = isSuccess(request, response);
        T ropResponse;
        if (isSuccess) {
            //正常返回
            String json = response.getContent() != null ? InputStreamUtils.InputStreamTOString(response.getContent(), "UTF-8") : "";
            log.debug("{} {}{} httpStatus={}, response ={}", request.getMethod().value()
                    , endpoint.toString(), request.getResourcePath(), response.getStatusCode(), json);
            if(responseClass == null || StringUtils.isBlank(json)) {
                return null;
            }
            ropResponse = ResponseParser.unmarshaller(json, responseClass);
        } else {
            //错误返回
            String json = InputStreamUtils.InputStreamTOString(response.getContent(), "UTF-8");
            log.debug("{} {}{} httpStatus={}, response ={}", request.getMethod().value()
                    , endpoint.toString(), request.getResourcePath(), response.getStatusCode(), json);
            ErrorMessage errorMessage = ResponseParser.unmarshaller(json, ErrorMessage.class);
            throw new BizCoreRuntimeException(ShopErrorConstants.BACK_END_ERROR, errorMessage.getErrorMessage());
        }
        return ropResponse;
    }

    /**
     * 获取响应数据
     *
     * @param request
     * @param response
     * @param responseClass
     * @return <T>
     * @throws Exception
     */
    protected <T> List<T> toGeneralListResponse(RequestMessage request, ResponseMessage response, Class<T> responseClass) throws Exception {

        Boolean isSuccess = isSuccess(request, response);
        List<T> ropResponse;
        if (isSuccess) {
            //正常返回
            String json = InputStreamUtils.InputStreamTOString(response.getContent(), "UTF-8");
            log.debug("{} {}{} httpStatus={}, response ={}", request.getMethod().value()
                    , endpoint.toString(), request.getResourcePath(), response.getStatusCode(), json);
            if(responseClass == null) {
                return null;
            }
            ropResponse = JSONObject.parseArray(json,responseClass);
        } else {
            //错误返回
            String json = InputStreamUtils.InputStreamTOString(response.getContent(), "UTF-8");
            log.debug("{} {}{} httpStatus={}, response ={}", request.getMethod().value()
                    , endpoint.toString(), request.getResourcePath(), response.getStatusCode(), json);
            ErrorMessage errorMessage = ResponseParser.unmarshaller(json, ErrorMessage.class);
            throw new BizCoreRuntimeException(ShopErrorConstants.BACK_END_ERROR, errorMessage.getErrorMessage());
        }
        return ropResponse;
    }


    protected ResponseMessage send(RequestMessage request, ExecutionContext context, boolean keepResponseOpen)
            throws ServiceException, ClientException {

        ResponseMessage response = serviceClient.sendRequest(request, context);
        if (!keepResponseOpen) {
            SafeUtils.safeCloseResponse(response);
        }
        return response;
    }

    protected ExecutionContext createDefaultContext(HttpMethod method) {
        ExecutionContext context = new ExecutionContext();
        context.setCharset("utf-8");
        return context;
    }

    protected void init(String endpoint) {

        init(endpoint, 0);
    }

    protected void init(String endpoint, int maxErrorRetry) {

        if(StringUtils.isNotBlank(endpoint)) {
            try{
                this.endpoint = new URI(endpoint);
            }catch(URISyntaxException e){
                throw new IllegalArgumentException(e);
            }
            ClientConfiguration config = new ClientConfiguration();
            config.setMaxErrorRetry(maxErrorRetry);
            serviceClient = new DefaultServiceClient(config);
        }
    }
}