package com.lewei.eshop.shop.biz.impl;

import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.shop.TenantWalletRequest;
import com.lewei.eshop.entity.shop.TenantWallet;
import com.lewei.eshop.entity.shop.TenantWalletAgent;
import com.xcrm.common.page.Pagination;

/**
 * 店铺钱包
 */
public interface ITenantWalletService {



    /**
     * 店铺钱包处理
     * @param request request
     */
    void tenantWalletHandler(TenantWalletRequest request);

    /**
     * 获取店铺钱包
     * @return TenantWallet
     */
    TenantWallet getTenantWallet();

    /**
     *获取店铺加盟商钱包
     * @param agentChainId 加盟商id
     * @return TenantWalletAgent
     */
    TenantWalletAgent getTenantWalletAgent(Long agentChainId);

    /**
     * 查询店铺钱包余额记录
     * @param st 开始时间
     * @param et 结束时间
     * @param request 请求实体
     * @param agentChainId 加盟商id
     * @return Pagination
     */
    Pagination queryTenantWalletRecords(Long st, Long et, PageRequest request, Long  agentChainId);
    /**
     * 查询加盟商的可提现金额
     * @return Pagination
     */
    Pagination queryDistributorWithdrawAmountPage(PageRequest request);

}
