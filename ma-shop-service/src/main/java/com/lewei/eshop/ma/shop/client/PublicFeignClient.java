package com.lewei.eshop.ma.shop.client;

import com.lewei.eshop.auth.feign.FeignClientConfig;
import com.lewei.eshop.auth.feign.config.PublicFeignClientConfig;
import com.lewei.eshop.common.request.file.Base64FileRequest;
import com.lewei.eshop.common.request.taoBao.TaobaoDispatchRequest;
import com.lewei.eshop.common.request.taoBao.TaobaoInstanceRequest;
import com.lewei.eshop.common.request.taoBao.TaobaoMinaVersionRequest;
import com.lewei.eshop.common.vo.recharge.RechargeTbConfigVO;
import com.lewei.eshop.common.vo.taoBao.TaobaoDispatchVo;
import com.lewei.eshop.common.vo.taoBao.TaobaoInstanceDetailVo;
import com.lewei.eshop.common.vo.taoBao.TaobaoInstanceVo;
import com.lewei.eshop.common.vo.taoBao.TaobaoMinaVersionVo;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.xcrm.starter.qiniu.UploadResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 公共务务feign client
 * <AUTHOR>
 * @since 2020/7/23
 */
@FeignClient(name = "public-service",path = "/public/v1",configuration = FeignClientConfig.class)
//@FeignClient(name = "public-service",url = "${xcrm.eshop.service}"+":8002/public/v1",configuration = PublicFeignClientConfig.class)
public interface PublicFeignClient {
        /**
         * 查询品牌配置
         * <AUTHOR>
         */
        @GetMapping(value = "/chain/config")
        ChainConfig queryChainConfig();

        /**
         * 查询品牌配置
         * <AUTHOR>
         */
        @GetMapping(value = "/tb/mina/order/check")
        Map checkTaobaoMinaOrder(@RequestParam(value = "miniAppId") String miniAppId,
                                 @RequestParam(value = "userNick")  String userNick);


        /**
         * 查询品牌配置
         * <AUTHOR>
         */
        @GetMapping(value = "/tb/mina/version")
        TaobaoMinaVersionVo queryTaobaoMinaVersionList(@RequestParam(value = "miniAppId") String miniAppId);

        /**
         * 查询品牌配置
         * <AUTHOR>
         */
        @PostMapping(value = "/tb/mina/version")
        Long saveTaobaoMinaVersion(@RequestBody TaobaoMinaVersionRequest request);

        /**
         * 实例化淘宝小程序
         * @param miniAppId
         * @param userNick
         * @param sessionKey
         * @param request
         * @return
         */
        @PostMapping("/tb/mina/instance")
        TaobaoInstanceVo taobaoMinaInstance(@RequestParam(value = "miniAppId") String miniAppId,
                                            @RequestParam(value = "userNick")  String userNick,
                                            @RequestParam(value = "sessionKey")  String sessionKey,
                                            @RequestBody TaobaoInstanceRequest request);

        /**
         * 查询淘宝小程序实例化信息
         * <AUTHOR>
         */
        @GetMapping(value = "/tb/mina/instance")
        TaobaoInstanceDetailVo queryTaobaoMinaInstance(@RequestParam(value = "miniAppId") String miniAppId);


        /**
         * 淘宝小程序更新
         * @param miniAppId
         * @param userNick
         * @param sessionKey
         * @param request
         * @return
         */
        @PostMapping("/tb/mina/instance/update")
        TaobaoInstanceVo taobaoMinaInstanceUpdate(@RequestParam(value = "miniAppId") String miniAppId,
                                            @RequestParam(value = "userNick")  String userNick,
                                            @RequestParam(value = "sessionKey")  String sessionKey,
                                            @RequestBody TaobaoInstanceRequest request);

        /**
         * 淘宝小程序上线
         * @param miniAppId
         * @param userNick
         * @param sessionKey
         * @param request
         * @return
         */
        @PostMapping("/tb/mina/instance/online")
        TaobaoDispatchVo dispatchTaobaoMina(@RequestParam(value = "miniAppId") String miniAppId,
                                            @RequestParam(value = "userNick")  String userNick,
                                            @RequestParam(value = "sessionKey")  String sessionKey,
                                            @RequestBody TaobaoDispatchRequest request);


        /**
         * 文件上传(base64)
         */
        @PostMapping("/file/base64")
        UploadResponse uploadFile(@RequestBody Base64FileRequest form);

        /**
         * 查询淘宝充值规则表
         * @return List<RechargeTbConfigVO>
         */
        @GetMapping(value = "/recharge/tb")
        List<RechargeTbConfigVO> queryRechargeTbConfig();
}
