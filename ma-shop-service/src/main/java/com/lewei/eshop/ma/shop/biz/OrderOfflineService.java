package com.lewei.eshop.ma.shop.biz;

import com.alibaba.fastjson.JSON;
import com.lewei.eshop.common.data.GrowTaskEnum;
import com.lewei.eshop.common.data.GrowTaskPayTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.commission.CommissionBonusRequest;
import com.lewei.eshop.common.request.order.OrderRechargeProfitRequest;
import com.lewei.eshop.common.request.pay.PayWxRequest;
import com.lewei.eshop.common.vo.order.OrderOfflineStatisticsVo;
import com.lewei.eshop.entity.member.MemberBalance;
import com.lewei.eshop.entity.member.MemberRechargeConfig;
import com.lewei.eshop.entity.order.OrderOffline;
import com.lewei.eshop.entity.order.types.OrderOfflineBizType;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.ma.shop.MaShopErrorDef;
import com.lewei.eshop.ma.shop.SysConfig;
import com.lewei.eshop.ma.shop.client.MemberFeignClient;
import com.lewei.eshop.ma.shop.client.PayFeignClient;
import com.lewei.eshop.ma.shop.event.entity.MemberPointEvent;
import com.lewei.eshop.ma.shop.event.entity.MemberScoreEvent;
import com.lewei.eshop.ma.shop.event.entity.ProxyGrowTaskEvent;
import com.lewei.eshop.ma.shop.pay.OrderEvent;
import com.lewei.eshop.ma.shop.resource.request.CashierPayRequest;
import com.lewei.eshop.ma.shop.resource.request.RechargePayRequest;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.db.saas.IIdWorker;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
*
* <AUTHOR>
* @date 2020/8/13
**/
@Service
@Transactional
@Slf4j
public class OrderOfflineService {

    private static Map<String, String> orderTitleMap = new HashMap<>();
    private static Map<String, String> scoreOrderTypeMap = new HashMap<>();
    private static Map<String, String> pointOrderTypeMap = new HashMap<>();
    private static Map<String, String> distProfitProductTypeMap = new HashMap<>();
    static {
        orderTitleMap.put(OrderOfflineBizType.S_OOBZ_CASHIER.value(), BizMessageSource.getInstance().getMessage("cem80001"));
        orderTitleMap.put(OrderOfflineBizType.S_OOBZ_RECHARGE.value(), BizMessageSource.getInstance().getMessage("cem80002"));

        scoreOrderTypeMap.put(OrderOfflineBizType.S_OOBZ_CASHIER.value(), GrowTaskEnum.score_bill.value());
        scoreOrderTypeMap.put(OrderOfflineBizType.S_OOBZ_RECHARGE.value(), GrowTaskEnum.score_recharge.value());

        pointOrderTypeMap.put(OrderOfflineBizType.S_OOBZ_CASHIER.value(), GrowTaskEnum.bill.value());
        pointOrderTypeMap.put(OrderOfflineBizType.S_OOBZ_RECHARGE.value(), GrowTaskEnum.recharge.value());

        distProfitProductTypeMap.put(OrderOfflineBizType.S_OOBZ_CASHIER.value(), "dist_order_cashier");
        distProfitProductTypeMap.put(OrderOfflineBizType.S_OOBZ_RECHARGE.value(), "dist_order_recharge");
    }

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private PayFeignClient payFeignClient;
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private TradeFlowService tradeFlowService;
    @Autowired
    private IIdWorker iIdWorker;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    public Map<String, Object> payCashierOrder(CashierPayRequest request, String appId, Long tenantId, Long opUserId, Long chainId) {
        OrderOffline order = null;
        String paymentType = request.getPaymentMethod();
        if(StringUtils.isNotBlank(request.getOrderSn())) {
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderSn", request.getOrderSn()))
                    .and(Restrictions.eq("dataStatus", 1));
            order = dao.query(queryBuilder, OrderOffline.class);
            if(order != null && !OrderEnum.S_OS_UNPAID.value().equals(order.getStatus())) {
                throw new BizCoreRuntimeException(MaShopErrorDef.ORDER_IS_PAYED);
            }
        }

        if(order == null) {
            order = new OrderOffline();
            order.setCreated(new Timestamp(System.currentTimeMillis()));
            order.setOrderSn(System.currentTimeMillis() + "");
            order.setOrderBizType(OrderOfflineBizType.S_OOBZ_CASHIER.value());
            order.setOrderTitle(orderTitleMap.get(OrderOfflineBizType.S_OOBZ_CASHIER.value()));
            order.setStatus(OrderEnum.S_OS_UNPAID.value());
            order.setMemberId(request.getMemberId());
            order.setOpenId("");
            order.setPaymentMethod(request.getPaymentMethod());
            order.setOrderMoney(request.getOrderMoney());
            order.setPaymentMoney(order.getOrderMoney());
            order.setTenantId(tenantId);
            order.setOrderSource("online");
            order.setOrderUserId(opUserId);
            order.setId(iIdWorker.nextId());

            if(OrderPaymentTypeEnum.S_OPM_OFFLINE.value().equals(paymentType)) {
                //下线付款，直接交易成功
                order.setStatus(OrderEnum.S_OS_DONE.value());
                order.setTradeSuccTime(new Timestamp(System.currentTimeMillis()));
                order.setOrderSource("offline");

                //this.handleOfflinePayType(order, chainId, GrowTaskEnum.score_bill);
            }

            dao.create(order);
        } else {
            order.setUpdated(new Timestamp(System.currentTimeMillis()));
            dao.update(order);
        }

        Map<String, Object> retMap = new HashMap<>(2);
        if(OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(paymentType)) {

            //调取微信支付
            PayWxRequest payWxRequest = new PayWxRequest();
            payWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/ma-shop-dm/pay/wx/cb");
            payWxRequest.setOrderCodes(order.getOrderSn());
            payWxRequest.setOrderTitle(order.getOrderTitle());
            payWxRequest.setPaymentMoney(order.getPaymentMoney());
            payWxRequest.setPayMethod("wx_user_scan");
            payWxRequest.setWxAppId(appId);

            Map payUrl = payFeignClient.callPayApi(payWxRequest);
            retMap.put("payParam", payUrl);
        } else {
            this.handleOfflinePayType(order, chainId, null);
        }
        retMap.put("orderSn", order.getOrderSn());
        return retMap;
    }

    public Map<String, Object> payRechargeOrder(RechargePayRequest request, String appId, Long tenantId, Long opUserId, Long chainId) {

        OrderOffline order = validateOrder(request.getOrderSn());
        String paymentType = request.getPaymentMethod();
        Map<String, BigDecimal> extJsonMap = new HashMap<>();
        if(request.getRechargeConfigId() != null) {
            //根据充值规则，配置本金和赠送金
            SaasQueryBuilder queryConfig = SaasQueryBuilder.where(Restrictions.eq("dataStatus", 1))
                    .and(Restrictions.eq("id", request.getRechargeConfigId())).and(Restrictions.eq("isSupportOnline", 1));
            MemberRechargeConfig rechargeConfig = dao.query(queryConfig, MemberRechargeConfig.class);
            if(rechargeConfig == null) {
                throw new BizCoreRuntimeException(MaShopErrorDef.ORDER_RECHARGE_ERROR);
            } else {
                if (request.getOrderMoney().compareTo(rechargeConfig.getPayMoney()) != 0){
                    request.setOrderMoney(rechargeConfig.getPayMoney());
                }

                if(rechargeConfig.getRechargeAmount() != null && rechargeConfig.getRechargeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    extJsonMap.put("rechargeAmount", rechargeConfig.getRechargeAmount());
                }
                if(rechargeConfig.getGiftAmount() != null && rechargeConfig.getGiftAmount().compareTo(BigDecimal.ZERO) > 0) {
                    extJsonMap.put("giftAmount", rechargeConfig.getGiftAmount());
                }
            }

        } else if(request.getRechargeAmount() != null && request.getRechargeAmount().compareTo(BigDecimal.ZERO) > 0) {
            extJsonMap.put("rechargeAmount", request.getOrderMoney());
        }

        if(extJsonMap.size() == 0) {
            throw new BizCoreRuntimeException(MaShopErrorDef.ORDER_RECHARGE_ERROR);
        }


        if(order == null) {
            //创建默认余额记录
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("memberId", request.getMemberId()))
                    .and(Restrictions.eq("tenantId", tenantId)).and(Restrictions.eq("dataStatus", 1));
            MemberBalance memberBalance = dao.query(queryBuilder, MemberBalance.class);
            if(memberBalance == null) {
                memberBalance = new MemberBalance();
                memberBalance.setBalance(BigDecimal.ZERO);
                memberBalance.setGift(BigDecimal.ZERO);
                memberBalance.setTenantId(tenantId);
                memberBalance.setCreated(new Timestamp(System.currentTimeMillis()));
                memberBalance.setMemberId(request.getMemberId());
                dao.save(memberBalance);
            }

            order = new OrderOffline();
            order.setTenantId(tenantId);
            order.setCreated(new Timestamp(System.currentTimeMillis()));
            order.setOrderSn(System.currentTimeMillis() + "");
            order.setOrderBizType(OrderOfflineBizType.S_OOBZ_RECHARGE.value());
            order.setOrderTitle(orderTitleMap.get(OrderOfflineBizType.S_OOBZ_RECHARGE.value()));
            order.setStatus(OrderEnum.S_OS_UNPAID.value());
            order.setMemberId(request.getMemberId());
            order.setOpenId("");
            order.setPaymentMethod(request.getPaymentMethod());
            order.setOrderMoney(request.getOrderMoney());
            order.setPaymentMoney(order.getOrderMoney());
            order.setExtJson(JSON.toJSONString(extJsonMap));
            order.setOrderSource("online");
            order.setOrderUserId(opUserId);
            order.setId(iIdWorker.nextId());

            if(OrderPaymentTypeEnum.S_OPM_OFFLINE.value().equals(paymentType)) {
                //下线付款，直接交易成功
                order.setStatus(OrderEnum.S_OS_DONE.value());
                order.setTradeSuccTime(new Timestamp(System.currentTimeMillis()));
                order.setOrderSource("offline");

                //this.handleOfflinePayType(order, chainId, GrowTaskEnum.score_recharge);
            }
            dao.create(order);
        } else {
            order.setUpdated(new Timestamp(System.currentTimeMillis()));
            dao.update(order);
        }

        Map<String, Object> retMap = new HashMap<>(2);
        if(OrderPaymentTypeEnum.S_OPM_WECHAT.value().equals(paymentType)) {
            PayWxRequest payWxRequest = new PayWxRequest();
            payWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/ma-shop-dm/pay/wx/cb");
            payWxRequest.setOrderCodes(order.getOrderSn());
            payWxRequest.setOrderTitle(order.getOrderTitle());
            payWxRequest.setPaymentMoney(order.getPaymentMoney());
            payWxRequest.setPayMethod("wx_user_scan");
            payWxRequest.setWxAppId(appId);

            //调取微信支付
            Map payUrl = payFeignClient.callPayApi(payWxRequest);
            retMap.put("payParam", payUrl);
        } else {
            this.handleOfflinePayType(order, chainId, extJsonMap);
        }

        retMap.put("orderSn", order.getOrderSn());
        return retMap;
    }

    public List<OrderOfflineStatisticsVo> queryOrderOfflineStatistics(Long tenantId,Long st,Long et){
        Date stt = null;
        Date ett = null;
        if(st != null) {
            stt = new Timestamp(st);
        }
        if(et != null) {
            ett = new Timestamp(et);
        }
        Ssqb query = Ssqb.create("com.lewei.eshop.ma.shop.order.queryOrderOfflineStatistics")
                .setParam("tenantId",tenantId)
                .setParam("st",stt)
                .setParam("et",ett)
                ;
        return dao.findForList(query,OrderOfflineStatisticsVo.class);
    }

    public OrderOffline queryByOrderSn(String orderSn) {
        return dao.query(SaasQueryBuilder.where(Restrictions.eq("orderSn", orderSn)), OrderOffline.class);
    }

    /**
     * 下线付款处理
     */
    private void handleOfflinePayType(OrderOffline order, Long chainId,  Map<String, BigDecimal> extJsonMap) {

        //保存交易流水
//        tradeFlowService.saveTradeFlow(order);
//
//        if(order.getOrderBizType().equals(OrderOfflineBizType.S_OOBZ_RECHARGE.value())) {
//            //直接充值
//            Ssqb ssqb = Ssqb.create("com.lewei.eshop.ma.shop.order.updateMemberBalanceForRecharge")
//                    .setParam("rechargeAmount", extJsonMap.get("rechargeAmount")).setParam("giftAmount", extJsonMap.get("giftAmount"))
//                    .setParam("memberId", order.getMemberId()).setParam("tenantId", order.getTenantId());
//            dao.updateByMybatis(ssqb);
//        }

        //会员积分处理
        applicationContext.publishEvent(new MemberScoreEvent(this, order.getMemberId(),order.getOrderMoney()
                , scoreOrderTypeMap.get(order.getOrderBizType()) , GrowTaskPayTypeEnum.other.value(),chainId,order.getOrderTitle()));
        //会员成长史处理
       // applicationContext.publishEvent(new MemberPointEvent(this, order.getMemberId(),order.getOrderMoney(), pointOrderTypeMap.get(order.getOrderBizType()), GrowTaskPayTypeEnum.other.value(),order.getChainId()));
        //代理等级处理
        applicationContext.publishEvent(new ProxyGrowTaskEvent(this,order,order.getMemberId()));
        //提成及 分佣处理，发布订单交易成功事件
        applicationContext.publishEvent(new OrderEvent(order));
    }


    private OrderOffline validateOrder(String orderSn) {
        OrderOffline order = null;
        if(StringUtils.isNotBlank(orderSn)) {
            SaasQueryBuilder queryBuilder = SaasQueryBuilder.where(Restrictions.eq("orderSn", orderSn))
                    .and(Restrictions.eq("dataStatus", 1));
            order = dao.query(queryBuilder, OrderOffline.class);
            if(order != null && !OrderEnum.S_OS_UNPAID.value().equals(order.getStatus())) {
                throw new BizCoreRuntimeException(MaShopErrorDef.ORDER_IS_PAYED);
            }
        }
        return order;
    }

    private void asyncHandleCommissionBonus(OrderOffline order, Long chainId) {

        log.debug("asyncHandleCommissionBonus");

        //在本事务提交后再异步调用其他处理
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                taskExecutor.execute(new Runnable() {
                    @Override
                    public void run() {
                        String lang = XcrmThreadContext.getLocale();
                        try {
                            log.debug("asyncHandleCommissionBonus execute run");
                            XcrmThreadContext.setChainId(chainId);
                            XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                            XcrmThreadContext.setLocale(lang);

                            //门店分红及员工提成处理
                            CommissionBonusRequest bonusRequest = new CommissionBonusRequest();
                            bonusRequest.setUserId(order.getOrderUserId());
                            bonusRequest.setProfitOrderAmount(order.getPaymentMoney());
                            bonusRequest.setTenantId(order.getTenantId());
                            bonusRequest.setOrderFrom("offline");
                            bonusRequest.setOrderId(order.getId());
                            bonusRequest.setOrderSn(order.getOrderSn());
                            memberFeignClient.handleCommissionProfit(bonusRequest);

                            //下线充值开单订单分佣
                            OrderRechargeProfitRequest profitRequest = new OrderRechargeProfitRequest();
                            profitRequest.setMemberId(order.getMemberId());
                            profitRequest.setOrderCode(order.getOrderSn());
                            profitRequest.setPayCode(order.getPaySn());
                            profitRequest.setOrderAmount(order.getPaymentMoney());
                            profitRequest.setProductType(distProfitProductTypeMap.get(order.getOrderBizType()));
                            memberFeignClient.handleOrderRechargeProfit(profitRequest);

                        } catch (Exception e) {
                            log.error("", e);
                        } finally {
                            XcrmThreadContext.removeAccessType();
                            XcrmThreadContext.removeChainId();
                            XcrmThreadContext.removeLocale();
                        }
                    }
                });
            }
        });
    }
}