package com.lewei.eshop.ma.shop.resource;

import com.lewei.eshop.ma.shop.client.PublicFeignClient;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;

/**
* <AUTHOR>
* @date 2023/03/14
**/
@Path("/recharge/tb")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class RechargeTbConfigResource extends BaseAuthedResource{

    @Autowired
    private PublicFeignClient publicFeignClient;
    /**
     * 查询配置
     * @return List<RechargeTbConfigVO>
     */
    @GET
    public Response queryRechargeTbConfig() {
        log.debug("RechargeTbConfigResource.queryRechargeTbConfig()");
        return Response.ok(publicFeignClient.queryRechargeTbConfig()).build();
    }
}