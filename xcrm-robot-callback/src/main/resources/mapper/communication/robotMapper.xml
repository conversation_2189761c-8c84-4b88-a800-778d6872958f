<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xcrm.robot.callback">

    <select id="queryMediasByIds" resultType="com.xcrm.robot.callback.communication.dto.MediaDTO">
        SELECT m.id AS mediaId, m.kind, m.title, m.content, m.remark, m.mainImage, m.fileType, m.microApp, ma.appOriginalId
        , ma.appName
        FROM t_pl_robot_media m
        LEFT JOIN t_pl_robot_micro_app ma ON ma.id = m.microAppId AND ma.dataStatus = 1
        WHERE m.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND m.dataStatus = 1
    </select>

    <update id="updateRobotState">
        UPDATE t_pl_robot
        SET state = #{state}, wxAccount = #{wxAccount}, wxAccountAlias = #{wxAccountAlias}, nickName = #{nickName}, headImg = #{headImg}
        WHERE robotCode = #{robotCode}
        AND dataStatus = 1
    </update>

    <select id="queryRobot" resultType="com.xcrm.platform.entity.robot.Robot">
        SELECT * FROM t_pl_robot where robotCode = #{robotCode}
    </select>

    <select id="queryFollowerByRobotWxAccount" resultType="com.xcrm.platform.common.robot.FollowerDTO">
        SELECT wxAccount, wxAccountAlias
        FROM t_pl_robot_follower
        WHERE chainId = #{chainId}
            AND robotWxAcount = #{robotWxAcount}
            AND (nickName = #{nickName} OR formName = #{nickName} )
    </select>
    
    <select id="queryOfflineNotifyRobots" resultType="java.lang.String">
        SELECT robotCode FROM
            t_pl_robot
        WHERE robotCode IN
        <foreach collection="robotCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND isOfflineNotify = 1
        AND state &lt;&gt; 'S_RS_EXPIRED'
    </select>

    <select id="queryRobotByWxAccount" resultType="com.xcrm.platform.entity.robot.Robot">
        SELECT * FROM t_pl_robot where wxAccount = #{wxAccount}
    </select>

    <select id="queryRobotMicroApp" resultType="com.xcrm.platform.entity.robot.RobotMicroApp">
        SELECT * FROM t_pl_robot_micro_app WHERE chainId = #{chainId} AND tenantId IS NULL AND dataStatus = 1 LIMIT 1;
    </select>
</mapper>