package com.xcrm.robot.callback.resource.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
* <AUTHOR>
* @date 2020/1/9
**/
@Data
public class PolicyChatRoomUpdateRequest {


    /**
     * 自动接受群邀请
     */
    @NotNull(message = "isAutoPass is required")
    private Boolean isAutoPass;
    /**
     * 每日通过上限人数 -1不限制
     */
    @NotNull(message = "dayPassLimit is required")
    @Range(min = -1, max = 500)
    private Integer dayPassLimit;


}