package com.lewei.eshop.auth.annotation;

import java.lang.annotation.*;

/**
* api资源访问策略
* <AUTHOR>
* @date 2018-09-19 14:15
**/
@Inherited
@Target({ ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiPolicy {
    /**
     * api资源需要通过管理员短信验证
     * @return
     */
    boolean isAdminSmsCheck() default false;

    /**
     * api资源不需要验证访问者身份，默认false，即需要身份验证
     * @return
     */
    boolean isNoAuth() default false;
}