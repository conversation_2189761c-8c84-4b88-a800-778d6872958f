package com.lewei.eshop.common.vo.oq.product;

import com.lewei.eshop.common.i18n.annotation.I18nSysCode;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2020/8/31
 */
@Data
public class OqPointProductDetailVo {

    private Long id;
    /**
     * 父spuId
     */
    private Long spuId;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 图片
     */
    private String mainImage;
    /**
     * 价格
     */
    private BigDecimal priceFee;
    /**
     * 成本价
     */
    private BigDecimal primeCostFee;
    /**
     * 商品详情
     */
    private String productIntro;
    /**
     * 兑换积分
     */
    private BigDecimal point;
    /**
     * 是否限兑换次数
     */
    private Boolean isLimit;
    /**
     * 限兑换次数
     */
    private Integer limitNum;
    /**
     * 是否参与
     */
    private Boolean isOpen;
}
