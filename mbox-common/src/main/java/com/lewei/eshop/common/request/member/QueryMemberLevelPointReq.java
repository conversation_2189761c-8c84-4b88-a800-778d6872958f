package com.lewei.eshop.common.request.member;

import com.lewei.eshop.common.request.PageRequest;
import lombok.Data;

import javax.ws.rs.QueryParam;

/**
 * <AUTHOR>
 * @since 2022/8/9
 */
@Data
public class QueryMemberLevelPointReq extends PageRequest {
    /**
     * 会员等级
     */
    @QueryParam("levelId")
    private Long levelId;
    /**
     * 周期类型
     */
    @QueryParam("taskType")
    private String taskType;
    /**
     * 请求实体
     */
    @QueryParam("type")
    private String type;

    @QueryParam("queryKey")
    private String queryKey;

}
