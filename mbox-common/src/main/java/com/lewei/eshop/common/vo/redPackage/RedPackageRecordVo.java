package com.lewei.eshop.common.vo.redPackage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/6/1
 */
@Data
public class RedPackageRecordVo {

    private Long id;
    private Long memberId;
    /**
     * 会员昵称
     */
    private String memberName;
    /**
     * 会员头像
     */
    private String headImage;

    private String mobile;

    /**
     * 金额
     */
    private BigDecimal money;


    private Date   created;


}
