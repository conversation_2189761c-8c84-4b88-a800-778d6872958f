package com.lewei.eshop.common.request.space;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 批量删除
 */
@Data
public class DeleteDifferentSpaceRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 异空间id
     */
    @NotEmpty(message = "differentSpaceIds")
    private List<Long> differentSpaceIds;

}
