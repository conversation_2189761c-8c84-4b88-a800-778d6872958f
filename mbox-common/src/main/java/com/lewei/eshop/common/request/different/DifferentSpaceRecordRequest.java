package com.lewei.eshop.common.request.different;

import com.lewei.eshop.common.request.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.ws.rs.QueryParam;

/**
 * 异度空间抽奖请求
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
@Data
public class DifferentSpaceRecordRequest extends PageRequest {

    @QueryParam("differentSpaceId")
    private Long differentSpaceId;

    @QueryParam("isMember")
    private Boolean isMember;
}
