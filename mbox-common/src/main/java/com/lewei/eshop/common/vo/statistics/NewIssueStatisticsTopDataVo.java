package com.lewei.eshop.common.vo.statistics;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date Created 2021/4/1910:47
 * @description
 **/
@Data
public class NewIssueStatisticsTopDataVo {
    /**
     * 赠送余额
     */
    private BigDecimal totalBalance;
    /**
     * 赠送积分
     */
    private BigDecimal totalFraction;
    /**
     * 赠送怒气值
     */
    private BigDecimal totalScore;
    /**
     * 赠送商品数量
     */
    private Integer totalProductQuantity;
    /**
     * 赠送商品金额
     */
    private BigDecimal totalPrice;
    /**
     * 赠送商品成本
     */
    private BigDecimal totalPrimeCostFee;
    /**
     * 赠送优惠券数量
     */
    private Integer totalCouponQuantity;
    /**
     * 免单次数
     */
    private Integer totalFreeCount;
    /**
     * 免单金额
     */
    private BigDecimal totalFreeFee;
    /**
     * 成长值
     */
    private BigDecimal totalPoint;

    private  BigDecimal treasurePrimeCostFee;

    private BigDecimal tbTicket;
    /**
     * 已开箱成本
     */
    private BigDecimal endPrimeCostFee;




}
