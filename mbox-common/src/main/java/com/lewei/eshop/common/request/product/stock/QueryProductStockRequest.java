package com.lewei.eshop.common.request.product.stock;

import com.lewei.eshop.common.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.ws.rs.QueryParam;

/**
 * <AUTHOR>
 * @since 2021/3/3
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryProductStockRequest extends PageRequest {

    @QueryParam("queryKey")
    private String queryKey;

    @QueryParam("stockKind")
    private String stockKind;

    @QueryParam("st")
    private Long st;

    @QueryParam("et")
    private Long et;

    @QueryParam("billId")
    private Long billId;

    @QueryParam("skuId")
    private Long skuId;

    /**
     * 排序字段
     */
    @QueryParam("orderName")
    private String orderName ;
    /**
     * 排序
     */
    @QueryParam("order")
    private String order ;


}
