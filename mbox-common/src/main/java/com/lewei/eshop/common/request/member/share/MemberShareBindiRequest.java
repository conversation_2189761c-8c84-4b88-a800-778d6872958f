package com.lewei.eshop.common.request.member.share;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.ws.rs.DefaultValue;
import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberShareBindiRequest implements Serializable {

    private static final long serialVersionUID = 872680605724075033L;

    /**
     * 会员id
     */
    @NotNull(message = "memberId is required")
    private Long memberId;
    /**
     * 下级id
     */
    @NotNull(message = "childId is required")
    private Long childId;

    @DefaultValue(value = "true")
    private Boolean isConfig;
}
