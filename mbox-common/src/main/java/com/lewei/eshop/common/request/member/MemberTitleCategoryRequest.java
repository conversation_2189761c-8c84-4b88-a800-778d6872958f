package com.lewei.eshop.common.request.member;

import lombok.Data;
import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @since 2022-08-24
 */
@Data
public class MemberTitleCategoryRequest {

    /**
     * 勋章分类
     */
    @NotEmpty(message = "勋章分类不允许为空")
    @Size(max = 10, message = "勋章分类长度不能大于10个字符")
    private String categoryName;

    /**
     * 是否展示
     */
    @NotNull(message = "是否展示不允许为空")
    private Boolean isShow;

    /**
     * 图片
     */
    private String pic;
}
