package com.lewei.eshop.common.request.statistics;

import com.lewei.eshop.common.request.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.ws.rs.QueryParam;

/**
 * <AUTHOR>
 */
@Data
public class BlackMarketAuctionStatisticsListRequest extends PageRequest {
    /**
     * 时间开始
     */
    @NotNull(message = "时间不可为空")
    @QueryParam("st")
    private Long st;
    /**
     * 时间结束
     */
    @NotNull(message = "时间不可为空")
    @QueryParam("et")
    private Long et;
    /**
     * 年 月 日
     */
    @QueryParam("timeType")
    private String timeType;

    /** 排序字段
     */
    @QueryParam("orderName")
    private String orderName ;
    /**
     * 排序
     */
    @QueryParam("order")
    private String order ;

}
