package com.lewei.eshop.common.vo.different;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 异空间奖品
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
@Data
public class MaDifferentSpaceRewardVo {
    /**
     * 奖品Id
     */
    private Long id;

    /**
     * 异空间id
     */
    private Long differentSpaceId;
    /**
     * spuId
     */
    private Long spuId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 分类id
     */
    private Long categoryId;
    /**
     * 中奖概率
     */
    private Double odds;
    /**
     * 排序
     */
    private Integer priority;
    /**
     * 商品图片
     */
    private String spuMainImage;
    /**
     * 商品名称
     */
    private String spuName;
    /*  *
     * 分类
     */
    private String categoryName;
    /**
     * 分类图片
     */
    private String categoryPic;
    /**
     * 售价
     */
    private BigDecimal priceFee;
    /**
     * 成本价
     */
    private BigDecimal primeCostFee;
}
