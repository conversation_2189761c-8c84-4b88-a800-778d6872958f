package com.lewei.eshop.common.request.space;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class DifferentSpaceRequest {
    /**
     * 标题
     */
    @NotEmpty(message = "title is required")
    @Size(max = 20,message = "title max size 20")
    private String title;

    /**
     * 盲盒的spuId
     */
    @NotNull(message = "boxSpuId is required")
    private Long boxSpuId;

    /**
     * 进度数量
     */
    @NotNull(message = "progressNum is required")
    @Min(value = 1,message = "购买数量不能小于1")
    @Max(value = 9999,message = "购买数量不能大于9999")
    private Integer progressNum;

    /**
     * 清空类型id
     */
    @NotNull(message = "clearCategoryId is required")
    private Long clearCategoryId;

    /**
     * 奖品列表
     */
    @Valid
    @NotEmpty(message = "rewards is required")
    private List<Reward> rewards;



    @Data
    public static class Reward{

        /**
         * spuId
         */
        @NotNull(message = "spuId is required")
        private Long spuId;
        /**
         * skuId
         */
        @NotNull(message = "skuId is required")
        private Long skuId;
        /**
         * 分类Id
         */
        @NotNull(message = "categoryId is required")
        private Long categoryId;
        /**
         * 中奖概率
         */
        @NotNull(message = "odds is required")
        private Double odds;
        /**
         * 排序
         */
        @NotNull(message = "priority is required")
        private Integer priority;
    }

}
