package com.lewei.eshop.common.vo.space;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 异空间
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class DifferentSpaceDetailVo {
    /**
     * id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 盲盒的spuId
     */
    private Long boxSpuId;
    /**
     * 进度数量
     */
    private Integer progressNum;
    /**
     * 清空类型id
     */
    private Long clearCategoryId;
    /**
     * 状态
     */
    private String status;
    /**
     * 创建时间
     */
    private Date created;

    private String boxName;

    private List<Reward> rewards;

    @Data
    public static class Reward{

        private Long spaceRewardId;

        /**
         * spuId
         */
        private Long spuId;
        /**
         * skuId
         */
        private Long skuId;
        /**
         * 分类Id
         */
        private Long categoryId;
        /**
         * 中奖概率
         */
        private Double odds;
        /**
         * 排序
         */
        private Integer priority;
        /**
         * 分类名称
         */
        private String productCategoryName;
        /**
         * 商品名称
         */
        private String rewardName;
        /**
         * 商品主图
         */
        private String mainImage;
        /**
         * 售价
         */
        private BigDecimal priceFee;
        /**
         * 成本价
         */
        private BigDecimal primeCostFee;
        /**
         * 回收价
         */
        private BigDecimal recoveryFee;
    }
}
