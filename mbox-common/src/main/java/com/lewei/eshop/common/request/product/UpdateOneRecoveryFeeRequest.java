package com.lewei.eshop.common.request.product;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/25
 */
@Data
public class UpdateOneRecoveryFeeRequest {
    /**
     * 产品ids
     */
    @NotNull(message = "spuId is requried")
    private Long spuId;

    @NotNull(message = "recoveryFee is requried")
    @DecimalMin(value = "0",message = "不能小于0")
    private BigDecimal recoveryFee;
}
