package com.lewei.eshop.common.request.doll;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 更改状态请求参数
 *
 * <AUTHOR>
 * @since 2023/8/7
 */
@Data
public class UpdateDollStatusRequest {

    @NotNull(message = "id不能为空")
    List<Long> ids;

    @NotEmpty(message = "状态不能为空")
    String status;

    @NotNull(message = "展示信息不能为空")
    String showConfig;


}
