package com.lewei.eshop.common.vo.product.stock;

import com.lewei.eshop.common.i18n.annotation.I18nSysCode;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/3/5
 */
@Data
public class ProductStockBillVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 调出方租户ID
     */
    private Long outTenantId;
    /**
     * 调出方店铺名
     */
    private String outShopName;

    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 单据状态
     */
    private String billStatus;
    /**
     * 单据状态Desc
     */
    @I18nSysCode
    private String billStatusDesc;
    /**
     * 类型 1出库 2入库
     */
    private Integer stockType;
    /**
     * 出入库子类型
     */
    private String stockKind;
    /**
     * 出入库子类型
     */
    @I18nSysCode
    private String stockKindDesc;
    /**
     * 出入库时间
     */
    private Date stockDate;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 创建人Id
     */
    private Long userId;

    /**
     * 创建人
     */
    private String realName;
    /**
     * 是否盘平
     */
    private Boolean isPanping;
    /**
     * 商品数
     */
    private Integer skuNum;



}
