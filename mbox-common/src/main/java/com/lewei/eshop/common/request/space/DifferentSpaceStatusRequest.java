package com.lewei.eshop.common.request.space;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
@Data
public class DifferentSpaceStatusRequest {


    /**
     * ids
     */
    @NotNull(message = "differentSpaceIds is required")
    private List<Long> differentSpaceIds;
    /**
     * 状态
     */
    @NotEmpty(message = "status is required")
    private String status;



}
