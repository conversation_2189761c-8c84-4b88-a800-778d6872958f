package com.lewei.eshop.common.request.statistics;

import lombok.Data;

import javax.ws.rs.QueryParam;

/**
 * <AUTHOR>
 * @since 2020/12/14
 */
@Data
public class SearchSignRankListRequest {

    @QueryParam("code")
    private String code;

    @QueryParam("searchKey")
    private String searchKey;

    @QueryParam("orderBy")
    private String orderBy;

    @QueryParam("orderType")
    private String orderType;

    @QueryParam("st")
    private Long st;

    @QueryParam("et")
    private Long et;

    @QueryParam("isRecover")
    private Boolean isRecover;

}
