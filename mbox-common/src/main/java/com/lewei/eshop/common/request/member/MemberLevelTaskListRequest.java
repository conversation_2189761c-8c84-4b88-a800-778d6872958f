package com.lewei.eshop.common.request.member;

import com.lewei.eshop.common.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.ws.rs.QueryParam;

/**
 * <p>
 * 收货地址表请求实体
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberLevelTaskListRequest extends PageRequest {


    /**
     * 会员等级id
     */
    @QueryParam("levelId")
    private Long levelId;

    /**
     * 任务类型
     */
    @QueryParam("type")
    private String type;
    /**
     * 搜索
     */
    @QueryParam("queryKey")
    private String queryKey;

}
