package com.lewei.eshop.common.vo.shop;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/12/28
 */
@Data
public class MaTenantDetailVO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * tel
     */
    private String tel;

    /**
     * 企业logo
     */
    private String logo;

    /**
     * 门头照片
     */
    private String shopImage;

    /**
     * 营业开始时间
     */
    private String businessStartTime;

    /**
     * 营业结束时间
     */
    private String businessEndTime;

    /**
     * province
     */
    private Integer province;

    /**
     * city
     */
    private Integer city;

    /**
     * area
     */
    private Integer area;
    /**
     * province
     */
    private String provinceDesc;

    /**
     * city
     */
    private String cityDesc;

    /**
     * area
     */
    private String areaDesc;

    /**
     * 店铺地址
     */
    private String address;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 店铺介绍
     */
    private String shopIntro;
    /**
     * 经度
     */
    private BigDecimal lon;
    /**
     * 纬度
     */
    private BigDecimal lat;

    private String jsonObj;
}
