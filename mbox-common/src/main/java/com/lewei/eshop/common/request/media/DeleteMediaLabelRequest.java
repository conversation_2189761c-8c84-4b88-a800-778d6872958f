package com.lewei.eshop.common.request.media;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 媒体表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Data
public class DeleteMediaLabelRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 标签id
     */
    @NotEmpty(message = "标签id不能为空")
    private List<Long> labelIds;

}
