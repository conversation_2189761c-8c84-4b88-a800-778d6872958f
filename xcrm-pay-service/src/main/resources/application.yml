server:
  port: 9002
  servlet:
    context-path: /pay

spring:
  profiles:
    active: ${project.profile}
  datasource:
    url: ${db.jdbc.url}
    username: ${db.jdbc.username}
    password: ${db.jdbc.password}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
  redis:
      database: 0
      host: ${redis.host}
      port: ${redis.port}
      password: ${redis.password}
      lettuce:
        pool:
          max-active: 200
          max-wait: 10000ms
          max-idle: 100
          min-idle: 50
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html

xcrm:
  serverId : ${serverId}
  pay:
    callback: ${pay.callback.url}
  k8s:
    namespace: paas
    config: k8s-config-prod.yml

mybatis:
  mapper-locations: classpath*:mapper/**/*.xml,classpath*:mapper/**/*.xml

pay:
  aliyun:
    accessKeyId: LTAI5tB9y9jwzF1P3Ri2iVWo
    accessKeySecret: ******************************