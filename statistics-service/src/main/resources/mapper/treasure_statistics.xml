<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.treasure.statistics">


    <select id="queryExchangeProductInfo" resultType="com.lewei.eshop.common.vo.treasure.TreasureStatisticsTopDataVo">
        SELECT  IFNULL(SUM(tpr.primeCostFee),0) exchangeProductCost,
        COUNT(tpr.id) exchangeProductCount,
        IFNULL(SUM(tpr.treasureNum),0) treasureNum
        FROM t_pl_treasure_product_record tpr
        WHERE
        tpr.chainId =  #{chainId}
        AND tpr.dataStatus = 1
        <if test="st != null">
            AND tpr.created &gt;= #{st}
        </if>
        <if test="et != null">
            AND tpr.created &lt;= #{et}
        </if>
        <if test="treasureIds != null and treasureIds.size() > 0">
            AND tpr.treasureId IN
            <foreach collection="treasureIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="queryTreasureCount" resultType="java.lang.Integer">

		SELECT SUM(pt.quantity) treasureCount
		FROM t_pl_member_treasure pt
		WHERE
        pt.chainId =  #{chainId}
        AND pt.dataStatus = 1
        <if test="treasureIds != null and treasureIds.size() > 0">
            AND pt.treasureId IN
            <foreach collection="treasureIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="queryTreasureTotal" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(IF(mtr.type = 'income', mtr.quantity,0)) ,0) treasureTotal
        FROM t_pl_member_treasure_record mtr
        WHERE mtr.chainId =  #{chainId}
        <if test="st != null">
            AND mtr.created &gt;= #{st}
        </if>
        <if test="et != null">
            AND mtr.created &lt;= #{et}
        </if>
        <if test="treasureIds != null and treasureIds.size() > 0">
            AND mtr.treasureId IN
            <foreach collection="treasureIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryTreasureStatisticsBottomData" resultType="com.lewei.eshop.common.vo.treasure.TreasureStatisticsBottomDataVo">

        SELECT
        m.id,
        m.memberName,
        m.headImage,
        m.mobile,
        aa.treasureCount treasureCount,
        bb.exchangeProductCost exchangeProductCost,
        bb.exchangeProductCount exchangeProductCount,
        bb.treasureNum treasureNum,
        cc.treasureTotal treasureTotal

        FROM  t_t_member m
        LEFT JOIN
        (
        SELECT SUM(pt.quantity) treasureCount, pt.memberId,pt.treasureId
        FROM t_pl_member_treasure pt
        WHERE
        pt.chainId =  #{chainId}
        AND pt.dataStatus = 1
        <if test="treasureIds != null and treasureIds.size() > 0">
            AND pt.treasureId IN
            <foreach collection="treasureIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY pt.memberId

        ) aa ON m.id = aa.memberId
        LEFT JOIN (
        SELECT  IFNULL(SUM(tpr.primeCostFee),0) exchangeProductCost,tpr.memberId,
        COUNT(tpr.id) exchangeProductCount,
        IFNULL(SUM(tpr.treasureNum),0) treasureNum
        FROM t_pl_treasure_product_record tpr
        WHERE
        tpr.chainId =  #{chainId}
        AND tpr.dataStatus = 1
        <if test="st != null">
            AND tpr.created &gt;= #{st}
        </if>
        <if test="et != null">
            AND tpr.created &lt;= #{et}
        </if>
        <if test="treasureIds != null and treasureIds.size() > 0">
            AND tpr.treasureId IN
            <foreach collection="treasureIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY tpr.memberId
        ) bb ON m.id = bb.memberId

        LEFT JOIN
        (SELECT IFNULL(SUM(IF(mtr.type = 'income', mtr.quantity,0)) ,0) treasureTotal, mtr.memberId
            FROM t_pl_member_treasure_record mtr
            WHERE mtr.chainId =  #{chainId}
            <if test="st != null">
                AND mtr.created &gt;= #{st}
            </if>
            <if test="et != null">
                AND mtr.created &lt;= #{et}
            </if>
            <if test="treasureIds != null and treasureIds.size() > 0">
                AND mtr.treasureId IN
                <foreach collection="treasureIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY mtr.memberId
        ) cc ON m.id = cc.memberId


        WHERE
        m.chainId =  #{chainId}
        <if test="queryKey != null and queryKey != ''">
            AND (
            m.mobile LIKE CONCAT('%', #{queryKey}, '%')
            OR m.memberName LIKE CONCAT('%', #{queryKey}, '%')
            )
        </if>


        GROUP BY m.id
        HAVING (treasureCount>0 OR exchangeProductCost >0 OR exchangeProductCount >0 OR treasureNum >0 OR treasureTotal > 0)
        <choose>
            <when test="orderName != null and orderName != '' and order != null and order != ''">
                ORDER BY ${orderName} ${order}
            </when>
            <otherwise>
                ORDER BY m.id DESC
            </otherwise>
        </choose>
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>


    </select>
</mapper>