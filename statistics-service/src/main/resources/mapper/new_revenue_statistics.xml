<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.revenue.statistics">


        <select id="queryRevenueStatisticsTopData" resultType="com.lewei.eshop.common.vo.statistics.RevenueStatisticsTopDataVO">
           SELECT
            SUM( IF((tf.tradeType = 'S_TT_BUY_BOX'),tf.tradeAmount,0) ) totalCashBoxMoney,
            TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_BUY_BOX'),tf.tradeAmount,0) )  / COUNT(IF((tf.tradeType = 'S_TT_BUY_BOX'), memberId,NULL)),2) totalCashBoxMoneyUntil,
            SUM( IF((tf.tradeType = 'S_TT_RECHARGE'),tf.tradeAmount,0) ) totalCashRechargeMoney,
            TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_RECHARGE'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_RECHARGE'), memberId,NULL)) ,2) totalCashRechargeMoneyUntil,
            SUM( IF((tf.tradeType = 'S_TT_BUY_PRODUCT'),tf.tradeAmount,0) ) totalCashProductMoney,
            TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_BUY_PRODUCT'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_BUY_PRODUCT'), memberId,NULL)),2) totalCashProductMoneyUntil,
            SUM( IF((tf.tradeType = 'S_TT_SHIP'),tf.tradeAmount,0) ) totalCashShipMoney,
            TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_SHIP'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_SHIP'), memberId,NULL)),2) totalCashShipMoneyUntil,
            SUM( IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_RECHARGE' OR tf.tradeType = 'S_TT_BUY_PRODUCT' OR tf.tradeType = 'S_TT_SHIP' ),tf.tradeAmount,0) ) totalCashMoney,
            TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_RECHARGE' OR tf.tradeType = 'S_TT_BUY_PRODUCT' OR tf.tradeType = 'S_TT_SHIP' ),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_RECHARGE' OR tf.tradeType = 'S_TT_BUY_PRODUCT' OR tf.tradeType = 'S_TT_SHIP'), memberId,NULL)),2) totalCashMoneyUntil
            FROM t_t_trade_flow tf
            WHERE tf.chainId = #{chainId}
            AND tf.dataStatus = 1
            AND tf.payType = 'S_TPT_CASH'
            <if test="st != null">
                AND tf.created &gt;= #{st}
            </if>
            <if test="et != null">
                AND tf.created &lt;= #{et}
            </if>

        </select>


    <select id="queryRevenueStatisticsBottomData" resultType="com.lewei.eshop.common.vo.statistics.RevenueStatisticsBottomDataVO">
        SELECT
        tf.created created,
        SUM( IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_BUY_BOX_CARD'),tf.tradeAmount,0) ) totalCashBoxMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_BUY_BOX_CARD'),tf.tradeAmount,0) )  / COUNT(IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_BUY_BOX_CARD'), memberId,NULL)),2) totalCashBoxMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_RECHARGE'),tf.tradeAmount,0) ) totalCashRechargeMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_RECHARGE'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_RECHARGE'), memberId,NULL)) ,2) totalCashRechargeMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_BUY_PRODUCT'),tf.tradeAmount,0) ) totalCashProductMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_BUY_PRODUCT'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_BUY_PRODUCT'), memberId,NULL)),2) totalCashProductMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_SERVICE'),tf.tradeAmount,0) ) totalCashServiceMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_SERVICE'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_SERVICE'), memberId,NULL)) ,2) totalCashServiceMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_CROWD_FUNDING'),tf.tradeAmount,0) ) totalCashCrowdMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_CROWD_FUNDING'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_CROWD_FUNDING'), memberId,NULL)),2) totalCashCrowdMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_DIY_BOX'),tf.tradeAmount,0) ) totalCashDiyBoxMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_DIY_BOX'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_DIY_BOX'), memberId,NULL)),2) totalCashDiyBoxMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_SHIP'),tf.tradeAmount,0) ) totalCashShipMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_SHIP'),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_SHIP'), memberId,NULL)),2) totalCashShipMoneyUntil,
        SUM( IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_RECHARGE' OR tf.tradeType = 'S_TT_BUY_PRODUCT' OR tf.tradeType = 'S_TT_SHIP' OR tf.tradeType = 'S_TT_BUY_BOX_CARD' OR tf.tradeType = 'S_TT_DIY_BOX' ),tf.tradeAmount,0) ) totalCashMoney,
        TRUNCATE(SUM( IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_RECHARGE' OR tf.tradeType = 'S_TT_BUY_PRODUCT' OR tf.tradeType = 'S_TT_SHIP' OR tf.tradeType = 'S_TT_BUY_BOX_CARD' OR tf.tradeType = 'S_TT_DIY_BOX' ),tf.tradeAmount,0) ) / COUNT(IF((tf.tradeType = 'S_TT_BUY_BOX' OR tf.tradeType = 'S_TT_RECHARGE' OR tf.tradeType = 'S_TT_BUY_PRODUCT' OR tf.tradeType = 'S_TT_SHIP' OR tf.tradeType = 'S_TT_BUY_BOX_CARD' OR tf.tradeType = 'S_TT_DIY_BOX'), memberId,NULL)),2) totalCashMoneyUntil
        FROM t_t_trade_flow tf
        WHERE tf.chainId = #{chainId}
        AND tf.dataStatus = 1
        AND tf.payType = 'S_TPT_CASH'
        <if test="st != null">
            AND tf.created &gt;= #{st}
        </if>
        <if test="et != null">
            AND tf.created &lt;= #{et}
        </if>
        GROUP BY DATE_ZONE_FORMAT(tf.created,#{dateOffset},'%Y-%m-%d')
        <choose>
            <when test="orderName != null and orderName != '' and order != null and order != ''">
                ORDER BY ${orderName} ${order}
            </when>
            <otherwise>
                ORDER BY tf.id DESC
            </otherwise>
        </choose>
        <if test="start != null and start >= 0 and pageSize != null and pageSize >= 0">
            LIMIT #{start},#{pageSize}
        </if>
        <if test="start != null and start > 0 and pageSize == null">
            LIMIT #{pageSize}
        </if>
    </select>
</mapper>