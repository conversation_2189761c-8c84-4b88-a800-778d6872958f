package com.lewei.eshop.statistics.resource;

import com.lewei.eshop.auth.BaseAuthedResource;
import com.lewei.eshop.common.request.statistics.QueryDiyBoxStatisticsReq;
import com.lewei.eshop.statistics.biz.IDiyBoxStatisticsService;
import com.xcrm.core.jersey.common.XcrmMediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;


/**
  * DIY盲盒统计
  * <AUTHOR>
  * @date 2023/05/19
  */
@Path("/diy/box/statistics")
@Produces(XcrmMediaType.APPLICATION_JSON)
@Slf4j
public class DiyBoxStatisticsResource extends BaseAuthedResource {

    @Autowired
    private IDiyBoxStatisticsService dictStatisticsService;

    /**
     * 查询DIY盲盒统计top
     * @param st 开始
     * @param et 结束
     * @return DiyBoxTopStatisticsVo
     */
    @GET
    @Path("/top/data")
    public Response queryDiyBoxTopData(@QueryParam("st") Long st,
                                       @QueryParam("et") Long et,
                                       @QueryParam("isRecover") Boolean isRecover) {
        log.debug("DiyBoxStatisticsResource.queryDiyBoxTopData(st={},et={})",st,et);
        return Response.ok(dictStatisticsService.queryDiyBoxTopData(st,et,isRecover)).build();
    }

    /**
     * 查询DIY盲盒统计列表
     * @param req 请求
     * @return Pagination
     */
    @GET
    @Path("/page")
    public Response queryDiyBoxPage(@Valid @BeanParam QueryDiyBoxStatisticsReq req){
        log.debug("DiyBoxStatisticsResource.queryDiyBoxPage(req = #{})",req);
        return Response.ok(dictStatisticsService.queryDiyBoxPage(req)).build();
    }
}
