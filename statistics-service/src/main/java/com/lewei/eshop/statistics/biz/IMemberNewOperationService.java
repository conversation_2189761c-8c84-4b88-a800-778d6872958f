package com.lewei.eshop.statistics.biz;

import com.lewei.eshop.common.request.PageRequest;
import com.lewei.eshop.common.request.statistics.MemberNewOperationTopDataRequest;
import com.lewei.eshop.common.vo.statistics.MemberNewOperationTopDataVo;
import com.lewei.eshop.common.vo.statistics.MemberNewOperationVo;
import com.xcrm.common.page.Pagination;

import java.util.List;

/**
 * 新会员报表
 * <AUTHOR>
 * @date Created 2021/12/29
 **/
public interface IMemberNewOperationService {
    /**
     * 会员统计最上面数据
     * @param request 时间开始-结束
     * @param chainId 连锁店id
     * @return MemberNewOperationTopDataVo
     */
    MemberNewOperationTopDataVo queryMemberTopData(MemberNewOperationTopDataRequest request, Long chainId);
    /**
     * 会员手动操作
     * @param order order
     * @param orderName orderName
     * @param request 分页
     * @return Pagination
     */
    Pagination queryMemberOperationPage(Long st,Long et, String order, String orderName, PageRequest request,Boolean isRecover);
    /**
     * 会员手动操作统计导出
     * @param order order
     * @param orderName orderName
     * @return Pagination
     */
    List<MemberNewOperationVo> queryMemberOperationPageExport(Long st,Long et, String order, String orderName,Boolean isRecover);
}
