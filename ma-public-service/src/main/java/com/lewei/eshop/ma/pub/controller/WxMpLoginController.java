package com.lewei.eshop.ma.pub.controller;

import cn.hutool.core.net.URLEncoder;
import com.lewei.eshop.entity.sso.SSOMpApp;
import com.lewei.eshop.entity.sso.types.MpTypeEnum;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.expression.Restrictions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.NotFoundException;
import java.nio.charset.StandardCharsets;

/**
 * 微信公众号扫码登录
 * <AUTHOR>
 */
@Controller
@Slf4j
public class WxMpLoginController {

    @Autowired
    private BaseDaoSupport dao;

    //扫码二维码后跳转地址
    @RequestMapping(value = "/wx_login", method = RequestMethod.GET)
    public void scanLogin(HttpServletRequest req, HttpServletResponse resp) throws Exception {
        String appId = req.getParameter("appId");
        QueryBuilder queryBuilder = QueryBuilder.where(Restrictions.eq("appId", appId));
        SSOMpApp ssoMpApp = dao.query(queryBuilder,SSOMpApp.class);
        if (ssoMpApp == null) {
            throw new NotFoundException("公众号未配置");
        }

        String queryString = req.getQueryString();
        String redirectUrl = ssoMpApp.getRedirectHost();
        if(StringUtils.isNotBlank(req.getQueryString())) {
            if(redirectUrl.indexOf("?") > 0) {
                redirectUrl += "&" + queryString;
            } else {
                redirectUrl += "?" + queryString;
            }

        }
        String wxOauth2Url = this.getWxOauth2Url(redirectUrl, appId);
        resp.sendRedirect(wxOauth2Url);
    }

    private static final String WX_LOGIN_AUTH2_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s" +
            "&response_type=code&scope=snsapi_userinfo&state=login&connect_redirect=1#wechat_redirect";

    public String getWxOauth2Url(String redirectUrl, String appId) {
        redirectUrl = URLEncoder.DEFAULT.encode(redirectUrl, StandardCharsets.UTF_8);
        return String.format(WX_LOGIN_AUTH2_URL, appId, redirectUrl);
    }

//    @Override
//    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
//        logger.debug("----WxMpLoginServlet.doGet()----");
//        //https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx2be5e707d3adec2a&redirect_uri=
//        // &response_type=code&scope=snsapi_userinfo&state=login&connect_redirect=1#wechat_redirect
//        doPost(req, resp);
//    }



    //扫码二维码后跳转地址
    @RequestMapping(value = "/wx_login_v2", method = RequestMethod.GET)
    public void scanLoginV2(HttpServletRequest req, HttpServletResponse resp) throws Exception {
        String appId = req.getParameter("appId");
        QueryBuilder queryBuilder = QueryBuilder.where(Restrictions.eq("appId", appId));
        SSOMpApp ssoMpApp = dao.query(queryBuilder,SSOMpApp.class);
        if (ssoMpApp == null) {
            throw new NotFoundException("公众号未配置");
        }
        String queryString = req.getQueryString();
        String redirectUrl = ssoMpApp.getRedirectHost() + "/" + ssoMpApp.getChainId();
        if(StringUtils.isNotBlank(req.getQueryString())) {
            if(redirectUrl.indexOf("?") > 0) {
                redirectUrl += "&" + queryString;
            } else {
                redirectUrl += "?" + queryString;
            }

        }
        String wxOauth2Url = this.getWxOauth2UrlV2(redirectUrl, appId);
        resp.sendRedirect(wxOauth2Url);
    }

    //扫码二维码后跳转地址
    @RequestMapping(value = "/wx_login_v3", method = RequestMethod.GET)
    public void scanLoginV3(HttpServletRequest req, HttpServletResponse resp) throws Exception {
        String appId = req.getParameter("appId");
        QueryBuilder queryBuilder = QueryBuilder.where(Restrictions.eq("appId", appId))
                .and(Restrictions.eq("mpType", MpTypeEnum.MALL.value()));
        SSOMpApp ssoMpApp = dao.query(queryBuilder,SSOMpApp.class);
        if (ssoMpApp == null) {
            throw new NotFoundException("公众号未配置");
        }
        String queryString = req.getQueryString();
        String redirectUrl = ssoMpApp.getRedirectHost();
        if(StringUtils.isNotBlank(req.getQueryString())) {
            if(redirectUrl.indexOf("?") > 0) {
                redirectUrl += "&" + queryString;
            } else {
                redirectUrl += "?" + queryString;
            }

        }
        String wxOauth2Url = this.getWxOauth2UrlV2(redirectUrl, appId);
        resp.sendRedirect(wxOauth2Url);
    }


    private static final String WX_LOGIN_AUTH2_URL_V2 = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s" +
            "&response_type=code&scope=snsapi_base&state=login&connect_redirect=1#wechat_redirect";

    public String getWxOauth2UrlV2(String redirectUrl, String appId) {
        redirectUrl = URLEncoder.DEFAULT.encode(redirectUrl, StandardCharsets.UTF_8);
        return String.format(WX_LOGIN_AUTH2_URL_V2, appId, redirectUrl);
    }


}
