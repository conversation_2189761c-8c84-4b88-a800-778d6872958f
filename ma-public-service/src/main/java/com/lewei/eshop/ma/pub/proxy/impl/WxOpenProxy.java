package com.lewei.eshop.ma.pub.proxy.impl;

import com.lewei.eshop.ma.pub.MaPubErrorDef;
import com.lewei.eshop.ma.pub.proxy.AbstractRestProxy;
import com.lewei.eshop.ma.pub.proxy.IWxOpenProxy;
import com.lewei.wx.ma.response.Code2SessionResponse;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.http.ExecutionContext;
import com.xcrm.common.http.HttpMethod;
import com.xcrm.common.http.RequestMessage;
import com.xcrm.common.http.ResponseMessage;
import com.xcrm.common.http.utils.SafeUtils;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Service
public class WxOpenProxy extends AbstractRestProxy implements IWxOpenProxy {


    @PostConstruct
    private void init() {

        String endpoint = sysConfig.getEshopService();
        log.debug("eshopService host = {}", endpoint);
        super.init(endpoint, 3);
    }

    @Override
    public Code2SessionResponse getSession(String appId, String jsCode) {

        ResponseMessage response = null;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(this.endpoint);
        request.setMethod(HttpMethod.GET);
        request.setResourcePath("/api/public/v1/wx/mina/session/openId");
        //request.setResourcePath(":8002/public/v1/wx/mina/session/openId");
        Map<String, String> parameters = new HashMap<>();
        parameters.put("appId", appId) ;
        parameters.put("jsCode", jsCode);
        request.setParameters(parameters);

        try {
            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
            Code2SessionResponse sessionResponse =  toGeneralResponse(request, response, Code2SessionResponse.class);
            return sessionResponse;
        } catch (BizCoreRuntimeException e) {
            throw e;
        }  catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            throw new BizCoreRuntimeException(MaPubErrorDef.BACK_END_ERROR, BizMessageSource.getInstance().getMessage("cem30002"));
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
    }
}
