<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lewei.eshop.ma.pub.hot.word">

    <select id="queryHotWordList" resultType="com.lewei.eshop.common.vo.word.HotWordVo">
        SELECT id,content,priority
        FROM t_t_hot_word
        WHERE chainId = #{chainId}
        AND dataStatus = 1
        ORDER BY priority DESC
    </select>

</mapper>