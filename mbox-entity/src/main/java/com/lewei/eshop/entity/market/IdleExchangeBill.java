package com.lewei.eshop.entity.market;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 闲置账单表(TTIdleExchangeBill)实体类
 *
 * <AUTHOR>
 * @since 2022-06-14 09:28:18
 */
@Data
@Table(tableName = "t_t_idle_exchange_bill")
public class IdleExchangeBill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 总店ID
     */
    private Long chainId;
    /**
     * 店铺ID
     */
    private Long tenantId;
    /**
     * 流水编号
     */
    private String flowSn;
    /**
     * 会员id
     */
    private Long memberId;
    /**
     * 标题
     */
    private String title;
    /**
     * 金额
     */
    private BigDecimal money;
    /**
     * 币(金额换算成币)
     */
    private BigDecimal coin;
    /**
     * 类型 收入：income 支出：expenses 退款：refund
     */
    private String type;
    /**
     * 支付方式
     */
    private String paymentMethod;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 删除标志
     */
    private Boolean dataStatus;

}
