package com.lewei.eshop.entity.app;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员锁箱记录
 *
 * <AUTHOR>
 * @since 2025/1/7
 */
@Data
@Table(tableName = "t_pl_lock_box_record")
public class LockBoxRecord  implements Serializable {
    private static final long serialVersionUID = 1L;
    @PrimaryKeyField
    private Long id;
    private Long chainId;
    private Long tenantId;

    /**
     * memberId：会员id，代表进行锁箱操作的会员
     */
    private Long memberId;

    /**
     * boxItemId：箱子id，对应所操作的箱子
     */
    private Long boxItemId;

    /**
     * spuId：spuId，相关商品的SPU标识
     */
    private Long spuId;

    /**
     * startTime：开始时间，记录锁箱操作开始的时间节点
     */
    private Date startTime;

    /**
     * endTime：结束时间，记录锁箱操作结束的时间节点
     */
    private Date endTime;

    /**
     * created：创建时间，记录该会员锁箱记录的创建时间
     */
    private Date created;

    /**
     * updated：更新时间，记录该会员锁箱记录被更新的时间情况
     */
    private Date updated;
}
