package com.lewei.eshop.entity.member;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 淘宝转卖回收记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Data
@Table(tableName = "t_t_member_reward_recycle")
public class MemberRewardRecycle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签Id
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 总店id
     */
    private Long chainId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 回收编号
     */
    private String recycleSn;
    /**
     * 回收金额
     */
    private BigDecimal totalRecycleFee;
    /**
     * 回收币
     */
    private BigDecimal totalRecycleBalance;
    /**
     * 会员id
     */
    private Long memberId;

    private Boolean isCard;
    /**
     * 扩展属性
     */
    private String extJson;
    /**
     * 奖品数量
     */
    private Integer num;
    /**
     * 奖品总成本价
     */
    private BigDecimal totalPrimeCostFee;
    /**
     * 寄卖来源
     */
    private String recycleSource;
    /**
     * 更新时间
     */
    private Date created;
    /**
     * 1:正常 0:删除
     */
    private Boolean dataStatus;
}
