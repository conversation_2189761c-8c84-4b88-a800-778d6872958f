package com.lewei.eshop.entity.space;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 异空间会员奖池表
 * </p>
 *
 * <AUTHOR>
 * @since 2025年5月14日11:27:42
 */
@Data
@Table(tableName = "t_t_different_space_member")
public class DifferentSpaceMember implements Serializable {

   private static final long serialVersionUID = 1L;

   /**
    * 主键ID
    */
   @PrimaryKeyField
   private Long id;

   /**
    * 连锁店Id
    */
   private Long chainId;

   /**
    * 租户ID
    */
   private Long tenantId;

   /**
    * 异空间Id
    */
   private Long differentSpaceId;

   /**
    * 会员Id
    */
   private Long memberId;

   /**
    * 剩余数量
    */
   private Integer quantity;

   /**
    * 获奖数量
    */
   private Integer giftNum;

   /**
    * 创建时间
    */
   private Date created;

   /**
    * 更新时间
    */
   private Date updated;

   /**
    * 删除标识
    */
   private Boolean dataStatus;
}
