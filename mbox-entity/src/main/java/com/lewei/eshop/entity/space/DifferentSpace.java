package com.lewei.eshop.entity.space;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 异空间
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Table(tableName = "t_t_different_space")
public class DifferentSpace implements Serializable {

    private static final long serialVersionUID = -8621782298868775772L;

    @PrimaryKeyField
    private Long id;
    private Long chainId;
    private Long tenantId;
    /**
     * 标题
     */
    private String title;
    /**
     * 盲盒的spuId
     */
    private Long boxSpuId;
    /**
     * 进度数量
     */
    private Integer progressNum;
    /**
     * 清空类型id
     */
    private Long clearCategoryId;
    /**
     * 状态
     */
    private String status;

    private Date created;
    private Long createBy;
    private Date updated;
    private Long updateBy;
    private Boolean dataStatus;
}
