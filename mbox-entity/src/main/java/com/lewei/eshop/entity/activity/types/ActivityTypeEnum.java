/**
 *
 */
package com.lewei.eshop.entity.activity.types;

/**
 * 活动类型
 *
 * <AUTHOR>
 * @date 2016年2月25日 上午10:57:07
 */
public enum ActivityTypeEnum {

    /**
     * 转发有礼
     */
    S_AT_GIFT("S_AT_GIFT"),
    /**
     * 抽奖赠送EE
     */
    S_AT_REWARD_GIFT("S_AT_REWARD_GIFT"),
    /**
     * EE
     */
    S_AT_ISSUE("S_AT_ISSUE"),

    /**
     * 会员权益
     */
    S_AT_MEMBER_LEVEL("S_AT_MEMBER_LEVEL"),
    /**
     * 福利码
     */
    S_AT_WELFARE_CODE("S_AT_WELFARE_CODE"),

    /**
     * 新人赠送
     */
    S_AT_NEWCOMER_GIFT_BAG("S_AT_NEWCOMER_GIFT_BAG"),

    /**
     * 新人专区
     */
    S_AT_NEWCOMER_ACTIVITY("S_AT_NEWCOMER_ACTIVITY"),
    /**
     * 矿难
     */
    S_AT_REWARD_MINE("S_AT_REWARD_MINE"),

    /**
     * 签到
     */
    S_AT_SIGN("S_AT_SIGN"),
    /**
     * 成长任务
     */
    S_AT_POINT_TASK("S_AT_POINT_TASK"),
    /**
     * 膜拜赠送
     */
    S_AT_WORSHIP("S_AT_WORSHIP"),

    /**
     * 对战
     */
    S_AT_FIGHT("S_AT_FIGHT"),

    /**
     * 淘宝关注
     */
    S_AT_CARE_GIFTS("S_AT_CARE_GIFTS"),
    /**
     * 聚宝盆
     */
    S_AT_CORNUCOPIA("S_AT_CORNUCOPIA"),
    /**
     * 流水返现
     */
    S_AT_CASHBACK("S_AT_CASHBACK"),
    /**
     * 宝箱活动
     */
    S_AT_TREASURE_ACTIVITY("S_AT_TREASURE_ACTIVITY"),
    /**
     * 亏损补偿
     */
    S_AT_LOSS_COMPENSATE("S_AT_LOSS_COMPENSATE"),

    /**
     * 明星片
     */
    S_AT_POSTCARD("S_AT_POSTCARD"),

    /**
     * 合成赠送
     */
    S_AT_SYNTHESIS("S_AT_SYNTHESIS"),
    /**
     * 点赞排行榜
     */
    S_AT_MEDIA_RANK("S_AT_MEDIA_RANK"),

    /**
     * roll房
     */
    S_AT_ROLL("S_AT_ROLL"),

    /**
     * 图鉴兑换
     */
    S_AT_BOX_BOOK("S_AT_BOX_BOOK"),

    /**
     * 流水转盘
     */
    S_AT_TURNTABLE("S_AT_TURNTABLE"),

    /**
     * 双倍狂欢
     */
    S_AT_DOUBLE("S_AT_DOUBLE"),

    /**
     * 对对碰游戏
     */
    S_AT_COLLISION("S_AT_COLLISION"),

    /**
     * 投票活动
     */
    S_AT_VOTE("S_AT_VOTE"),

    /**
     * 娃主等级
     */
    S_AT_OQ_LEVEL("S_AT_OQ_LEVEL"),

    /**
     * 乐享赚
     */
    S_AT_MEMBER_INVITE("S_AT_MEMBER_INVITE"),

    /**
     * UP赏盲盒
     */
    S_AT_UP_BOX("S_AT_UP_BOX"),

    /**
     * 异空间
     */
    S_AT_DIFFERENT_SPACE("S_AT_DIFFERENT_SPACE"),

    ;


    private final String value;

    private ActivityTypeEnum(final String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

    /**
     * Convert a numerical status code into the corresponding Status.
     *
     * @param code the numerical status code.
     * @return the matching Status or null is no matching Status is defined.
     */
    public static ActivityTypeEnum fromCode(final String code) {
        for (ActivityTypeEnum s : ActivityTypeEnum.values()) {
            if (s.value().equals(code)) {
                return s;
            }
        }
        return null;
    }

}
