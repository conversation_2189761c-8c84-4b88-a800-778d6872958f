package com.lewei.eshop.entity.message;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 小程序消息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Data
@Table(tableName = "t_t_app_message_record")
public class AppMessageRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 连锁店id
     */
    private Long chainId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 标题
     */
    private String title;
    /**
     * 消息类型
     */
    private String type;
    /**
     * 子消息类型
     */
    private String subType;

    private Long plId;
    /**
     * 消息内容
     */
    private String messageData;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 删除标识
     */
    private Boolean dataStatus;



}
