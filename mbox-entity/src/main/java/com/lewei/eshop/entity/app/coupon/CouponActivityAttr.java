package com.lewei.eshop.entity.app.coupon;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 活动优惠券关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Data
@Table(tableName = "t_pl_coupon_activity_attr")
public class CouponActivityAttr implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 品牌id
     */
    private Long chainId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 优惠券ID
     */
    private Long couponId;

    private Integer num;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 创建时间
     */
    private Date created;



}
