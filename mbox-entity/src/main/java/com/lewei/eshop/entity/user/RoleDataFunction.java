package com.lewei.eshop.entity.user;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 角色数据权限关系表
 * @date 2022/6/5
 */
@Table(tableName = "t_b_role_data_function",isSaas=false)
@Data
public class RoleDataFunction {

    /**
     * id
     */
    @PrimaryKeyField
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 权限ID
     */
    private Long dataFunctionId;
}
