package com.lewei.eshop.entity.product;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 库位管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Data
@Table(tableName = "t_t_stock_location")
public class StockLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 连锁店id
     */
    private Long chainId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 库位名称
     */
    private String stockLocation;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新时间
     */
    private Date updated;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 删除标示
     */
    private Boolean dataStatus;
}
