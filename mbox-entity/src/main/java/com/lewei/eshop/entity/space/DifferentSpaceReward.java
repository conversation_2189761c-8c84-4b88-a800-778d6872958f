package com.lewei.eshop.entity.space;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 异空间奖品表
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Table(tableName = "t_t_different_space_reward")
public class DifferentSpaceReward implements Serializable {

    private static final long serialVersionUID = -8621782298868775772L;

    @PrimaryKeyField
    private Long id;
    private Long chainId;
    private Long tenantId;
    /**
     * 异空间id
     */
    private Long differentSpaceId;
    /**
     * spuId
     */
    private Long spuId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 分类id
     */
    private Long categoryId;
    /**
     * 中奖概率
     */
    private Double odds;
    /**
     * 排序
     */
    private Integer priority;

    private Date created;
    private Long createBy;
    private Date updated;
    private Long updateBy;
    private Boolean dataStatus;
}
