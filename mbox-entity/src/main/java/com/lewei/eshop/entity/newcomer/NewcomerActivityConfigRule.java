package com.lewei.eshop.entity.newcomer;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 新人专区配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-01
 */
@Data
@Table(tableName = "t_pl_newcomer_activity_config_rule")
public class NewcomerActivityConfigRule implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 连锁店id
     */
    private Long chainId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 配置Id
     */
    private Long configId;
    /**
     * 盲盒id
     */
    private Long spuId;
    /**
     * 活动价格
     */
    private BigDecimal price;
    /**
     * 图片
     */
    private String image;
    /**
     * 排序字段
     */
    private Integer priority;
    /**
     * 赠品json
     */
    private String giftJson;
    /**
     * 是否赠送
     */
    private Boolean isGift;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 创建人
     */
    private Long createBy;
}
