
package com.lewei.eshop.entity.member.types;

/**
 * 商品获得途径
 *
 * <AUTHOR>
 * @date 2022-03-22
 */
public enum RewardReceiveWayEnum {
    /**
     * 抽奖所得
     */
    S_MRRW_BUY("S_MRRW_BUY"),
    /**
     * 购买所得
     */
    S_MRRW_BUY_PRODUCT("S_MRRW_BUY_PRODUCT"),
    /**
     * 商家赠送
     */
    S_MRRW_BUSINESS("S_MRRW_BUSINESS"),
    /**
     * 好友赠送
     */
    S_MRRW_FRIEND("S_MRRW_FRIEND"),
    /**
     * 积分抽奖
     */
    S_MRRW_FRACTION("S_MRRW_FRACTION"),
    /**
     * 怒气抽奖
     */
    S_MRRW_SCORE("S_MRRW_SCORE"),
    /**
     * 抽赏赠送
     */
    S_MRRW_REWARD("S_MRRW_REWARD"),
    /**
     * 藏宝图兑换
     */
    S_MRRW_TREASURE("S_MRRW_TREASURE"),
    /**
     * 换换宇宙所得
     */
    S_MRRW_UNIVERSE("S_MRRW_UNIVERSE"),
    /**
     * 会员权益
     */
    S_MRRW_MEMBER_LEVEL("S_MRRW_MEMBER_LEVEL"),
    /**
     * 新人赠送
     */
    S_MRRW_NEWCOMER_GIFT_BAG("S_MRRW_NEWCOMER_GIFT_BAG"),
    /**
     * 新人赠送
     */
    S_MRRW_NEWCOMER_ACTIVITY("S_MRRW_NEWCOMER_ACTIVITY"),
    /**
     * 福利码兑换
     */
    S_MRRW_WELFARE_CODE("S_MRRW_WELFARE_CODE"),
    /**
     * 矿难赠送
     */
    S_MRRW_REWARD_MINE("S_MRRW_REWARD_MINE"),
    /**
     * 签到
     */
    S_MRRW_SIGN("S_MRRW_SIGN"),
    /**
     * 成长任务
     */
    S_MRRW_POINT_TASK("S_MRRW_POINT_TASK"),
    /**
     * 自建盲盒
     */
    S_MRRW_SELF_BOX("S_MRRW_SELF_BOX"),

    /**
     * 对战
     */
    S_MRRW_FIGHT("S_MRRW_FIGHT"),

    /**
     * roll房
     */
    S_MRRW_ROLL("S_MRRW_ROLL"),

    /**
     * 赏品合成
     */
    S_MRRW_PRODUCT_COMPOSE("S_MRRW_PRODUCT_COMPOSE"),


    /**
     * 宝箱
     */
    S_MRRW_PRODUCT_TREASURE("S_MRRW_PRODUCT_TREASURE"),

    /**
     * 宝箱活动
     */
    S_MRRW_TREASURE_ACTIVITY("S_MRRW_TREASURE_ACTIVITY"),

    /**
     * DIY盲盒
     */
    S_MRRW_DIY_BOX("S_MRRW_DIY_BOX"),
    /**
     * 抓取游戏
     */
    S_MRRW_DOLL_BOX("S_MRRW_DOLL_BOX"),
    /**
     * 明信片
     */
    S_MRRW_POSTCARD("S_MRRW_POSTCARD"),
    /**
     * 关怀补贴
     */
    S_MRRW_LOSS_COMPENSATE("S_MRRW_LOSS_COMPENSATE"),

    /**
     * 合成赠送
     */
    S_MRRW_SYNTHESIS("S_MRRW_SYNTHESIS"),

    /**
     * 怒气商城
     */
    S_MRRW_SCORE_SHOP("S_MRRW_SCORE_SHOP"),
    /**
     * 卡册兑换
     */
    S_MRRW_CARD_BOOK("S_MRRW_CARD_BOOK"),

    /**
     * 盲盒图鉴兑换
     */
    S_MRRW_BOX_BOOK("S_MRRW_BOX_BOOK"),

    /**
     * 流水转盘
     */
    S_MRRW_TURNTABLE("S_MRRW_TURNTABLE"),
    /**
     * 娃主商城
     */
    S_MRRW_OQ_PRODUCT("S_MRRW_OQ_PRODUCT"),

    /**
     * 双倍狂欢
     */
    S_MRRW_DOUBLE("S_MRRW_DOUBLE"),

    /**
     * 对对碰游戏
     */
    S_MRRW_COLLISION("S_MRRW_COLLISION"),

    /**
     * 娃主等级
     */
    S_MRRW_OQLEVEL("S_MRRW_OQLEVEL"),

    /**
     * 娃主等级
     */
    S_MRRW_DIFFERENT_SPACE("S_MRRW_DIFFERENT_SPACE"),
    ;


    private final String value;

    RewardReceiveWayEnum(final String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }
}
