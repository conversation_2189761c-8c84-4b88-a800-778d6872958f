package com.lewei.eshop.entity.space;


import com.xcrm.core.db.annotation.Table;
import com.xcrm.core.db.annotation.PrimaryKeyField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 异空间奖品记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025年5月14日15:45:52
 */
@Data
@Table(tableName = "t_t_different_space_reward_record")
public class DifferentSpaceRewardRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @PrimaryKeyField
    private Long id;

    /**
     * 连锁店Id
     */
    private Long chainId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 异空间Id
     */
    private Long differentSpaceId;

    /**
     * 会员Id
     */
    private Long memberId;
    /**
     * spuId
     */
    private Long spuId;

    /**
     * 奖品图片
     */
    private String mainImage;

    /**
     * 奖品名称
     */
    private String rewardName;
    /**
     * 分类Id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 分类图片
     */
    private String categoryPic;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 删除标识
     */
    private Boolean dataStatus;
}
