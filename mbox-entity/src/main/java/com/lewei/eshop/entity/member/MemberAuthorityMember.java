package com.lewei.eshop.entity.member;

import com.xcrm.core.db.annotation.Table;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <p> 
 * 会员封禁关系表
 * <p> 
 *
 * <AUTHOR>
 * @date 2022/10/14
 */

@Data
@Table(tableName = "t_t_member_authority_member")
public class MemberAuthorityMember implements Serializable{
    /**
     * 会员ID
     */
    private Long memberId;
    /**
     * 封禁id
     */
    private Long authId;
	/**
	 * 总店ID
	 */
	private Long chainId;
	/**
	 * 店铺ID
	 */
	private Long tenantId;
    /**
     * 是否封禁
     */
    private Boolean isBan;
    /**
     * 是否封禁全部账号
     */
    private Boolean isAllAccount;
    /**
     * 封禁状态
     */
    private Boolean bannedState;
    /**
     * 封禁时长
     */
    private Integer bannedTime;
    /**
     * 封禁当前时间
     */
    private Date bannedNowTime;
    /**
     * 备注
     */
    private String remark;
}
