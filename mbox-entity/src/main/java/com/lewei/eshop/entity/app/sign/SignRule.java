package com.lewei.eshop.entity.app.sign;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 分享有礼获取规则
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Data
@Table(tableName = "t_pl_sign_rule")
public class SignRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @PrimaryKeyField
    private Long id;
    /**
     * 总店ID
     */
    private Long chainId;
    /**
     * 店铺ID
     */
    private Long tenantId;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 签到规则 base/continuous
     */
    private String ruleType;
    /**
     * 连续签到天数
     */
    private Integer signDays;
    /**
     * 礼品类型 coupon/card/point/score/customize
     */
    private String giftType;

    /**
     * 礼品内容json，客户端自定义
     */
    private String giftExtJson;

    /**
     * 创建时间
     */
    private Date created;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新时间
     */
    private Date updated;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 删除标志
     */
    private Boolean dataStatus;
}
