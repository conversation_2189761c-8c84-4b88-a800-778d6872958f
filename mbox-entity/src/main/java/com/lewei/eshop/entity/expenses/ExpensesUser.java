package com.lewei.eshop.entity.expenses;

import com.xcrm.core.db.annotation.PrimaryKeyField;
import com.xcrm.core.db.annotation.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 支出表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-06
 */
@Data
@Table(tableName = "t_t_expenses_user")
public class ExpensesUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @PrimaryKeyField
    private Long id;
    private Long chainId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 员工id
     */
    private Long userId;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 跟新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private Date updated;


}
