package com.lewei.eshop.member;

import com.lewei.eshop.common.request.member.HandleMemberLevelPointTaskReq;
import com.lewei.eshop.common.request.member.HandleMemberLevelTaskReq;
import com.lewei.eshop.common.request.member.ReceiveBenefitsReq;
import com.lewei.eshop.entity.member.types.MemberLevelTaskTypeEnum;
import com.lewei.eshop.member.biz.IMemberLevelBenefitService;
import com.lewei.eshop.member.biz.IMemberLevelPointTaskService;
import com.lewei.eshop.member.biz.IMemberLevelTaskLogService;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MemberServiceApplication.class})
public class BenefitTest {

    @Autowired
    private IMemberLevelBenefitService benefitService;
    @Autowired
    private IMemberLevelTaskLogService levelTaskLogService;
    @Autowired
    private IMemberLevelPointTaskService memberLevelPointTaskService;



    @Before
    public void init(){
        XcrmThreadContext.setChainId(8652259593785344L);
        XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
    }

    @After
    public void after() {
        XcrmThreadContext.removeAccessType();
        XcrmThreadContext.removeChainId();
    }


    @Test
    public void receiveBenefits() {
        ReceiveBenefitsReq req = new ReceiveBenefitsReq();
        List<Integer> list = new ArrayList<>();
        list.add(30);
        list.add(60);
        req.setMemberId(1536966261668626465L);
        req.setLevelId(1537012206252384346L);
        req.setActiveValues(list);
        benefitService.receiveBenefits(req,8652259593785344L);

    }

    @Test
    public void handleMemberLevelTask() {
        HandleMemberLevelTaskReq taskReq = new HandleMemberLevelTaskReq();
        taskReq.setMemberId(1545225135894896725L);
        taskReq.setTaskType(MemberLevelTaskTypeEnum.S_MLTE_TRANSACTION.value());
        taskReq.setTarget(BigDecimal.valueOf(10));
        taskReq.setOrderId(1552122385518575671L);
        levelTaskLogService.handleMemberLevelTask(taskReq,8652259593785344L);

    }

    @Test
    public void handleMemberLevelPointTask() {
        HandleMemberLevelPointTaskReq taskReq = new HandleMemberLevelPointTaskReq();
        taskReq.setMemberId(1545225135894896725L);
        taskReq.setTaskType(MemberLevelTaskTypeEnum.S_MLTE_SHARE.value());
        taskReq.setTarget(BigDecimal.valueOf(1));
       // taskReq.setOrderId(1552122385518575671L);
        memberLevelPointTaskService.handleMemberLevelTask(taskReq,8652259593785344L);

    }
}
