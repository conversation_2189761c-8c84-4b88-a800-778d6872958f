package com.lewei.eshop.member.pay.chain;

import com.alibaba.fastjson.JSON;
import com.lewei.eshop.auth.ma.SSOMaAppService;
import com.lewei.eshop.common.request.pay.PayWxRequest;
import com.lewei.eshop.common.vo.pay.PayDeductConfigVo;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberMaRef;
import com.lewei.eshop.entity.sso.SSOMaApp;
import com.lewei.eshop.entity.sso.types.MaPlatformEnum;
import com.lewei.eshop.entity.sso.types.MaTypeEnum;
import com.lewei.eshop.member.MemberErrorConstants;
import com.lewei.eshop.member.SysConfig;
import com.lewei.eshop.member.biz.IMemberService;
import com.lewei.eshop.member.client.AppFeignClient;
import com.lewei.eshop.member.client.PayFeignClient;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 2020/10/30
 */
public abstract class AbstractPayHandlerChain {


    @Autowired
    protected BaseDaoSupport dao;
    @Autowired
    protected PayFeignClient payFeignClient;
    @Autowired
    protected SysConfig sysConfig;
    @Autowired
    protected IMemberService memberService;
    @Autowired
    protected SSOMaAppService ssoMaAppService;
    @Autowired
    private AppFeignClient appFeignClient;

    /**
     * 微信支付 扫码方式/js（ 获取对应c端appId 未授权绑定小程序 不能支付）
     * @param orderTitle 订单标题
     * @param paymentMoney 支付金额
     * @param orderCodes 订单编号
     * @param chainId 品牌id
     * @return  Map
     */
    protected Map callWxPayApi(Long memberId,String payMethod,String orderTitle, BigDecimal paymentMoney, String orderCodes, Long chainId,String appId,String returnUrl, String openId) {

        PayWxRequest payWxRequest = new PayWxRequest();
        payWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/member/pay/wx/callback");
        payWxRequest.setOrderCodes(orderCodes);
        payWxRequest.setOrderTitle(orderTitle);
        payWxRequest.setPaymentMoney(paymentMoney);
        payWxRequest.setOpenId(Optional.ofNullable(openId).orElse(this.queryMemberOpenId(memberId,appId)));
        payWxRequest.setPayMethod(payMethod);
        payWxRequest.setBizType(queryMemberBalancePayBizType());
        payWxRequest.setReturnUrl(returnUrl);
        if (StringUtils.isBlank(appId)){
            appId = this.querySSoMaApp(chainId).getAppId();
        }
        payWxRequest.setWxAppId(appId);
        Map<String,Object> extra = new HashMap<>();
        Member member = this.getMember(memberId);
        extra.put("mobile",member.getMobile());
        payWxRequest.setExtra(JSON.toJSONString(extra));
        return payFeignClient.callPayApi(payWxRequest);
    }

    /**
     * 获取c端小程序 appId
     * @param chainId 品牌id
     * @return SSOMaApp
     */
    private SSOMaApp querySSoMaApp(Long chainId){

        SSOMaApp ssoMaApp = null;
        try {
            ssoMaApp = ssoMaAppService.querySSOMaApp(MaTypeEnum.MALL.value(), MaPlatformEnum.WEIXIN.value(), chainId);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizCoreRuntimeException(MemberErrorConstants.MA_AUTHORIZATION_ERROR);
        }
        if (ssoMaApp == null) {
            throw new BizCoreRuntimeException(MemberErrorConstants.MA_NOT_AUTHORIZATION);
        }
        return ssoMaApp;
    }

    /**
     * 获取会员信息
     * @param memberId 会员id
     * @return Member
     */
    protected Member getMember(Long memberId){
        Member member = memberService.queryMember(memberId);
        if (member == null) {
            throw new NotFoundException(BizMessageSource.getInstance().getMessage("cem50088"));
        }
        return member;
    }

    private String queryMemberBalancePayBizType(){
        SaasQueryBuilder query = SaasQueryBuilder.create();
        ChainConfig chainConfig = dao.query(query, ChainConfig.class);
        return chainConfig == null ? "default" : chainConfig.getMemberBalancePayBizType();
    }

    /**
     * 获取会员openId
     * @param memberId 会员id
     * @param appId appId
     * @return string
     */
    protected String queryMemberOpenId(Long memberId,String appId){
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("memberId",memberId))
                .and(Restrictions.eq("appId",appId));
        MemberMaRef memberMaRef  = dao.query(query,MemberMaRef.class);
        if (memberMaRef == null) {
            return "";
        }
        return memberMaRef.getOpenId();
    }

    protected BigDecimal handelPayDeduct(BigDecimal orderMoney) {
        PayDeductConfigVo config = appFeignClient.queryPayDeductConfig();
        if (config != null && BooleanUtils.isTrue(config.getIsEnable())) {
            int bg = config.getBgFee().multiply(BigDecimal.valueOf(100)).intValue();
            int eg = config.getEndFee().multiply(BigDecimal.valueOf(100)).intValue();
            Random random = new Random();
            int intMoney = random.nextInt(eg - bg + 1) + bg;
            BigDecimal deductMoney = BigDecimal.valueOf(intMoney).divide(BigDecimal.valueOf(100));
            orderMoney = orderMoney.subtract(deductMoney);
            if (orderMoney.compareTo(BigDecimal.ZERO) <= 0) {
                orderMoney = BigDecimal.valueOf(0.01);
            }
        }
        return orderMoney;
    }
}
