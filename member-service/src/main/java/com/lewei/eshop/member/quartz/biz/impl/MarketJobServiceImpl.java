package com.lewei.eshop.member.quartz.biz.impl;

import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.request.market.PayAuctionRequest;
import com.lewei.eshop.common.request.market.PayFixedRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.vo.market.BlackMarketJobListVo;
import com.lewei.eshop.common.vo.market.ChangeOrderJobListVo;
import com.lewei.eshop.common.vo.market.RobotMarketVo;
import com.lewei.eshop.entity.market.*;
import com.lewei.eshop.entity.market.types.BlackMarketStatusEnum;
import com.lewei.eshop.entity.market.types.ChangeOrderStatusEnum;
import com.lewei.eshop.entity.market.types.MarketAuctionStatusEnum;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.member.MemberBill;
import com.lewei.eshop.entity.member.types.MemberBillTypeEnum;
import com.lewei.eshop.entity.member.types.MemberRewardStatusEnum;
import com.lewei.eshop.entity.order.types.OrderPaymentTypeEnum;
import com.lewei.eshop.member.biz.IMemberBalanceService;
import com.lewei.eshop.member.client.MaFeignClient;
import com.lewei.eshop.member.quartz.biz.IMarketJobService;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/4/15
 */
@Transactional
@Service
@Slf4j
public class MarketJobServiceImpl implements IMarketJobService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private IMemberBalanceService balanceService;
    @Autowired
    private MaFeignClient maFeignClient;
    @Autowired
    private IMemberBalanceService memberBalanceService;

    @Override
    public void handleExpiredMarket() {


        QueryBuilder query = QueryBuilder.create("com.lewei.eshop.black.market.queryExpiredMarkets");

        List<BlackMarketJobListVo> markets = dao.findForList(query, BlackMarketJobListVo.class);

        if (ListUtil.isNotEmpty(markets)) {
            for (BlackMarketJobListVo market : markets) {
                try {
                    XcrmThreadContext.setChainId(market.getChainId());
                    XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                    //下架黑市
                    BlackMarket blackMarket = new BlackMarket();
                    blackMarket.setId(market.getId());
                    blackMarket.setStatus(BlackMarketStatusEnum.S_BMS_OFF_SHELF.value());
                    dao.update(blackMarket);
                    //赏品返回赏袋
                    if (ListUtil.isNotEmpty(market.getRewordVos())) {
                        List<Long> memberRewardIds = market.getRewordVos().stream().map(BlackMarketJobListVo.MarketRewordVo::getMemberRewardId).collect(Collectors.toList());

                        Ssqb updateMemberRewardStatus = Ssqb.create("com.lewei.eshop.black.market.updateMemberRewardStatus")
                                .setParam("status", MemberRewardStatusEnum.S_MRS_UNAPPLY.value())
                                .setParam("rewardIds", memberRewardIds);
                        dao.updateByMybatis(updateMemberRewardStatus);

                    }
                    //竞拍返回余额
                    if (ListUtil.isNotEmpty(market.getAuctionVos())) {

                        for (BlackMarketJobListVo.AuctionVo auctionVo : market.getAuctionVos()) {
                            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                            balanceRequest.setGift(BigDecimal.ZERO);
                            balanceRequest.setBalance(auctionVo.getAuctionPrice());
                            balanceRequest.setMemberId(auctionVo.getMemberId());
                            balanceRequest.setPlId(auctionVo.getAuctionId());
                            balanceRequest.setContent("黑市退款：" + auctionVo.getTitle());
                            balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                            balanceService.memberBalanceHandler(balanceRequest);
                            BlackMarketAuction auction = new BlackMarketAuction();
                            auction.setId(auctionVo.getAuctionId());
                            auction.setStatus(MarketAuctionStatusEnum.S_MAS_YTK.value());
                            auction.setUpdated(DateFormatUtils.getNow());
                            dao.update(auction);

                            SaasQueryBuilder queryRewards = SaasQueryBuilder.where(Restrictions.eq("auctionId",auction.getId()));

                            List<BlackMarketAuctionReward> rewards = dao.queryList(queryRewards,BlackMarketAuctionReward.class);

                            if (ListUtil.isNotEmpty(rewards)){
                                List<Long> memberRewardIds = rewards.stream().map(BlackMarketAuctionReward::getMemberRewardId).collect(Collectors.toList());
                                  Ssqb  update = Ssqb.create("com.lewei.eshop.black.market.updateMemberRewardStatus")
                                  .setParam("rewardIds",memberRewardIds)
                                  .setParam("status",MemberRewardStatusEnum.S_MRS_UNAPPLY.value());
                                  dao.updateByMybatis(update);
                            }

                        }

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    XcrmThreadContext.removeChainId();
                    XcrmThreadContext.removeAccessType();
                }
            }
        }
    }

    @Override
    public void triggerRobotJob() {

       QueryBuilder query = QueryBuilder.create("com.lewei.eshop.black.market.queryLowPriceMarket");
        List<RobotMarketVo>  marketVos = dao.findForList(query,RobotMarketVo.class);
        if (ListUtil.isNotEmpty(marketVos)){
            Map<Long,List<RobotMarketVo>> mapping = marketVos.stream().collect(Collectors.groupingBy(RobotMarketVo::getChainId));
            mapping.forEach((k,v)->{
                try {
                    XcrmThreadContext.setChainId(k);
                    XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                    Member member = null;
                    List<Member> members;

                        SaasQueryBuilder querySysMember = SaasQueryBuilder.where(Restrictions.eq("dataStatus",1))
                                .and(Restrictions.eq("isSystem",1));
                        members = dao.queryList(querySysMember, Member.class);
                        if(ListUtil.isNotEmpty(members)){
                            member = members.get(new Random().nextInt(members.size()));
                        }


                    if (member != null) {

                        for (RobotMarketVo marketVo : v) {
                            if (BooleanUtils.isTrue(marketVo.getIsFixedPrice()) &&
                                    marketVo.getFixedPrice().compareTo(marketVo.getRecoveryFee()) <= 0 ){
                                //一口价小于回收价格  直接一口价回收
                                PayFixedRequest request = new PayFixedRequest();
                                request.setFixedPrice(marketVo.getFixedPrice());
                                request.setMarketId(marketVo.getId());
                                request.setPaymentMethod(OrderPaymentTypeEnum.S_OPM_BALANCE.value());
                                try {
                                    maFeignClient.pay(request,member.getId());
                                } catch (Exception e) {
                                    log.error("triggerRobotJob.pay.error:{}",e.getMessage());
                                    e.printStackTrace();
                                    if (e instanceof BizCoreRuntimeException){
                                    }
                                }
                            }else if(marketVo.getAuctionPrice().compareTo(marketVo.getRecoveryFee()) <= 0 ){
                                //判断是否参与竞拍
                                boolean isAuction = false;
                                if (StringUtils.isNotEmpty(marketVo.getAuctionMemberIds())){

                                    List<Long> memberIds =Arrays.stream(marketVo.getAuctionMemberIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                                    for (Member member1 : members) {
                                        if (memberIds.contains(member1.getId())){
                                            isAuction = true;
                                            break;
                                        }
                                    }
                                }
                                if (!isAuction ){
                                    PayAuctionRequest request = new PayAuctionRequest();
                                    request.setAuctionPrice(marketVo.getRecoveryFee());
                                    request.setMarketId(marketVo.getId());
                                    request.setPaymentMethod(OrderPaymentTypeEnum.S_OPM_BALANCE.value());
                                    try {
                                        maFeignClient.payAuction(request,member.getId());
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        log.error("triggerRobotJob.pay.error:{}",e.getMessage());
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    XcrmThreadContext.removeAccessType();
                    XcrmThreadContext.removeChainId();
                }

            });
        }





    }

    @Override
    public void triggerChangeJob() {
        QueryBuilder query = QueryBuilder.create("com.lewei.eshop.change.order.queryChangeOrderPast");
        List<ChangeOrderJobListVo> changeOrderJobListVos = dao.findForList(query, ChangeOrderJobListVo.class);

        if (ListUtil.isNotEmpty(changeOrderJobListVos)) {
            for (ChangeOrderJobListVo changeOrderJobListVo : changeOrderJobListVos) {
                try {
                    XcrmThreadContext.setChainId(changeOrderJobListVo.getChainId());
                    XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                    if (ListUtil.isNotEmpty(changeOrderJobListVo.getAuctionVos())) {
                        for (ChangeOrderJobListVo.AuctionVo auctionVo : changeOrderJobListVo.getAuctionVos()) {
                            IdleExchangeAuction auction = new IdleExchangeAuction();

                            // 余额充值到发布者钱包
                            MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                            balanceRequest.setGift(BigDecimal.ZERO);
                            balanceRequest.setBalance(changeOrderJobListVo.getAuctionPrice().add(changeOrderJobListVo.getFreight()));
                            balanceRequest.setMemberId(changeOrderJobListVo.getSellMemberId());
                            balanceRequest.setPlId(changeOrderJobListVo.getId());
                            balanceRequest.setContent("黑市到账：" + changeOrderJobListVo.getTitle());
                            balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                            memberBalanceService.memberBalanceHandler(balanceRequest);

                            // 币记录
                            IdleExchangeBill idleExchangeBill = new IdleExchangeBill();
                            idleExchangeBill.setFlowSn(changeOrderJobListVo.getFlowSn());
                            idleExchangeBill.setMemberId(changeOrderJobListVo.getSellMemberId());
                            idleExchangeBill.setTitle(changeOrderJobListVo.getTitle());
                            idleExchangeBill.setMoney(changeOrderJobListVo.getAuctionPrice().add(changeOrderJobListVo.getFreight()));
                            idleExchangeBill.setCoin(changeOrderJobListVo.getCoin());
                            idleExchangeBill.setType(MemberBillTypeEnum.income.value());
                            idleExchangeBill.setPaymentMethod(auctionVo.getPaymentMethod());
                            idleExchangeBill.setCreated(DateFormatUtils.getNow());
                            dao.save(idleExchangeBill);

                            // 保存会员账单
                            MemberBill bill = new MemberBill();
                            bill.setMemberId(changeOrderJobListVo.getSellMemberId());
                            bill.setOrderSn(changeOrderJobListVo.getFlowSn());
                            bill.setMoney(changeOrderJobListVo.getAuctionPrice().add(changeOrderJobListVo.getFreight()));
                            bill.setPaymentMethod(auctionVo.getPaymentMethod());
                            bill.setType(MemberBillTypeEnum.income.value());
                            bill.setCreated(DateFormatUtils.getNow());
                            bill.setTradeSucTime(DateFormatUtils.getNow());
                            dao.save(bill);

                            auction.setId(auctionVo.getAuctionId());
                            auction.setOrderStatus(ChangeOrderStatusEnum.S_COS_JYCG.value());
                            auction.setStatus(MarketAuctionStatusEnum.S_MAS_JPCG.value());
                            auction.setUpdated(DateFormatUtils.getNow());
                            dao.update(auction);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    XcrmThreadContext.removeChainId();
                    XcrmThreadContext.removeAccessType();
                }
            }
        }
    }
}
