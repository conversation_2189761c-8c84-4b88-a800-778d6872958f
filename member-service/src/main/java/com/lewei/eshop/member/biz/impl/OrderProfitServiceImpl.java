package com.lewei.eshop.member.biz.impl;

import com.lewei.common.utils.WebHookUtil;
import com.lewei.eshop.common.vo.order.OrderProfitCountVo;
import com.lewei.eshop.entity.chain.Chain;
import com.lewei.eshop.entity.order.OrderDeliveredJob;
import com.lewei.eshop.entity.order.OrderProfitLog;
import com.lewei.eshop.entity.order.OrderProfitRetryLog;
import com.lewei.eshop.member.Constants;
import com.lewei.eshop.member.SysConfig;
import com.lewei.eshop.member.biz.IOrderProfitService;
import com.lewei.eshop.member.proxy.PaasHttpProxy;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2020/4/27
**/
@Service
@Transactional
@Slf4j
public class OrderProfitServiceImpl implements IOrderProfitService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private PaasHttpProxy paasHttpProxy;
    @Autowired
    private SysConfig sysConfig;

//    @Override
//    public OrderProfitLog saveOrderProfitLog(String payCode, String orderSn, Long chainId) {
//        OrderProfitLog profitLog = new OrderProfitLog();
//        profitLog.setChainId(chainId);
//        profitLog.setOrderSn(orderSn);
//        profitLog.setPayCode(payCode);
//        profitLog.setCreated(new Timestamp(System.currentTimeMillis()));
//        profitLog.setStatus("pending");
//
//        dao.save(profitLog);
//
//        return profitLog;
//    }

    @Override
    public void saveOrderProfitLog(List<OrderDeliveredJob> orderDeliveredJobs) {
        List<OrderProfitLog> logs = new ArrayList<>();
        for(OrderDeliveredJob orderDeliveredJob :  orderDeliveredJobs) {
            OrderProfitLog profitLog = new OrderProfitLog();
            profitLog.setChainId(orderDeliveredJob.getChainId());
            profitLog.setOrderSn(orderDeliveredJob.getOrderSn());
            profitLog.setPayCode(orderDeliveredJob.getPaySn());
            profitLog.setCreated(new Timestamp(System.currentTimeMillis()));
            profitLog.setStatus("pending");

            logs.add(profitLog);
        }
        dao.batchSave(logs, OrderProfitLog.class);
    }

    @Override
    public List<OrderProfitCountVo> queryOrderProfitCount(String paymentMethod) {
        Ssqb query = Ssqb.create("com.lewei.eshop.order.profit.queryOrderProfitCount").setParam("paymentMethod", paymentMethod);
        return dao.findForList(query, OrderProfitCountVo.class);
    }

    @Override
    public void submitOrderProfitByManual(Long chainId, String opsCode, String paymentMethod) {

        QueryBuilder queryBuilder = QueryBuilder.where(Restrictions.eq("status", "pending"))
                .and(Restrictions.eq("chainId", chainId));
        if(StringUtils.isNotBlank(paymentMethod)) {
            queryBuilder.and(Restrictions.eq("paymentMethod", paymentMethod));
        }
        List<OrderProfitLog> logs = dao.queryList(queryBuilder, OrderProfitLog.class);
        callProfitApi(logs, true, opsCode);
    }

    @Override
    public void triggerOrderProfitJob() {

        //查询24小时前的记录，T+1 结算分账
        DateTime dt = DateTime.now().plusDays(-1);
        QueryBuilder queryBuilder = QueryBuilder.where(Restrictions.eq("status", "pending"))
                .and(Restrictions.le("created", dt.toDate()));
        List<OrderProfitLog> logs = dao.queryList(queryBuilder, OrderProfitLog.class);
        callProfitApi(logs, false, null);
    }

    @Override
    public void triggerOrderProfitRetryJob() {

        //查询24小时前的记录
        DateTime dt = DateTime.now().plusHours(-24);
        DateTime st = DateTime.now().plusDays(-7);
        QueryBuilder queryBuilder = QueryBuilder.where(Restrictions.eq("status", "pending"))
                .and(Restrictions.le("updated", dt.toDate())).and(Restrictions.ge("updated", st.toDate()));
        List<OrderProfitRetryLog> logs = dao.queryList(queryBuilder, OrderProfitRetryLog.class);
        Date created = new Timestamp(System.currentTimeMillis());
        //重试成功记录
        List<OrderProfitRetryLog> retrySuccLogs = new ArrayList<>();
        //需要人工处理的记录
        List<OrderProfitRetryLog> byManualLogs = new ArrayList<>();

        for(OrderProfitRetryLog orderProfitLog : logs) {
            //分账订单发起分账处理
            Integer retryCount = orderProfitLog.getRetryCount();
            try {
                Map map = paasHttpProxy.callProfitApi(orderProfitLog.getPayCode(), orderProfitLog.getOrderSn(), orderProfitLog.getChainId());
                if(map != null) {
                    Long profitId = MapUtils.getLong(map, "profitId");
                    orderProfitLog.setStatus("suc");
                    orderProfitLog.setProfitId(profitId);
                }
            } catch (BizCoreRuntimeException e) {
                orderProfitLog.setStatus("failed");
                if(e.getErrorContents() != null) {
                    String failReason = Arrays.stream(e.getErrorContents()).map(error->error.toString()).collect(Collectors.joining(","));
                    orderProfitLog.setFailReason(failReason);
                } else {
                    orderProfitLog.setFailReason(e.getMessage());
                }

            } catch (Exception e) {
                orderProfitLog.setStatus("failed");
                orderProfitLog.setFailReason(e.getMessage());
                log.error("triggerOrderProfitRetryJob error", e);
            }
            retryCount++;
            if("failed".equals(orderProfitLog.getStatus())) {
                //重试失败
                if(retryCount > 2) {
                    orderProfitLog.setStatus("ByManual");
                    byManualLogs.add(orderProfitLog);
                } else {
                    //重置为待处理状态
                    orderProfitLog.setStatus("pending");
                }

            } else if("suc".equals(orderProfitLog.getStatus())){
                //重试成功
                retrySuccLogs.add(orderProfitLog);
            }
            orderProfitLog.setRetryCount(retryCount);
            orderProfitLog.setUpdated(created);
        }

        dao.batchUpdate(logs, OrderProfitRetryLog.class);

        //存在重试成功记录， 更新原分账记录
        if(CollectionUtils.isNotEmpty(retrySuccLogs)) {
            for(OrderProfitRetryLog item : retrySuccLogs) {
                QueryBuilder sql = QueryBuilder.create("com.lewei.eshop.order.profit.updateOrderProfitLogForRetry")
                        .setParam("orderProfitLogId", item.getOrderProfitLogId())
                        .setParam("profitId", item.getProfitId());
                dao.updateByMybatis(sql);
            }
        }

        if(CollectionUtils.isNotEmpty(byManualLogs)) {
            //发消息
            Map<Long, List<OrderProfitRetryLog>>  byManualLogMap = byManualLogs.stream().collect(Collectors.groupingBy(OrderProfitRetryLog::getChainId));

            for(Map.Entry<Long, List<OrderProfitRetryLog>> entry : byManualLogMap.entrySet()) {
                StringBuilder stringBuilder = new StringBuilder(BizMessageSource.getInstance().getMessage("cem50054")+
                        "\r\n"+ BizMessageSource.getInstance().getMessage("cem50055") +"\r\n");
                Long chainId = entry.getKey();
                QueryBuilder queryBuilder1 = QueryBuilder.where(Restrictions.eq("chainId", chainId));
                Chain chain = dao.query(queryBuilder1, Chain.class);
                String shopName = null;
                if(chain != null) {
                    shopName = chain.getShopName();
                }
                //原因 个数
                Map<String, Integer> resultMap = new HashMap<>();
                for(OrderProfitRetryLog item : entry.getValue()) {
                    updateResultMap(resultMap, item.getFailReason());
                }
                stringBuilder.append(BizMessageSource.getInstance().getMessage("cem50056")+"id：")
                        .append(chainId).append("\r\n")
                        .append(BizMessageSource.getInstance().getMessage("cem50057")).append(shopName).append("\r\n");
                for(Map.Entry<String, Integer> entryResult : resultMap.entrySet()) {
                    stringBuilder.append(entryResult.getKey()).append("：").append(entryResult.getValue()).append("。\r\n");
                }

                if(sysConfig.getProjectProfile().equals(Constants.SYSTEM_MODEL_PRODUCT)) {
                    WebHookUtil.sendOpsMessage(stringBuilder.toString());
                }
            }
        }
    }


    private void callProfitApi(List<OrderProfitLog> logs, Boolean isManual, String opsCode) {
        if(ListUtil.isNotEmpty(logs)) {
            //原因 个数
            Map<String, Integer> resultMap = new HashMap<>();
            List<OrderProfitRetryLog> orderProfitRetryLogs = new ArrayList<>();
            Date updated = new Timestamp(System.currentTimeMillis());
            for(OrderProfitLog orderProfitLog : logs) {
                //分账订单发起分账处理
                try {
                    Map map = paasHttpProxy.callProfitApi(orderProfitLog.getPayCode(), orderProfitLog.getOrderSn(), orderProfitLog.getChainId());
                    if(map != null) {
                        Long profitId = MapUtils.getLong(map, "profitId");
                        orderProfitLog.setStatus("suc");
                        orderProfitLog.setProfitId(profitId);
                        updateResultMap(resultMap, BizMessageSource.getInstance().getMessage("cem50089"));
                    }
                } catch (BizCoreRuntimeException e) {
                    orderProfitLog.setStatus("failed");
                    if(e.getErrorContents() != null) {
                        String failReason = Arrays.stream(e.getErrorContents()).map(error->error.toString()).collect(Collectors.joining(","));
                        orderProfitLog.setFailReason(failReason);
                        updateResultMap(resultMap, failReason);
                    } else {
                        orderProfitLog.setFailReason(e.getMessage());
                        updateResultMap(resultMap, e.getMessage());
                    }
                    if(!BizMessageSource.getInstance().getMessage("cem50090").equals(orderProfitLog.getFailReason())) {
                        orderProfitRetryLogs.add(buildOrderProfitRetryLog(orderProfitLog));
                    }

                } catch (Exception e) {
                    orderProfitLog.setStatus("failed");
                    orderProfitLog.setFailReason(e.getMessage());
                    if(!BizMessageSource.getInstance().getMessage("cem50090").equals(orderProfitLog.getFailReason())) {
                        orderProfitRetryLogs.add(buildOrderProfitRetryLog(orderProfitLog));
                    }
                    updateResultMap(resultMap, e.getMessage());
                    log.error("order profit error", e);
                }
                orderProfitLog.setUpdated(updated);
            }
            dao.batchUpdate(logs, OrderProfitLog.class);

            //保存重试记录
            if(CollectionUtils.isNotEmpty(orderProfitRetryLogs)) {
                dao.batchSave(orderProfitRetryLogs, OrderProfitRetryLog.class);
            }

            //发消息
            StringBuilder stringBuilder = null;
            if(BooleanUtils.isTrue(isManual)) {
                stringBuilder = new StringBuilder(BizMessageSource.getInstance().getI18nMessage("cem50058")
                        + opsCode +
                        BizMessageSource.getInstance().getMessage("cem50059") + logs.size()).append("\r\n");
            } else {
                stringBuilder = new StringBuilder(BizMessageSource.getInstance().getI18nMessage("cem50060")  + logs.size()).append("\r\n");
            }

            for(Map.Entry<String, Integer> entry : resultMap.entrySet()) {
                stringBuilder.append(entry.getKey()).append("：").append(entry.getValue()).append("\r\n");
            }
            stringBuilder.append(BizMessageSource.getInstance().getMessage("cem50061"));

            if(BooleanUtils.isTrue(isManual) && sysConfig.getProjectProfile().equals(Constants.SYSTEM_MODEL_PRODUCT)) {
                WebHookUtil.sendOpsMessage(stringBuilder.toString());
            }

        }
    }

    private OrderProfitRetryLog buildOrderProfitRetryLog(OrderProfitLog log) {

        OrderProfitRetryLog orderProfitRetryLog = new OrderProfitRetryLog();
        orderProfitRetryLog.setChainId(log.getChainId());
        orderProfitRetryLog.setPaymentMethod(log.getPaymentMethod());
        orderProfitRetryLog.setPayCode(log.getPayCode());
        orderProfitRetryLog.setOrderSn(log.getOrderSn());
        orderProfitRetryLog.setOrderProfitLogId(log.getId());
        orderProfitRetryLog.setRetryCount(0);
        orderProfitRetryLog.setStatus("pending");
        orderProfitRetryLog.setCreated(new Timestamp(System.currentTimeMillis()));
        orderProfitRetryLog.setUpdated(new Timestamp(System.currentTimeMillis()));
        return orderProfitRetryLog;
    }

    private void updateResultMap(Map<String, Integer> resultMap, String key) {
        if(resultMap.containsKey(key)) {
            resultMap.put(key, resultMap.get(key) + 1);
        } else {
            resultMap.put(key, 1);
        }
    }
}