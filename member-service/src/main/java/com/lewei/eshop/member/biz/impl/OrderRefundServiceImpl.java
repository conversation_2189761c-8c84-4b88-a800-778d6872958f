package com.lewei.eshop.member.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lewei.eshop.auth.AuthConstants;
import com.lewei.eshop.auth.PaasToken;
import com.lewei.eshop.common.CodeGenerator;
import com.lewei.eshop.common.data.member.MemberBalanceRecordTypeEnum;
import com.lewei.eshop.common.data.order.OrderEnum;
import com.lewei.eshop.common.request.coupon.CouponStatusUpdateRequest;
import com.lewei.eshop.common.request.member.MemberBalanceRequest;
import com.lewei.eshop.common.request.member.ServiceConfirmReq;
import com.lewei.eshop.common.request.member.ServiceRefundReq;
import com.lewei.eshop.common.request.order.*;
import com.lewei.eshop.common.request.wallet.TransferMoneyRequest;
import com.lewei.eshop.common.vo.member.OrderDetailVo;
import com.lewei.eshop.common.vo.order.OrderRefundLogVO;
import com.lewei.eshop.common.vo.order.OrderRefundPageVO;
import com.lewei.eshop.common.vo.order.OrderRefundVO;
import com.lewei.eshop.common.vo.order.RefundResponse;
import com.lewei.eshop.entity.app.coupon.MemberCoupon;
import com.lewei.eshop.entity.chain.ChainConfig;
import com.lewei.eshop.entity.member.Member;
import com.lewei.eshop.entity.order.*;
import com.lewei.eshop.entity.order.types.*;
import com.lewei.eshop.entity.shop.TenantAddress;
import com.lewei.eshop.entity.shop.types.TenantAddressTypeEnum;
import com.lewei.eshop.entity.sso.types.MaPlatformEnum;
import com.lewei.eshop.entity.trade.TradeFlow;
import com.lewei.eshop.entity.trade.types.TradeTypeEnum;
import com.lewei.eshop.entity.wallet.types.ApplicationTypeEnum;
import com.lewei.eshop.entity.wallet.types.WithDrawPlatformEnum;
import com.lewei.eshop.entity.wallet.types.WithDrawTransferTypeEnum;
import com.lewei.eshop.member.MemberErrorConstants;
import com.lewei.eshop.member.OrderErrorConstants;
import com.lewei.eshop.member.biz.*;
import com.lewei.eshop.member.client.AppFeignClient;
import com.lewei.eshop.member.client.PublicFeignClient;
import com.lewei.eshop.member.client.paas.DistFeignClient;
import com.lewei.eshop.member.client.paas.request.CancelTransOrderRequest;
import com.lewei.eshop.member.proxy.AbstractRestProxy;
import com.xcrm.common.context.SystemAccessType;
import com.xcrm.common.context.XcrmThreadContext;
import com.xcrm.common.exception.BizCoreRuntimeException;
import com.xcrm.common.http.ExecutionContext;
import com.xcrm.common.http.HttpMethod;
import com.xcrm.common.http.RequestMessage;
import com.xcrm.common.http.ResponseMessage;
import com.xcrm.common.http.utils.SafeUtils;
import com.xcrm.common.page.Pagination;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.common.util.InputStreamUtils;
import com.xcrm.common.util.ListUtil;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import com.xcrm.core.db.query.QueryBuilder;
import com.xcrm.core.db.query.SaasQueryBuilder;
import com.xcrm.core.db.query.Ssqb;
import com.xcrm.core.db.query.expression.Restrictions;
import com.xcrm.core.db.saas.IIdWorker;
import com.xcrm.core.jersey.i18n.BizMessageSource;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.lewei.eshop.common.data.coupon.MemberCouponStatusEnum.S_MCDS_WSY;
import static com.lewei.eshop.common.data.coupon.MemberCouponStatusEnum.S_MCDS_YGQ;
import static com.lewei.eshop.entity.order.types.OrderProductStatusEnum.S_OPS_REFUND_SUCC;
import static com.lewei.eshop.entity.order.types.OrderProductStatusEnum.S_OPS_SALED;
import static com.lewei.eshop.entity.order.types.OrderRefundMoneyStateEnum.*;
import static com.lewei.eshop.entity.order.types.OrderRefundStateEnum.*;

/**
* 订单退款
* <AUTHOR>
* @date 2020/3/23
**/
@Component("orderRefundServiceImpl")
@Transactional
public class OrderRefundServiceImpl extends AbstractRestProxy implements IOrderRefundService {

    @Autowired
    private BaseDaoSupport dao;
    @Autowired
    private AppFeignClient appFeignClient;
    @Autowired
    private DistFeignClient distFeignClient;
    @Autowired
    @Lazy
    private IMessagePsuhService orderMessagePsuhService;

    @Autowired
    private IIdWorker idWorker;

    @Autowired
    private IMemberBalanceService memberBalanceService;

    @Autowired
    private ITradeFlowService tradeFlowService;

    @Autowired
    private PublicFeignClient publicFeignClient;

    @Autowired
    private IOrderService orderService;
    @Autowired
    private IMemberServicesService memberServicesService;

    @Override
    public void approveRefund(String refundCode, ApproveRefundRequest request, Long opUserId, Long chainId) throws IllegalAccessException {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                .and(Restrictions.eq("dataStatus", 1));
        OrderRefund orderRefund = dao.query(query, OrderRefund.class);
        if(orderRefund != null) {
            if(!orderRefund.getRefundState().equals(S_ORSS_WAIT_FOR_SELLER.value())) {
                throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
            }
            if (!(S_ORM_UN_REFUND.value().equals(orderRefund.getMoneyState()) || S_ORM_REFUND_FAILED.value().equals(orderRefund.getMoneyState())) ){
                throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
            }

            String refundWay = orderRefund.getRefundWay();
            Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
            if(order != null) {
                if(StringUtils.isBlank(order.getPaySn())) {
                    throw new BizCoreRuntimeException(OrderErrorConstants.ORDER_NOT_SUPPORT_REFUND);
                }
                if(BooleanUtils.isTrue(request.getIsAgree())) {

                    if(request.getRealRefundMoney() == null) {
                        throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_MONEY_IS_REQUIRED);
                    }

                    if(request.getRealRefundMoney().compareTo(order.getPaymentMoney()) > 0) {
                        throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_MONEY_ERROR);
                    }
                    //同意退款
                    if(OrderRefundWayEnum.S_ORW_ONLY_MONEY.value().equals(refundWay)) {

                        //仅退款
                        if (BooleanUtils.isFalse(request.getIsBalance())) {
                            handleRefundMoney(orderRefund, order.getOrderSn(),order.getPaySn(), request.getRealRefundMoney(), opUserId, BizMessageSource.getInstance().getMessage("cem50001"), "seller");
                        }else {
                            handleRefundBalance(orderRefund, order, request.getRealRefundMoney(), opUserId, BizMessageSource.getInstance().getMessage("cem50002"), "seller");
                        }

                    } else if(OrderRefundWayEnum.S_ORW_MONEY_PRODUCT.value().equals(refundWay)) {
                        //退款退货
                        TenantAddress returnAddress = queryReturnAddress(orderRefund.getTenantId());
                        if(returnAddress == null || StringUtils.isBlank(returnAddress.getContact()) || StringUtils.isBlank(returnAddress.getContactTel())) {
                            //
                            throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_RETURN_ADDRESS_IS_EMPTY);
                        }
                        orderRefund.setUpdateBy(opUserId);
                        orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
                        orderRefund.setOptUserId(opUserId);
                        orderRefund.setRealRefundMoney(request.getRealRefundMoney());
                        orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_WAIT_FOR_BUYER.value());
                        orderRefund.setRefundTimeout(DateTime.now().plusDays(7).toDate());
                        orderRefund.setReturnAddress(returnAddress.getAddress());
                        orderRefund.setReturnContact(returnAddress.getContact());
                        orderRefund.setReturnContactTel(returnAddress.getContactTel());
                        orderRefund.setSysRemark(BizMessageSource.getInstance().getMessage("cem50003"));
                        dao.update(orderRefund);
                        saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50004")
                                , BizMessageSource.getInstance().getMessage("cem50005") + returnAddress.getAddress(), "seller", orderRefund.getTenantId(),opUserId);

                    }
                    else {
                        throw new IllegalAccessException(BizMessageSource.getInstance().getMessage("cem50006"));
                    }

                    //公众号消息推送
                  //  orderMessagePsuhService.agreeRefund(order.getId(),refundCode,order.getChainId(),order.getTenantId());
                } else {
                    //拒绝退款申请
                    if(StringUtils.isBlank(request.getRefuseReason())) {
                        throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_REFUSE_REASON_IS_REQUIRED);
                    }

                    List<OrderProduct> orderProducts = queryOrderProductsByRefundCode(refundCode);
                    if(ListUtil.isNotEmpty(orderProducts)) {
                        for(OrderProduct orderProduct : orderProducts) {
                            //退款中的商品，更新为初始状态
                            if(OrderProductStatusEnum.S_OPS_REFUNDING.value().equals(orderProduct.getOrderProductStatus())) {
                                orderProduct.setOrderProductStatus(S_OPS_SALED.value());
                            }
                        }
                        dao.batchUpdate(orderProducts, OrderProduct.class);
                    }

                    //还原 orderProduct， order，
                    order.setRefundState(OrderRefundState.S_ORS_REFUND_FINISH.value());
                    order.setUpdated(new Timestamp(System.currentTimeMillis()));
                    dao.update(order);

                    orderRefund.setOptUserId(opUserId);
                    orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
                    orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_CLOSE.value());
                    orderRefund.setSysRemark(BizMessageSource.getInstance().getMessage("cem50007"));
                    orderRefund.setRefuseReason(request.getRefuseReason());
                    dao.update(orderRefund);
                    saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50007")
                            , BizMessageSource.getInstance().getMessage("cem50008") + request.getRefuseReason(), "seller", orderRefund.getTenantId(),opUserId);
                    orderMessagePsuhService.refuseRefund(refundCode,orderRefund.getMaxRefundMoney(),request.getRefuseReason(),order.getMemberId(),
                            order.getChainId(),order.getTenantId(),order.getId());
                }
            }
        }
    }

    @Override
    public OrderRefundVO queryOrderRefund(String refundCode) {
        Ssqb query = Ssqb.create("com.lewei.eshop.order.refund.queryOrderRefund").setParam("refundCode", refundCode);
        OrderRefundVO refundVO = dao.findForObj(query, OrderRefundVO.class);
        return refundVO;
    }

    @Override
    public Pagination queryOrderRefundPage(String queryKey, Long tenantId, String refundState, String refundWay
            , String refundCode, String orderSn, Date st, Date et, Integer pageNo, Integer pageSize,String refundChannel,
                                           String moneyState,String orderName,String order,String realChannel,Boolean isRecover,String orderType,String refundType) {
        List<String> refundStates = null;
        if(StringUtils.isNotBlank(refundState)) {
            refundStates = Arrays.stream(refundState.split(",", -1)).collect(Collectors.toList());
        }
        List<String> moneyStates = null;
        if(StringUtils.isNotBlank(moneyState)) {
            moneyStates = Arrays.stream(moneyState.split(",", -1)).collect(Collectors.toList());
        }
        Ssqb query = Ssqb.create("com.lewei.eshop.order.refund.queryOrderRefundPage")
                .setParam("refundCode", refundCode).setParam("queryKey", queryKey)
                .setParam("refundChannel", refundChannel)
                .setParam("tenantId", tenantId).setParam("refundStates", refundStates)
                .setParam("moneyStates", moneyStates)
                .setParam("orderType", orderType)
                .setParam("refundType", refundType)
                .setParam("isRecover", isRecover)
                .setParam("orderName", orderName)
                .setParam("order", order)
                .setParam("realChannel", realChannel)
                .setParam("refundWay", refundWay).setParam("orderSn", orderSn)
                .setParam("st", st).setParam("et", et)
                .setParam("pageNo", pageNo).setParam("pageSize", pageSize);
        Pagination pagination = dao.findForPage(query);
        List<OrderRefundPageVO> orderRefundPageVOS = (List<OrderRefundPageVO>) pagination.getList();
        for (OrderRefundPageVO orderRefundPageVO : orderRefundPageVOS) {
            String opStr = orderRefundPageVO.getOpStr();
            if (StringUtils.isNotEmpty(opStr)) {
                String[] opStrs = opStr.split("##");
                for (String str : opStrs) {
                    List<String> opStrList = Arrays.asList(str.split("@@"));
                    OrderRefundPageVO.RefundProduct refundProduct = new OrderRefundPageVO.RefundProduct();
                    refundProduct.setRefundProductId(Long.valueOf(opStrList.get(0)));
                    refundProduct.setSpuId(Long.valueOf(opStrList.get(1)));
                    refundProduct.setSkuId(Long.valueOf(opStrList.get(2)));
                    refundProduct.setRefundCount(Integer.valueOf(opStrList.get(3)));
                    refundProduct.setProductName(opStrList.get(4));
                    refundProduct.setMainImage(opStrList.get(5));
                    refundProduct.setCurrentUnitPrice(new BigDecimal(opStrList.get(6)));
                    orderRefundPageVO.getRefundProducts().add(refundProduct);
                }
            }
        }
        return pagination;

    }

    @Override
    public List<OrderRefundLogVO> queryOrderRefundLogs(String refundCode) {
        Ssqb query = Ssqb.create("com.lewei.eshop.order.refund.queryOrderRefundLogs").setParam("refundCode", refundCode);
        return dao.findForList(query, OrderRefundLogVO.class);
    }

    @Override
    public Boolean checkOrderProductIsRefunding(OrderProduct orderProduct) {
        if(orderProduct == null) {
            return Boolean.FALSE;
        } else {
            if(OrderProductStatusEnum.S_OPS_REFUNDING.value().equals(orderProduct.getOrderProductStatus())) {
                throw new BizCoreRuntimeException(OrderErrorConstants.ORDER_IS_REFUNDING_ERROR);
            }
            return Boolean.TRUE;
        }
    }

    @Override
    public Boolean checkOrderIsRefunding(Long orderId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("id", orderId));
        Order order = dao.query(query, Order.class);
        return checkOrderIsRefunding(order);
    }

    @Override
    public Boolean checkOrderIsRefunding(Order order) {
        if(order == null) {
            return Boolean.FALSE;
        } else {
            if(OrderRefundState.S_ORS_REFUNDING.value().equals(order.getRefundState())) {
                throw new BizCoreRuntimeException(OrderErrorConstants.ORDER_IS_REFUNDING_ERROR);
            }
            return Boolean.TRUE;
        }
    }

    @Override
    public void confirmReceived(String refundCode, Long opUserId, String logTitle, String logFrom) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                .and(Restrictions.eq("dataStatus", 1));
        OrderRefund orderRefund = dao.query(query, OrderRefund.class);
        if(orderRefund != null) {
            if(!orderRefund.getRefundState().equals(OrderRefundStateEnum.S_ORSS_WAIT_FOR_SELLER_RECEIVE.value())) {
                throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
            }

            Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
            if(order != null) {
                if(!S_ORM_UN_REFUND.value().equals(orderRefund.getMoneyState())) {
                    throw new BizCoreRuntimeException(OrderErrorConstants.MONEY_IS_REFUNDING_ERROR);
                }

                if(StringUtils.isBlank(order.getPaySn())) {
                    throw new BizCoreRuntimeException(OrderErrorConstants.ORDER_NOT_SUPPORT_REFUND);
                }
                //调用退款api，发起资金退款
                handleRefundMoney(orderRefund, order.getOrderSn(),order.getPaySn(), orderRefund.getRealRefundMoney(), opUserId, logTitle, logFrom);

                //需要还原库存list <skuId，refundCount>
                List<Map<String, Object>> restoreSkus = new ArrayList<>();
                //退货商品
                List<OrderRefundProduct> refundProducts = queryOrderRefundProducts(refundCode);

                for(OrderRefundProduct refundProduct : refundProducts) {
                    Map<String, Object> restoreSku = new HashMap<>();
                    restoreSku.put("skuId", refundProduct.getSkuId());
                    restoreSku.put("refundCount", refundProduct.getRefundCount());
                    restoreSkus.add(restoreSku);
                }

                Ssqb restoreStockSql = Ssqb.create("com.lewei.eshop.order.refund.updateSkuQuantityForRestore")
                        .setParam("restoreSkus", restoreSkus);
                dao.updateByMybatis(restoreStockSql);
            }
        }
    }

    @Override
    public void finishOrderRefundByManual(String refundCode) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                .and(Restrictions.eq("dataStatus", 1));
        OrderRefund orderRefund = dao.query(query, OrderRefund.class);
        if(orderRefund != null) {
//            if(!orderRefund.getRefundState().equals(S_ORSS_WAIT_FOR_SELLER_RECEIVE.value())
//                    && !orderRefund.getRefundState().equals(S_ORSS_WAIT_FOR_SELLER.value())) {
//                throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
//            }
//
//            if (!S_ORM_REFUND_FAILED.value().equals(orderRefund.getMoneyState())) {
//                throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_FINISH_ERROR);
//            }
            //退款成功处理
            handleRefundSucc(orderRefund);
            saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50009")
                    , BizMessageSource.getInstance().getMessage("cem50010"), "seller", orderRefund.getTenantId(),null);
        }
    }

    @Override
    public void triggerTimeOutRefundJob() {
        Date now = new Timestamp(System.currentTimeMillis());
        List<String> refundStates =  Arrays.asList(S_ORSS_WAIT_FOR_SELLER.value(), S_ORSS_WAIT_FOR_BUYER.value(), S_ORSS_WAIT_FOR_SELLER_RECEIVE.value());
        QueryBuilder query = QueryBuilder.where(Restrictions.in("refundState", refundStates))
                .and(Restrictions.in("moneyState", S_ORM_UN_REFUND.value(), S_ORM_REFUND_FAILED.value()))
                .and(Restrictions.lt("refundTimeout", now))
                .and(Restrictions.eq("dataStatus", 1));
        List<OrderRefundJob> orderRefunds = dao.queryList(query, OrderRefundJob.class);

        for(OrderRefundJob orderRefund : orderRefunds) {
            try {
                XcrmThreadContext.setChainId(orderRefund.getChainId());
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);

                Date refundTimeOut = orderRefund.getRefundTimeout();
                String refundState = orderRefund.getRefundState();
                String moneyState = orderRefund.getMoneyState();
                if(refundTimeOut != null && refundTimeOut.before(now)) {
                    //退款单据未正常处理，超时
                    String title = "";
                    if(S_ORSS_WAIT_FOR_SELLER.value().equals(refundState)) {
                        title = BizMessageSource.getInstance().getMessage("cem50011");
                        if(moneyState.equals(S_ORM_UN_REFUND.value())) {
                            //商家没有主动处理，没有做资金退回操作
                            Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
                            if(order != null) {
                                OrderRefund or = new OrderRefund();
                                BeanUtils.copyProperties(orderRefund, or);
                                handleRefundMoney(or, order.getOrderSn(),order.getPaySn(), orderRefund.getMaxRefundMoney(), null
                                        , title, "sys");
                            }
                        } else {
                            closeOrderRefund(orderRefund, title);
                        }
                    } else if(S_ORSS_WAIT_FOR_BUYER.value().equals(refundState)) {
                        title = BizMessageSource.getInstance().getMessage("cem50012");
                        closeOrderRefund(orderRefund, title);
                    } else if(S_ORSS_WAIT_FOR_SELLER_RECEIVE.value().equals(refundState)) {
                        title = BizMessageSource.getInstance().getMessage("cem50013");
                        if(moneyState.equals(S_ORM_UN_REFUND.value())) {
                            //商家没有主动 确认收货，没有做资金退回操作
                            //系统自动确认收货
                            confirmReceived(orderRefund.getRefundCode(), null, title, "sys");
                        } else {
                            closeOrderRefund(orderRefund, title);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("triggerTimeOutRefundJob failed" , e);
            } finally {
                XcrmThreadContext.removeAccessType();
                XcrmThreadContext.removeChainId();
            }
        }
    }


    @Override
    public void agreeRefund(AgreeRefundRequest request, String refundCode,Long userId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                .and(Restrictions.eq("dataStatus", 1));
        OrderRefund orderRefund = dao.query(query, OrderRefund.class);
        if(orderRefund != null) {
            this.approveRefundValidate(orderRefund);
            Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
            if(order != null) {
                if (request.getRealRefundMoney().compareTo(order.getPaymentMoney()) > 0) {
                    throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_MONEY_ERROR);
                }


                orderRefund.setMoneyState(S_ORM_REFUND_SUC.value());
                orderRefund.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem50014"));
                orderRefund.setUpdateBy(userId);
                orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
                orderRefund.setOptUserId(userId);
                orderRefund.setRealRefundMoney(request.getRealRefundMoney());

                saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50015")
                        , BizMessageSource.getInstance().getMessage("cem50016") + request.getRealRefundMoney(), "seller", orderRefund.getTenantId(),userId);

                //转账打款
//                TransferMoneyRequest transferMoneyRequest = new TransferMoneyRequest();
//                BeanUtils.copyProperties(request,transferMoneyRequest);
//                transferMoneyRequest.setIsRefund(true);
//                transferMoneyRequest.setIdentity(true);
//                transferMoneyRequest.setMaxWithdrawAmount(orderRefund.getMaxRefundMoney());
//                transferMoneyRequest.setIsSubBalance(false);
//                transferMoneyRequest.setWithdrawPlatform(WithDrawPlatformEnum.eshop.value());
//                transferMoneyRequest.setMemberId(order.getMemberId());
//                transferMoneyRequest.setPlId(orderRefund.getRefundCode());
//                transferMoneyRequest.setWithdrawAmount(request.getRealRefundMoney());
//                transferMoneyRequest.setApplicationType(ApplicationTypeEnum.S_WWA_REFUND.value());
//                Map map = appFeignClient.applyWithDraw(transferMoneyRequest);
//                if (map != null) {
//                    orderRefund.setWithdrawNo(MapUtils.getString(map,"withdrawOrderNo"));
//                }
                dao.update(orderRefund);
                if(order.getOrderType().equals(OrderEnum.S_OOT_PRODUCT.value())){
                    //IP分润退款处理(只处理商品类型)
                    memberServiceRefund(order.getId(),order.getChainId());
                }

            }
        }
    }

    /**
     * 众筹成功Ip分润退款处理
     * @param plId
     */
    private void memberServiceRefund(Long plId,Long chainId){
        ServiceRefundReq req = new ServiceRefundReq();
        req.setPlId(plId);
        memberServicesService.batchRefundServiceRefund(req,chainId);
    }
    @Override
    public void refuseRefund(RefuseRefundRequest request, String refundCode, Long userId) {
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                .and(Restrictions.eq("dataStatus", 1));
        OrderRefund orderRefund = dao.query(query, OrderRefund.class);
        if(orderRefund != null) {
            if(!orderRefund.getRefundState().equals(S_ORSS_WAIT_FOR_SELLER.value())) {
                throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
            }

            if(!S_ORM_UN_REFUND.value().equals(orderRefund.getMoneyState())) {
                throw new BizCoreRuntimeException(OrderErrorConstants.MONEY_IS_REFUNDING_ERROR);
            }


            orderRefund.setOptUserId(userId);
            orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
            orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_CLOSE.value());
            orderRefund.setSysRemark(BizMessageSource.getInstance().getMessage("cem50017"));
            orderRefund.setRefuseReason(request.getRefuseReason());
            dao.update(orderRefund);
            saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50017")
                    , BizMessageSource.getInstance().getMessage("cem50008") + request.getRefuseReason(), "seller", orderRefund.getTenantId(),userId);
            Order order = dao.queryById(orderRefund.getOrderId(), Order.class);


            if(order != null) {
                order.setMemberDeleteStatus(true);
                order.setUpdated(new Timestamp(System.currentTimeMillis()));
                order.setStatus(OrderEnum.S_OS_PAYED.value());
                dao.update(order);
            }
        }
    }

    /**
     * 余额退款审批
     *
     * @param request
     * @param refundCode
     * @param userId
     * <AUTHOR>
     */
    @Override
    public void approveRefundByBalance(AgreeRefundBalanceRequest request, String refundCode, Long userId) {
        if (!WithDrawTransferTypeEnum.S_WWT_BALANCE.value().equals(request.getTransferType())) {
            throw new BizCoreRuntimeException(OrderErrorConstants.INCORRECT_REFUND_TYPE);
        }
        SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                .and(Restrictions.eq("dataStatus", 1));
        OrderRefund orderRefund = dao.query(query, OrderRefund.class);
        if (orderRefund != null) {
            // 退款审批验证
            this.approveRefundValidate(orderRefund);
            Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
            if (order != null) {
                if (request.getRealRefundMoney().compareTo(order.getPaymentMoney()) > 0) {
                    throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_MONEY_ERROR);
                }
                this.saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50015")
                        , BizMessageSource.getInstance().getMessage("cem50016") + request.getRealRefundMoney(), "seller", orderRefund.getTenantId(), userId);
                // 余额转账
                this.saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50018") , null, "sys");

                Long memberId = order.getMemberId();

                TradeFlowReq tradeFlow = new TradeFlowReq();
                tradeFlow.setTradeContent(BizMessageSource.getInstance().getMessage("cem50019"));
                tradeFlow.setOrderId(orderRefund.getOrderId());
                tradeFlow.setMemberId(memberId);
                tradeFlow.setTradeAmount(request.getRealRefundMoney());
                tradeFlow.setTradeType(TradeTypeEnum.S_TT_REFUND.value());
                tradeFlow.setTradeSource("pc");
                tradeFlowService.saveTradeFlow(tradeFlow);

                BigDecimal moneyRatio = publicFeignClient.queryChainConfig().getMoneyRatio();
                Ssqb ssqb = Ssqb.create("com.lewei.eshop.order.refund.updateByRefundCode")
                        .setParam("refundCode",refundCode)
                        .setParam("moneyState",OrderRefundMoneyStateEnum.S_ORM_REFUND_SUC.value())
                        .setParam("refundState",OrderRefundStateEnum.S_ORSS_SUCCESS.value())
                        .setParam("optUserId",userId)
                        .setParam("realRefundMoney",request.getRealRefundMoney())
                        .setParam("realRefundCoin",request.getRealRefundMoney().multiply(moneyRatio))
                        .setParam("realChannel",request.getType())
                        .setParam("updateBy",userId);
                int i = dao.updateByMybatis(ssqb);
                if(i != 1){
                    throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
                }

                ssqb = Ssqb.create("com.lewei.eshop.order.updateRefundStateByOrderId")
                        .setParam("orderId", order.getId())
                        .setParam("refundState", OrderRefundState.S_ORS_REFUND_FINISH.value());
                i = dao.updateByMybatis(ssqb);
                if(i != 1){
                    throw new BizCoreRuntimeException(OrderErrorConstants.ORDER_STATUS_ERROR);
                }

                MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
                balanceRequest.setGift(BigDecimal.ZERO);
                balanceRequest.setBalance(request.getRealRefundMoney());
                balanceRequest.setMemberId(memberId);
                balanceRequest.setContent(BizMessageSource.getInstance().getMessage("cem50020"));
                balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
                memberBalanceService.memberBalanceHandler(balanceRequest);

                if(order.getOrderType().equals(OrderEnum.S_OOT_PRODUCT.value())){
                    //IP分润退款处理(只处理商品类型)
                    memberServiceRefund(order.getId(),order.getChainId());
                }
            }
        }
    }


    @Override
    public void applyForRefund(ApplyForRefundReq refundReq) {
        Long orderId = refundReq.getOrderId();
        OrderDetailVo orderDetailVo = orderService.queryOrderDetail(orderId);
        if (orderDetailVo == null) {
            log.error("OrderRefundServiceImpl.applyForRefund order is null");
            return;
        }
        if (orderDetailVo.getOrderMoney().compareTo(BigDecimal.ZERO) <= 0) {
            log.error("OrderRefundServiceImpl.applyForRefund orderMoney is zero");
            return;
        }
        if (!OrderEnum.S_OS_DONE.value().equals(orderDetailVo.getStatus())) {
            log.error("OrderRefundServiceImpl.applyForRefund status is error status={}",orderDetailVo.getStatus());
            return;
        }

        if (StringUtils.isEmpty(orderDetailVo.getPaySn())){
            log.error("OrderRefundServiceImpl.applyForRefund PaySn is null ");
            return;
        }

        Date now = DateFormatUtils.getNow();
        //更新的订单状态
        orderService.updateOrderStatus(orderId,OrderEnum.S_OS_REFUNDING.value(),null);
        //保存退款单据

        String refundCode = CodeGenerator.getShortCode("R");
        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setOrderId(orderId);
        orderRefund.setReason("");
        orderRefund.setRefundDesc("");
        orderRefund.setRefundImages("");
        orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_WAIT_FOR_SELLER.value());
        orderRefund.setRefundWay(OrderRefundWayEnum.S_ORW_ONLY_MONEY.value());
        orderRefund.setMoneyState(OrderRefundMoneyStateEnum.S_ORM_REFUNDING.value());
        orderRefund.setRefundType(OrderRefundTypeEnum.S_RT_REFUND.value());
        orderRefund.setCreated(now);
        orderRefund.setRefundCode(refundCode);
        orderRefund.setRefundTimeout(DateTime.now().plusDays(7).toDate());
        orderRefund.setMaxRefundMoney(orderDetailVo.getOrderMoney());
        orderRefund.setRealRefundMoney(orderDetailVo.getOrderMoney());
        orderRefund.setSysRemark(BizMessageSource.getInstance().getMessage("cem50068"));
        orderRefund.setRealChannel("wx");
        orderRefund.setReason(refundReq.getReason());
        dao.create(orderRefund);

        List<OrderRefundProduct> refundProducts = new ArrayList<>();


        for (OrderDetailVo.OrderProduct orderProduct : orderDetailVo.getOrderProducts()) {
            OrderRefundProduct refundProduct = new OrderRefundProduct();
            refundProduct.setRefundCode(refundCode);
            refundProduct.setOrderProductId(orderProduct.getOrderProductId());
            refundProduct.setSpuId(orderProduct.getSpuId());
            refundProduct.setSkuId(orderProduct.getSkuId());
            refundProduct.setRefundCount(orderProduct.getQuantity());
            refundProduct.setCreated(now);
            refundProduct.setPrimeCostFee(orderProduct.getPrimeCostFee());
            refundProduct.setMainImage(orderProduct.getMainImage());
            refundProduct.setProductName(orderProduct.getProductName());
            refundProducts.add(refundProduct);
//            orderProduct.setOrderProductStatus(OrderProductStatusEnum.S_OPS_REFUND_SUCC.value());
//            orderProduct.setLastRefundCode(refundCode);
//            dao.update(orderProduct);
        }

        dao.batchSave(refundProducts, OrderRefundProduct.class);

        //退款处理
        this.handleRefundMoney(orderRefund,orderDetailVo.getOrderSn(),orderDetailVo.getPaySn(),orderRefund.getRealRefundMoney(), null, BizMessageSource.getInstance().getMessage("cem50021"), "sys");

    }

    private void approveRefundValidate(OrderRefund orderRefund) {
        if (!orderRefund.getRefundState().equals(S_ORSS_WAIT_FOR_SELLER.value())) {
            throw new BizCoreRuntimeException(OrderErrorConstants.REFUND_STATE_ERROR);
        }
        if (!S_ORM_UN_REFUND.value().equals(orderRefund.getMoneyState())) {
            throw new BizCoreRuntimeException(OrderErrorConstants.MONEY_IS_REFUNDING_ERROR);
        }
    }

    private void saveRefundLog(String refundCode, String title, String content, String from) {
        OrderRefundLog log = new OrderRefundLog();
        log.setRefundCode(refundCode);
        log.setTitle(title);
        log.setContent(content);
        log.setFrom(from);
        log.setCreated(new Timestamp(System.currentTimeMillis()));

        dao.save(log);
    }

    /**
     * 关闭退款单据
     * @param orderRefund               退款单据对象
     * @param title                     日志标题
     */
    private void closeOrderRefund(OrderRefundJob orderRefund, String title) {
        List<OrderProduct> orderProducts = queryOrderProductsByRefundCode(orderRefund.getRefundCode());
        if(ListUtil.isNotEmpty(orderProducts)) {
            for(OrderProduct orderProduct : orderProducts) {
                //退款中的商品，更新为初始状态
                if(OrderProductStatusEnum.S_OPS_REFUNDING.value().equals(orderProduct.getOrderProductStatus())) {
                    orderProduct.setOrderProductStatus(S_OPS_SALED.value());
                }
            }
            dao.batchUpdate(orderProducts, OrderProduct.class);
        }

        Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setRefundState(OrderRefundState.S_ORS_REFUND_FINISH.value());
        updateOrder.setUpdated(new Timestamp(System.currentTimeMillis()));
        dao.update(updateOrder);

        orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
        orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_CLOSE.value());
        orderRefund.setSysRemark(title);
        dao.update(orderRefund);

        saveRefundLog(orderRefund.getRefundCode(), title, null, "sys", orderRefund.getTenantId(),null);
    }


    @Override
    public String handleRefundResult(HttpServletRequest request) {
        try {
            String httpBody = InputStreamUtils.InputStreamTOString(request.getInputStream(),"utf-8");
            log.debug("handleRefundResult callbackData : {}", httpBody);
            JSONObject jsonObject = JSON.parseObject(httpBody);
            String refundStatus = jsonObject.getString("refundStatus");
            String refundCode = jsonObject.getString("refundCode");
            //refund_suc
            QueryBuilder query = QueryBuilder.create("com.lewei.eshop.order.refund.queryOrderRefundForCallback")
                    .setParam("refundCode", refundCode);
            OrderRefund orderRefund = dao.findForObj(query, OrderRefund.class);
            if (orderRefund != null) {
                XcrmThreadContext.setChainId(orderRefund.getChainId());
                XcrmThreadContext.setAccessType(SystemAccessType.chain_admin);
                //退款资金状态--退款中
                if (S_ORM_REFUNDING.value().equals(orderRefund.getMoneyState())) {
                    //支付系统回馈结果，退款成功
                    if("refund_suc".equals(refundStatus)) {
                        handleRefundSucc(orderRefund);
                        saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50009")
                                , BizMessageSource.getInstance().getMessage("cem50022") + jsonObject.getString("refundRecvAccount"), "sys", orderRefund.getTenantId(),null);

                    } else {
                        orderRefund.setMoneyState(S_ORM_REFUND_FAILED.value());
                        orderRefund.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem50023") + jsonObject.getString("memo"));
                        orderRefund.setSysRemark(BizMessageSource.getInstance().getMessage("cem50024"));
                        dao.update(orderRefund);
                        saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50025")
                                , BizMessageSource.getInstance().getMessage("cem50023") + jsonObject.getString("memo"), "sys", orderRefund.getTenantId(),null);
                    }


                }
            }
        }  catch (Exception e) {
            log.error("#########################handleRefundResult#########################",e);
            //throw e;
        } finally {
            XcrmThreadContext.removeChainId();
            XcrmThreadContext.removeAccessType();
        }
        return "ok";
    }

    /**
     * 退款成功处理
     * @param orderRefund               退款单据对象
     */
    private void handleRefundSucc(OrderRefund orderRefund) {
        String refundCode = orderRefund.getRefundCode();
        Order order = dao.queryById(orderRefund.getOrderId(), Order.class);
//        String orderType = order.getOrderType();
//        Long memberCouponId = order.getMemberCouponId();
//        //退货商品
//        List<OrderRefundProduct> refundProducts = queryOrderRefundProducts(refundCode);
//        //<orderProductId, refundCount>
//        Map<Long, Integer> refundProductCountMap = refundProducts.stream().collect(
//                Collectors.toMap(OrderRefundProduct::getOrderProductId, a->a.getRefundCount()));
//        //订单产品列表
//        List<OrderProduct> orderProducts = dao.queryList(
//                SaasQueryBuilder.where(Restrictions.in("id", refundProductCountMap.keySet())), OrderProduct.class);
//
//        //未验证的验证码映射<orderProductId, List<OrderProductVerify>>
//        Map<Long, List<OrderProductVerify>> availableVerifyCodeMapping = new HashMap<>();
//        //已成功验证的验证码映射（非退款）
//        Map<Long, List<OrderProductVerify>> checkedVerifyCodeMapping = new HashMap<>();
//        if(OrderEnum.S_OOT_SERVICE.value().equals(orderType)) {
//            //服务项目，验证可退数量，已验证的不能退款
//            SaasQueryBuilder queryAll = SaasQueryBuilder
//                    .where(Restrictions.in("orderProductId", refundProductCountMap.keySet()))
//                    //.and(Restrictions.eq("isAvailable", true))
//                    .and(Restrictions.eq("dataStatus",true));
//            List<OrderProductVerify> availableVerifyCodes = dao.queryList(queryAll,OrderProductVerify.class);
//            if(ListUtil.isNotEmpty(availableVerifyCodes)) {
//                //存在验证码的服务项目订单 <orderProductId, List<OrderProductVerify>>
//                availableVerifyCodeMapping = availableVerifyCodes.stream().filter(code->BooleanUtils.isTrue(code.getIsAvailable()))
//                        .collect(Collectors.groupingBy(OrderProductVerify::getOrderProductId));
//
//                checkedVerifyCodeMapping = availableVerifyCodes.stream()
//                        .filter(code->BooleanUtils.isFalse(code.getIsAvailable()) && code.getVerifyTime() != null)
//                        .collect(Collectors.groupingBy(OrderProductVerify::getOrderProductId));
//            }
//        }

//        for(OrderProduct orderProduct : orderProducts) {
//            //当前申请退货个数
//            Integer refundCount = refundProductCountMap.get(orderProduct.getId());
//            //已成功验证的验证码（非退款）的个数
//            int checkedVerifyCodeCount = 0;
//            if(availableVerifyCodeMapping.get(orderProduct.getId()) != null) {
//                //取消验证码可用状态
//                List<OrderProductVerify> availableVerifyCodes = availableVerifyCodeMapping.get(orderProduct.getId());
//                //验证码应退数量
//                int verifyCodeRefundCount = refundCount;
//                for(int i = 0; i < availableVerifyCodes.size(); i++) {
//                    OrderProductVerify code = availableVerifyCodes.get(i);
//                    if(verifyCodeRefundCount > 0) {
//                        code.setIsAvailable(false);
//                        code.setSysRemark("已退款");
//                        verifyCodeRefundCount--;
//                    }
//                }
//                if(checkedVerifyCodeMapping.get(orderProduct.getId()) != null) {
//                    checkedVerifyCodeCount = checkedVerifyCodeMapping.get(orderProduct.getId()).size();
//                }
//
//                dao.batchUpdate(availableVerifyCodes, OrderProductVerify.class);
//            }
//
//            //更新成退款成功状态,
//            //是否还有剩余未退商品，购买数量 - 已成功验证个数 - 历史已退数 - 当前申请退货数量
//            boolean hasLeft = orderProduct.getQuantity() - checkedVerifyCodeCount - orderProduct.getRefundSucCount() - refundCount > 0;
//            log.debug("hasLeft {}, quantity {} checkedVerifyCodeCount {} refundSucCount {} refundCount {}",hasLeft, orderProduct.getQuantity()
//                    , checkedVerifyCodeCount, orderProduct.getRefundSucCount(), refundCount);
//            orderProduct.setOrderProductStatus(hasLeft ? S_OPS_SALED.value() : S_OPS_REFUND_SUCC.value());
//            orderProduct.setRefundSucCount(orderProduct.getRefundSucCount() + refundCount);
//        }
//        dao.batchUpdate(orderProducts, OrderProduct.class);
//
//        SaasQueryBuilder salesCountSql = SaasQueryBuilder.where(Restrictions.eq("orderId", orderRefund.getOrderId()));
//        List<OrderProduct> allOrderProducts = dao.queryList(salesCountSql, OrderProduct.class);
//        List<OrderProduct> refundedOrderProducts = allOrderProducts.stream()
//                .filter(orderProduct -> orderProduct.getOrderProductStatus().equals(S_OPS_REFUND_SUCC.value())).collect(Collectors.toList());

//        if(allOrderProducts.size() == refundedOrderProducts.size()) {
//            //商品以全部退款
//            order.setStatus(OrderEnum.S_OS_CANCELED.value());
//            order.setCancelReason("订单全额退款，订单取消");
//            if(order.getPoint() != null && order.getPoint().compareTo(BigDecimal.ZERO) > 0) {
//                //成长值扣除处理
//                Member member=  dao.queryById(order.getMemberId(), Member.class);
//                if(member != null) {
//                    BigDecimal memberPoint = member.getPoint();
//                    memberPoint = memberPoint.compareTo(order.getPoint()) >= 0 ? memberPoint.subtract(order.getPoint()) : BigDecimal.ZERO;
//                    member.setPoint(memberPoint);
//                    dao.update(member);
//                }
//            }
            //订单使用了优惠券
//            if(memberCouponId != null && memberCouponId.compareTo(0L) > 0) {
//                Date receiveTime = order.getReceiveTime();
//                if(receiveTime == null) {
//                    //没有确认收货
//                    try {
//                        MemberCoupon memberCoupon = appFeignClient.queryMemberCouponById(memberCouponId);
//                        Date now = new Timestamp(System.currentTimeMillis());
//                        if(memberCoupon != null) {
//                            CouponStatusUpdateRequest request = new CouponStatusUpdateRequest();
//                            Date beginTime = memberCoupon.getBeginTime();
//                            Date endTime = memberCoupon.getEndTime();
//                            if(now.after(beginTime) && now.before(endTime)) {
//                                request.setStatus(S_MCDS_WSY.value());
//                                appFeignClient.updateMemberCouponStatus(memberCouponId, request);
//                            } else {
//                                request.setStatus(S_MCDS_YGQ.value());
//                                appFeignClient.updateMemberCouponStatus(memberCouponId, request);
//                            }
//                        }
//                    } catch (Exception e) {
//                        log.error("订单退款优惠券还原失败", e);
//                    }
//                }
//            }
//        }

        //更新订单退款状态
        order.setRefundState(OrderRefundState.S_ORS_REFUND_FINISH.value());
        order.setUpdated(new Timestamp(System.currentTimeMillis()));
        order.setStatus(OrderEnum.S_OS_CANCELED.value());
        dao.update(order);


        //更新退款单据状态
        orderRefund.setRefundState(OrderRefundStateEnum.S_ORSS_SUCCESS.value());
        orderRefund.setSysRemark(BizMessageSource.getInstance().getMessage("cem50026"));
        orderRefund.setMoneyState(OrderRefundMoneyStateEnum.S_ORM_REFUND_SUC.value());
        orderRefund.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem50027"));
        orderRefund.setRefundSuccessTime(new Timestamp(System.currentTimeMillis()));
        dao.update(orderRefund);

        //存流水
        saveTradeFlow(orderRefund, order);

//        if(OrderBizTypeEnum.S_OBZ_DIST.value().equals(order.getOrderBizType())) {
//            //分销订单 撤销分佣
//            CancelTransOrderRequest request = new CancelTransOrderRequest();
//            request.setOrderCode(order.getOrderSn());
//
//            distFeignClient.cancelProfitTransOrder(request);
//        }
        //退款成功消息推送
//        String refundProductNames = refundedOrderProducts.stream().map(OrderProduct::getProductName).collect(Collectors.joining(","));
//        orderMessagePsuhService.refundSuccess(order.getOrderSn(),order.getMemberId(),refundProductNames,OrderRefundWayEnum.fromCode(orderRefund.getRefundWay()).desc(),
//                orderRefund.getRealRefundMoney(),orderRefund.getRefundSuccessTime(),order.getChainId(),order.getTenantId(),order.getId());
    }

    //保存交易流水
    private void saveTradeFlow(OrderRefund orderRefund, Order order) {


        TradeFlowReq tradeFlow = new TradeFlowReq();
        tradeFlow.setTradeContent(order.getOrderTitle() + "-"+BizMessageSource.getInstance().getMessage("cem50019"));
        tradeFlow.setTradeType(TradeTypeEnum.S_TT_REFUND.value());
        tradeFlow.setOrderId(order.getId());
        tradeFlow.setMemberId(order.getMemberId());
        tradeFlow.setTradeAmount(orderRefund.getRealRefundMoney());
        if (order.getOrderType().equals(OrderEnum.S_OOT_CROWD_FUNDING.value())){
            tradeFlow.setPaymentMethod(order.getPaymentMethod());
        }
        tradeFlow.setTradeSource(MaPlatformEnum.PC.value());
        tradeFlow.setCreateBy(null);
        tradeFlowService.saveTradeFlow(tradeFlow);
    }

    private List<OrderProduct> queryOrderProductsByRefundCode(String refundCode) {
        List<OrderRefundProduct> refundProducts = queryOrderRefundProducts(refundCode);
        if(ListUtil.isNotEmpty(refundProducts)) {
            List<Long> orderProductIds = refundProducts.stream().map(OrderRefundProduct::getOrderProductId).collect(Collectors.toList());
            return dao.queryList(SaasQueryBuilder.where(Restrictions.in("id", orderProductIds)), OrderProduct.class);
        }
        return null;
    }

    private List<OrderRefundProduct> queryOrderRefundProducts(String refundCode) {
        return dao.queryList(SaasQueryBuilder.where(Restrictions.eq("refundCode", refundCode))
                , OrderRefundProduct.class);
    }

    /**
     * 获取退款地址
     * @param tenantId                  门店id
     * @return                          退款地址
     */
    private TenantAddress queryReturnAddress(Long tenantId) {
        TenantAddress tenantAddress = null;
        if(tenantId != null) {
            SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("type", TenantAddressTypeEnum.refund.value()))
                    .and(Restrictions.eq("tenantId", tenantId)).and(Restrictions.eq("dataStatus", 1));
            tenantAddress = dao.query(query, TenantAddress.class);
        }

        if(tenantAddress == null) {
            //门店没有设置具体退货地址， 总部默认退货地址
            SaasQueryBuilder query = SaasQueryBuilder.where(Restrictions.eq("type", TenantAddressTypeEnum.refund.value()))
                    .and(Restrictions.isNull("tenantId")).and(Restrictions.eq("dataStatus", 1));
            tenantAddress = dao.query(query, TenantAddress.class);
        }


        if(tenantAddress != null) {
            QueryBuilder queryBuilder
                    = QueryBuilder.create("com.lewei.eshop.order.refund.queryAddressInfo").setParam("areaCode", tenantAddress.getAreaCode());
            String areaInfo = dao.findForObj(queryBuilder, String.class);
            tenantAddress.setAddress(areaInfo + " " + tenantAddress.getAddress());
            return tenantAddress;
        }

        return null;
    }

    /**
     * 保存退款日志
     * <AUTHOR>
     * @param refundCode                单据号
     * @param title                     标题
     * @param content                   内容
     * @param from                      来自 sys/seller/buyer
     * @param tenantId                  门店id
     */
    private void saveRefundLog(String refundCode, String title, String content, String from, Long tenantId,Long userId) {
        OrderRefundLog log = new OrderRefundLog();
        log.setTenantId(tenantId);
        log.setRefundCode(refundCode);
        log.setTitle(title);
        log.setContent(content);
        log.setFrom(from);
        log.setCreateBy(userId);
        log.setCreated(new Timestamp(System.currentTimeMillis()));

        dao.save(log);
    }

    /**
     * 处理退款单的资金
     * @param orderRefund               退款单
     * @param realRefundMoney           实际退款金额
     * @param opUserId                  操作人
     * @param logTitle                  日志标题
     * @param logFrom                   日志来源
     */
    private void handleRefundMoney(OrderRefund orderRefund, String orderCode,String paySn, BigDecimal realRefundMoney, Long opUserId, String logTitle, String logFrom) {


        String refundCode = orderRefund.getRefundCode();

        saveRefundLog(refundCode, logTitle
                , BizMessageSource.getInstance().getMessage("cem50016") + realRefundMoney, logFrom, orderRefund.getTenantId(),opUserId);

        RefundWxRequest refundWxRequest = new RefundWxRequest();
        refundWxRequest.setOrderCode(orderCode);
        refundWxRequest.setPayCode(paySn);
        refundWxRequest.setRefundCode(refundCode);
        refundWxRequest.setRefundMoney(realRefundMoney);
        refundWxRequest.setNotifyUrl(sysConfig.getEshopService() + "/api/member/refund/cb");

        RefundResponse response = callRefundApi(refundWxRequest, orderRefund.getChainId());
        if("0".equals(response.getCode())) {
            orderRefund.setMoneyState(S_ORM_REFUNDING.value());
            orderRefund.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem50014"));
            orderRefund.setUpdateBy(opUserId);
            orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
            orderRefund.setOptUserId(opUserId);
            orderRefund.setRealRefundMoney(realRefundMoney);
            dao.update(orderRefund);

        }else {
            log.info("返回失败信息是"+response.getMsg());
            orderRefund.setMoneyState(S_ORM_REFUND_FAILED.value());
            orderRefund.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem50028"));
            orderRefund.setUpdateBy(opUserId);
            orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
            orderRefund.setOptUserId(opUserId);
            orderRefund.setRealRefundMoney(realRefundMoney);
            dao.update(orderRefund);
            if (StringUtils.isNotBlank(response.getMsg()) && response.getMsg().length() > 500){
                response.setMsg(response.getMsg().substring(0,500));
            }
            saveRefundLog(refundCode, BizMessageSource.getInstance().getMessage("cem50028")
                    , response.getMsg(), logFrom, orderRefund.getTenantId(),opUserId);
        }


    }


    private void handleRefundBalance(OrderRefund orderRefund, Order order, BigDecimal realRefundMoney, Long opUserId, String logTitle, String logFrom) {
        String refundCode = orderRefund.getRefundCode();

        MemberBalanceRequest balanceRequest = new MemberBalanceRequest();
        balanceRequest.setGift(BigDecimal.ZERO);
        balanceRequest.setBalance(realRefundMoney);
        balanceRequest.setMemberId(order.getMemberId());
        balanceRequest.setContent(order.getOrderTitle()+"-"+BizMessageSource.getInstance().getMessage("cem50019"));
        balanceRequest.setType(MemberBalanceRecordTypeEnum.income.value());
        memberBalanceService.memberBalanceHandler(balanceRequest);

        ChainConfig chainConfig = publicFeignClient.queryChainConfig();

        orderRefund.setMoneyState(S_ORM_REFUND_SUC.value());
        orderRefund.setRefundState(S_ORSS_SUCCESS.value());
        orderRefund.setMoneyRemark(BizMessageSource.getInstance().getMessage("cem50027"));
        orderRefund.setUpdateBy(opUserId);
        orderRefund.setUpdated(new Timestamp(System.currentTimeMillis()));
        orderRefund.setOptUserId(opUserId);
        orderRefund.setRealRefundMoney(realRefundMoney);
        orderRefund.setRealChannel("balance");
        orderRefund.setRealRefundCoin(realRefundMoney.multiply(chainConfig.getMoneyRatio()));
        dao.update(orderRefund);
        saveRefundLog(refundCode, logTitle
                , BizMessageSource.getInstance().getMessage("cem50016") + realRefundMoney, logFrom, orderRefund.getTenantId(),opUserId);

        this.saveTradeFlow(orderRefund,order);
        order = new Order();
        order.setId(orderRefund.getOrderId());
        order.setStatus(OrderEnum.S_OS_CANCELED.value());
        order.setRefundState(OrderRefundState.S_ORS_REFUND_FINISH.value());
        order.setUpdated(DateFormatUtils.getNow());
        dao.update(order);
    }

    /**
     * 调用支付平台接口，发起微信退款
     * @param requestObj                请求对象
     * @param chainId                   总部id
     * @return                          是否调用成功
     */
    private RefundResponse callRefundApi(RefundWxRequest requestObj, Long chainId) {

        ResponseMessage response = null;
        boolean isSucc = false;
        RequestMessage request = new RequestMessage();
        request.setEndpoint(endpoint);
        request.setMethod(HttpMethod.POST);
        request.setResourcePath("/pay/v1/order/refund");

        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode("0");

        try
        {
            Map<String,String> headers = new HashMap<>();
            headers.put("x-access-token", PaasToken.build(chainId));
            headers.put("x-app-id", AuthConstants.PAAS_JWT_APPID);
            headers.put("x-mt-date", formatRfc822Date(new Date()));

            // 构造Body
            String body = JSON.toJSONString(requestObj);
            byte[] bodyByte = body.getBytes(StandardCharsets.UTF_8);
            request.setContent(new ByteArrayInputStream(bodyByte));
            request.setContentLength(bodyByte.length);
            headers.put("Content-Length", request.getContentLength() + "");
            headers.put("Content-Type", "application/json");

            request.setHeaders(headers);
            // 执行客户端请求
            ExecutionContext context = createDefaultContext(request.getMethod());
            response = send(request, context, true);
             toGeneralResponse(request, response, null);
            isSucc = true;

        } catch (BizCoreRuntimeException e) {
           refundResponse.setCode(e.getErrorCode());
            if (e.getErrorContents() != null) {
                refundResponse.setMsg(String.valueOf(e.getErrorContents()[0]));
            }
           return refundResponse;
        }  catch (Exception e) {
            log.error("{} failed {}{} response ={}", request.getMethod(), endpoint.toString(), request.getResourcePath(), e);
            refundResponse.setCode("1-");
            refundResponse.setMsg(BizMessageSource.getInstance().getMessage("cem50029"));
            return refundResponse;
        }
        finally {
            if (response != null) {
                SafeUtils.safeCloseResponse(response);
            }
        }
        return refundResponse;
    }

    @PostConstruct
    public void init() {
        String endpoint = sysConfig.getPaasServiceUrl();
        log.debug("paasServiceUrl host = {}", endpoint);
        super.init(endpoint, 3);
    }

    public static void main(String[] args) {
        try {
            throw new BizCoreRuntimeException(MemberErrorConstants.SAME_MEMBER_LEVEL_ONLY_ONE_TASK_TYPE);
        } catch (BizCoreRuntimeException e) {
            e.printStackTrace();
        }
    }
}