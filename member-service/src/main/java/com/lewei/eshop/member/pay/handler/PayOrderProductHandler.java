package com.lewei.eshop.member.pay.handler;

import com.lewei.eshop.entity.order.OrderProduct;
import com.lewei.eshop.member.pay.entity.PayCashOrderProduct;
import com.lewei.eshop.member.pay.entity.PayContext;
import com.lewei.eshop.member.pay.service.IPayHandler;
import com.xcrm.common.util.DateFormatUtils;
import com.xcrm.core.db.jdbc.BaseDaoSupport;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付订单商品处理
 * <AUTHOR>
 * @since 2020/10/27
 */
@Transactional
public class PayOrderProductHandler implements IPayHandler {

    @Autowired
    private BaseDaoSupport dao;

    @Override
    public void execute(PayContext context) {

        List<PayCashOrderProduct> payCashOrderProducts = context.getOrderProducts();
        List<OrderProduct> orderProducts = new ArrayList<>();
        OrderProduct orderProduct;
        for (PayCashOrderProduct payCashOrderProduct : payCashOrderProducts) {
            orderProduct = new OrderProduct();
            BeanUtils.copyProperties(payCashOrderProduct,orderProduct);
            orderProduct.setOrderId(context.getOrder().getId());
            orderProduct.setTenantId(context.getTenantId());
            orderProduct.setChainId(context.getChainId());
            orderProduct.setCreateBy(context.getOpUserId());
            orderProduct.setCreated(DateFormatUtils.getNow());
            orderProduct.setTotalPrice(payCashOrderProduct.getTotalPrice());
            orderProducts.add(orderProduct);
        }
        dao.batchSave(orderProducts,OrderProduct.class);
        context.setDbOrderProducts(orderProducts);
    }
}
